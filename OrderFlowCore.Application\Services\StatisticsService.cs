using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Helper;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Core.Models;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using static OrderFlowCore.Application.DTOs.SupervisorDelayStatisticsDto;
using static OrderFlowCore.Application.DTOs.PendingOrderDetails;
using static OrderFlowCore.Application.DTOs.SupervisorStatisticsDto;

namespace OrderFlowCore.Application.Services
{
    public class StatisticsService : IStatisticsService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<StatisticsService> _logger;



        // Define supervisor mappings as static readonly for better performance
        private static readonly Dictionary<string, string> SupervisorMappings = new()
        {
            ["SupervisorOfEmployeeServices"] = "خدمات الموظفين",
            ["SupervisorOfHumanResourcesPlanning"] = "إدارة تخطيط الموارد البشرية",
            ["SupervisorOfInformationTechnology"] = "إدارة تقنية المعلومات",
            ["SupervisorOfAttendance"] = "مراقبة الدوام",
            ["SupervisorOfMedicalRecords"] = "السجلات الطبية",
            ["SupervisorOfPayrollAndBenefits"] = "إدارة الرواتب والاستحقاقات",
            ["SupervisorOfLegalAndCompliance"] = "إدارة القانونية والالتزام",
            ["SupervisorOfHumanResourcesServices"] = "خدمات الموارد البشرية",
            ["SupervisorOfHousing"] = "إدارة الإسكان",
            ["SupervisorOfFiles"] = "قسم الملفات",
            ["SupervisorOfOutpatientClinics"] = "العيادات الخارجية",
            ["SupervisorOfSocialSecurity"] = "التأمينات الاجتماعية",
            ["SupervisorOfInventoryControl"] = "وحدة مراقبة المخزون",
            ["SupervisorOfRevenueDevelopment"] = "إدارة تنمية الإيرادات",
            ["SupervisorOfSecurity"] = "إدارة الأمن و السلامة",
            ["SupervisorOfMedicalConsultation"] = "الطب الاتصالي"
        };

        // Cache order status sets for better performance
        private static readonly HashSet<OrderStatus> PendingStatuses = new()
        {
            OrderStatus.DM, OrderStatus.A1, OrderStatus.A2, OrderStatus.A3,
            OrderStatus.A4, OrderStatus.B, OrderStatus.C, OrderStatus.D
        };

        private static readonly HashSet<OrderStatus> CancelledStatuses = new()
        {
            OrderStatus.CancelledByDepartmentManager,
            OrderStatus.CancelledByAssistantManager,
            OrderStatus.CancelledByCoordinator,
            OrderStatus.CancelledByManager
        };
        // النظام المتخصص لحساب الأزمنة
        private readonly SpecializedCompletionTimeCalculator _calculator;

        public StatisticsService(IUnitOfWork unitOfWork, ILogger<StatisticsService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _calculator = new SpecializedCompletionTimeCalculator(logger);
        }




        // ✅ 2. تحديث GetGeneralStatisticsAsync





        /// <summary>
        /// خدمة إحصائيات الأقسام النهائية - دقة عالية مع بساطة 🏢📊
        /// </summary>
        public async Task<ServiceResult<List<OrderDepartmentStatisticsDto>>> GetDepartmentStatisticsAsync()
        {
            try
            {
                var allOrders = (await _unitOfWork.Orders.GetAllAsync()).ToList();
                _logger.LogInformation("معالجة {OrderCount} طلب لإحصائيات الأقسام", allOrders.Count);

                var departmentStats = allOrders
                    .Where(o => !string.IsNullOrEmpty(o.Department))
                    .GroupBy(o => o.Department)
                    .Select(g => CreateDepartmentStatistics(g.Key, g.ToList()))
                    .Where(d => d.TotalOrders > 0)
                    .ToList();

                // استخدام دالة الترتيب المحدثة
                CalculateRankings(departmentStats);

                _logger.LogInformation("تم إنشاء إحصائيات لـ {DeptCount} قسم", departmentStats.Count);
                return ServiceResult<List<OrderDepartmentStatisticsDto>>.Success(departmentStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب إحصائيات الأقسام");
                return ServiceResult<List<OrderDepartmentStatisticsDto>>.Failure($"حدث خطأ: {ex.Message}");
            }
        }

        /// <summary>
        /// إنشاء إحصائيات قسم واحد - تحسين بسيط لقراءة الحالات المعلقة
        /// </summary>
        private OrderDepartmentStatisticsDto CreateDepartmentStatistics(string departmentName, List<Core.Models.OrdersTable> orders)
        {
            var stats = new OrderDepartmentStatisticsDto
            {
                DepartmentName = departmentName,
                TotalOrders = orders.Count
            };

            // تصنيف الطلبات حسب الحالة الفعلية
            foreach (var order in orders)
            {
                var orderStatus = GetSimpleOrderStatus(order);

                switch (orderStatus)
                {
                    case "مكتمل":
                        stats.CompletedOrders++;
                        break;
                    case "ملغي":
                        stats.CancelledOrders++;
                        break;
                    case "معلق":
                        stats.PendingOrders++;
                        break;
                }
            }

            // حساب متوسط الزمن
            var completedDeptOrders = orders.Where(o =>
                (o.ConfirmedByDepartmentManager ?? "").Contains("اعتماد بواسطة")).ToList();
            stats.AverageCompletionTime = _calculator.CalculateDepartmentAverageTime(completedDeptOrders);

            // الإحصائيات الزمنية المكتملة
            var now = DateTime.Now;
            var thisMonth = new DateTime(now.Year, now.Month, 1);
            var lastMonth = thisMonth.AddMonths(-1);  // ← مفقود
            var last3Months = now.AddMonths(-3);
            var last6Months = now.AddMonths(-6);
            var lastYear = now.AddYears(-1);

            stats.ThisMonthOrders = orders.Count(o => o.CreatedAt >= thisMonth);
            stats.LastMonthOrders = orders.Count(o => o.CreatedAt >= lastMonth && o.CreatedAt < thisMonth);  // ← مفقود
            stats.Last3MonthsOrders = orders.Count(o => o.CreatedAt >= last3Months);
            stats.Last6MonthsOrders = orders.Count(o => o.CreatedAt >= last6Months);
            stats.LastYearOrders = orders.Count(o => o.CreatedAt >= lastYear);

            // حساب اتجاه الأداء
            stats.TrendDirection = DetermineDepartmentTrend(stats.ThisMonthOrders, stats.Last3MonthsOrders, stats.LastYearOrders);
            return stats;
        }

        /// <summary>
        /// تحديد الحالة البسيطة للطلب - مُصحح للحالة DM
        /// </summary>
        private string GetSimpleOrderStatus(Core.Models.OrdersTable order)
        {
            var orderStatusString = order.OrderStatus.ToString();

            // أولاً: الطلبات المُعادة للقسم (معلقة في القسم)
            if (orderStatusString == "ReturnedByAssistantManager" ||
                orderStatusString == "ReturnedByCoordinator")
            {
                return "معلق";
            }

            // ثانياً: الحالة DM - الطلب في القسم دائماً معلق
            // حتى لو اعتمده مدير القسم، مازال في انتظار باقي الإجراءات
            if (orderStatusString == "DM")
            {
                // فحص إذا ألغاه مدير القسم
                var deptConfirmation = order.ConfirmedByDepartmentManager ?? "";
                if (deptConfirmation.Contains("تم الإلغاء بواسطة"))
                {
                    return "ملغي";
                }

                // حتى لو اعتمده مدير القسم، مازال معلق في القسم
                return "معلق";
            }

            // ثالثاً: الحالات الملغاة
            if (orderStatusString.Contains("Cancelled"))
            {
                return "ملغي";
            }

            // رابعاً: باقي الحالات - فحص مدير القسم
            var deptText = order.ConfirmedByDepartmentManager ?? "";
            if (deptText.Contains("اعتماد بواسطة"))
            {
                return "مكتمل";
            }
            if (deptText.Contains("تم الإلغاء بواسطة"))
            {
                return "ملغي";
            }

            // الافتراضي: معلق
            return "معلق";
        }
        private string DetermineDepartmentTrend(int thisMonthCount, int last3MonthsCount, int lastYearCount)
        {
            if (thisMonthCount == 0 && last3MonthsCount == 0)
                return "مستقر";

            var avgMonthlyInQuarter = last3MonthsCount / 3.0;
            var avgMonthlyInYear = lastYearCount / 12.0;

            if (thisMonthCount > avgMonthlyInQuarter * 1.3)
                return "متزايد";
            else if (thisMonthCount < avgMonthlyInYear * 0.7 && avgMonthlyInYear > 0)
                return "متناقص";
            else
                return "مستقر";
        }
        /// <summary>
        /// حساب الترتيب للأقسام
        /// </summary>
        private void CalculateRankings(List<OrderDepartmentStatisticsDto> departments)
        {
            var ranked = departments
                .OrderByDescending(d => d.CompletionPercentage)    // 1. نسبة الإنجاز أولاً
                .ThenBy(d => d.AverageCompletionTime >= 0 ? d.AverageCompletionTime : 999) // 2. السرعة المحدثة
                .ThenByDescending(d => d.CompletedOrders)          // 3. الكمية المنجزة
                .ThenByDescending(d => d.TotalOrders)              // 4. إجمالي الطلبات
                .ToList();

            for (int i = 0; i < ranked.Count; i++)
            {
                ranked[i].Ranking = i + 1;
            }

            departments.Clear();
            departments.AddRange(ranked);
        }
        // ✨ إحصائيات المشرفين المحسنة
        // 🔧 قائمة الحقول الموحدة (بدون تغيير)
        private static readonly string[] SupervisorFieldNames =
        {
    "SupervisorOfEmployeeServices",
    "SupervisorOfHumanResourcesPlanning",
    "SupervisorOfInformationTechnology",
    "SupervisorOfAttendance",
    "SupervisorOfMedicalRecords",
    "SupervisorOfPayrollAndBenefits",
    "SupervisorOfLegalAndCompliance",
    "SupervisorOfHumanResourcesServices",
    "SupervisorOfHousing",
    "SupervisorOfFiles",
    "SupervisorOfOutpatientClinics",
    "SupervisorOfSocialSecurity",
    "SupervisorOfInventoryControl",
    "SupervisorOfRevenueDevelopment",
    "SupervisorOfSecurity",
    "SupervisorOfMedicalConsultation"
};

        // ✅ دالة واحدة موحدة (إبقاء الاسم الأصلي)
        private static string GetSupervisorValue(Core.Models.OrdersTable order, string fieldName)
        {
            try
            {
                return fieldName switch
                {
                    "SupervisorOfEmployeeServices" => order.SupervisorOfEmployeeServices ?? string.Empty,
                    "SupervisorOfHumanResourcesPlanning" => order.SupervisorOfHumanResourcesPlanning ?? string.Empty,
                    "SupervisorOfInformationTechnology" => order.SupervisorOfInformationTechnology ?? string.Empty,
                    "SupervisorOfAttendance" => order.SupervisorOfAttendance ?? string.Empty,
                    "SupervisorOfMedicalRecords" => order.SupervisorOfMedicalRecords ?? string.Empty,
                    "SupervisorOfPayrollAndBenefits" => order.SupervisorOfPayrollAndBenefits ?? string.Empty,
                    "SupervisorOfLegalAndCompliance" => order.SupervisorOfLegalAndCompliance ?? string.Empty,
                    "SupervisorOfHumanResourcesServices" => order.SupervisorOfHumanResourcesServices ?? string.Empty,
                    "SupervisorOfHousing" => order.SupervisorOfHousing ?? string.Empty,
                    "SupervisorOfFiles" => order.SupervisorOfFiles ?? string.Empty,
                    "SupervisorOfOutpatientClinics" => order.SupervisorOfOutpatientClinics ?? string.Empty,
                    "SupervisorOfSocialSecurity" => order.SupervisorOfSocialSecurity ?? string.Empty,
                    "SupervisorOfInventoryControl" => order.SupervisorOfInventoryControl ?? string.Empty,
                    "SupervisorOfRevenueDevelopment" => order.SupervisorOfRevenueDevelopment ?? string.Empty,
                    "SupervisorOfSecurity" => order.SupervisorOfSecurity ?? string.Empty,
                    "SupervisorOfMedicalConsultation" => order.SupervisorOfMedicalConsultation ?? string.Empty,
                    _ => string.Empty
                };
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// حساب الإحصائيات الزمنية للمشرف - دالة جديدة
        /// </summary>
        private void CalculateSupervisorTimeStatistics(SupervisorStatisticsDto stats,
    List<Core.Models.OrdersTable> ordersWithSupervisor, string fieldName)
        {
            try
            {
                var now = DateTime.Now;

                // حساب بداية الفترات الزمنية المختلفة
                var startOfMonth = new DateTime(now.Year, now.Month, 1);
                var start3MonthsAgo = now.AddMonths(-3);
                var start6MonthsAgo = now.AddMonths(-6);
                var startOfYear = now.AddYears(-1);

                // عدد الطلبات التي تم التعامل معها في كل فترة
                foreach (var order in ordersWithSupervisor)
                {
                    var fieldValue = GetSupervisorValue(order, fieldName);
                    if (string.IsNullOrEmpty(fieldValue)) continue;

                    // فقط العمليات المنجزة (ليس تحت التنفيذ)
                    if (fieldValue.Contains("اعتماد") || fieldValue.Contains("تمت الإعادة") || fieldValue.Contains("طلب إجراء"))
                    {
                        var orderDate = order.CreatedAt;

                        // إحصائيات هذا الشهر
                        if (orderDate >= startOfMonth)
                        {
                            stats.ThisMonthOperations++;
                        }

                        // إحصائيات آخر 3 أشهر
                        if (orderDate >= start3MonthsAgo)
                        {
                            stats.Last3MonthsOperations++;
                        }

                        // إحصائيات آخر 6 أشهر
                        if (orderDate >= start6MonthsAgo)
                        {
                            stats.Last6MonthsOperations++;
                        }

                        // إحصائيات آخر سنة
                        if (orderDate >= startOfYear)
                        {
                            stats.LastYearOperations++;
                        }
                    }
                }

                // حساب الطلبات متعددة المراحل
                stats.MultiStageOrders = CountMultiStageOrders(ordersWithSupervisor, fieldName);

                // تحديد اتجاه الأداء
                stats.TrendDirection = DetermineSupervisorTrend(stats.ThisMonthOperations, stats.Last3MonthsOperations);

                _logger.LogInformation($"📅 {stats.SupervisorName}: شهر={stats.ThisMonthOperations}, 3أشهر={stats.Last3MonthsOperations}, اتجاه={stats.TrendDirection}");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, $"خطأ في حساب الإحصائيات الزمنية للمشرف: {stats.SupervisorName}");
            }
        }
        /// <summary>
        /// عد الطلبات متعددة المراحل للمشرف
        /// </summary>
        private int CountMultiStageOrders(List<Core.Models.OrdersTable> orders, string fieldName)
        {
            var multiStageCount = 0;

            foreach (var order in orders)
            {
                var fieldValue = GetSupervisorValue(order, fieldName);
                if (string.IsNullOrEmpty(fieldValue)) continue;

                // عد عدد العمليات في هذا الحقل للطلب الواحد
                var operationsCount = 0;
                if (fieldValue.Contains("اعتماد")) operationsCount++;
                if (fieldValue.Contains("تمت الإعادة")) operationsCount++;
                if (fieldValue.Contains("طلب إجراء")) operationsCount++;

                // إذا كان هناك أكثر من عملية واحدة
                if (operationsCount > 1)
                {
                    multiStageCount++;
                }
            }

            return multiStageCount;
        }
        /// <summary>
        /// تحديد اتجاه الأداء للمشرف
        /// </summary>
        private string DetermineSupervisorTrend(int thisMonthCount, int last3MonthsCount)
        {
            if (thisMonthCount == 0 && last3MonthsCount == 0)
                return "مستقر";

            // حساب متوسط شهري من آخر 3 أشهر
            var avgMonthlyIn3Months = last3MonthsCount / 3.0;

            if (thisMonthCount > avgMonthlyIn3Months * 1.3)
                return "متزايد";
            else if (thisMonthCount < avgMonthlyIn3Months * 0.7)
                return "متناقص";
            else
                return "مستقر";
        }
        /// <summary>
        /// عد الكلمات المفتاحية في مجموعة من النصوص
        /// </summary>
        private static int CountByKeyword(IEnumerable<string> values, string keyword)
        {
            return values.Count(value => !string.IsNullOrEmpty(value) && value.Contains(keyword));
        }
        /// <summary>
        /// حساب متوسط زمن الإنجاز للمشرفين
        /// </summary>
        private double CalculateSupervisorAverageTime(List<Core.Models.OrdersTable> orders, string fieldName)
        {
            try
            {
                var completedOrders = orders
                    .Where(o => GetSupervisorValue(o, fieldName).Contains("اعتماد"))
                    .ToList();

                if (!completedOrders.Any())
                    return 0;

                var validDurations = new List<double>();

                foreach (var order in completedOrders)
                {
                    try
                    {
                        var supervisorValue = GetSupervisorValue(order, fieldName);
                        var outDate = OrderHelper.ExtractOutDate(supervisorValue);

                        if (string.IsNullOrEmpty(outDate) || !DateTime.TryParse(outDate, out var completionDate))
                            continue;

                        var inDate = OrderHelper.ExtractInDate(supervisorValue);
                        var creationDate = DateTime.TryParse(inDate, out var parsedInDate) &&
                                       parsedInDate.Date != completionDate.Date
                            ? parsedInDate
                            : order.CreatedAt;

                        var businessDays = CalculateBusinessDays(creationDate, completionDate);
                        if (businessDays >= 0)
                        {
                            validDurations.Add(businessDays);
                        }
                    }
                    catch
                    {
                        continue;
                    }
                }

                return validDurations.Any() ? Math.Round(validDurations.Average(), 1) : 0;
            }
            catch
            {
                return 0;
            }
        }
        // ✨ 2. دالة حساب الترتيب المحسنة (إضافة للكلاس الأساسي)
        /// <summary>
        /// حساب ترتيب المشرفين بناءً على معايير محسنة
        /// </summary>
        private void CalculateSupervisorRankings(List<SupervisorStatisticsDto> supervisors)
        {
            try
            {
                _logger.LogInformation($"🏆 بدء حساب ترتيب {supervisors.Count} مشرف...");

                // ترتيب المشرفين حسب معايير الـ DTO الأساسي
                var rankedSupervisors = supervisors
                    .OrderByDescending(s => s.CompletionRate)                        // 1. نسبة الإنجاز
                    .ThenByDescending(s => s.CompletedPercentage)                    // 2. نسبة الإنجاز المكتمل
                    .ThenBy(s => s.AverageCompletionTime > 0 ? s.AverageCompletionTime : 999) // 3. السرعة
                    .ThenByDescending(s => s.TotalCompletedWork)                     // 4. إجمالي الأعمال المنجزة
                    .ThenByDescending(s => s.ThisMonthOperations)                    // 5. النشاط الحالي
                    .ThenBy(s => s.SupervisorName)                                   // 6. الترتيب الأبجدي للتجانس
                    .ToList();

                // تطبيق الترتيب
                for (int i = 0; i < rankedSupervisors.Count; i++)
                {
                    rankedSupervisors[i].Ranking = i + 1;

                    _logger.LogInformation($"📍 ترتيب #{i + 1}: {rankedSupervisors[i].SupervisorName} " +
                        $"(إنجاز: {rankedSupervisors[i].CompletionRate:F1}%, " +
                        $"مكتمل: {rankedSupervisors[i].CompletedPercentage:F1}%, " +
                        $"متوسط وقت: {rankedSupervisors[i].AverageCompletionTime:F1})");
                }

                // تحديث القائمة الأصلية
                supervisors.Clear();
                supervisors.AddRange(rankedSupervisors);

                _logger.LogInformation($"✅ تم ترتيب {supervisors.Count} مشرف بنجاح");

                // تحليل إضافي اختياري
                AnalyzeSupervisorPerformance(supervisors);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في حساب ترتيب المشرفين");
            }
        }



        /// <summary>
        /// تحليل تفصيلي لأداء المشرفين
        /// </summary>
        private void AnalyzeSupervisorPerformance(List<SupervisorStatisticsDto> supervisors)
        {
            if (!supervisors.Any()) return;

            try
            {
                var topPerformers = supervisors.Where(s => s.IsTopPerformer).ToList();
                var highPerformers = supervisors.Where(s => s.IsHighPerformer).ToList();
                var needsImprovement = supervisors.Where(s => s.CompletionRate < 40).ToList();

                _logger.LogInformation($"📊 تحليل الأداء: متميزون={topPerformers.Count}, " +
                    $"عالي الأداء={highPerformers.Count}, يحتاج تحسين={needsImprovement.Count}");

                // عرض أفضل 3 مشرفين
                var top3 = supervisors.Take(3).ToList();
                for (int i = 0; i < top3.Count; i++)
                {
                    var supervisor = top3[i];
                    var medal = i == 0 ? "🥇" : i == 1 ? "🥈" : "🥉";
                    _logger.LogInformation($"{medal} المركز {i + 1}: {supervisor.SupervisorName} " +
                        $"(إنجاز: {supervisor.CompletionRate:F1}%, مستوى: {supervisor.PerformanceLevel})");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "تحذير في تحليل أداء المشرفين");
            }
        }
        // ===================================================================
        // 🎯 المرحلة الأولى: الإحصائيات الأساسية)
        // ===================================================================

        /// <summary>
        /// GetSupervisorStatisticsAsync المُحدث - مع دمج بيانات الطلبات المعلقة)
        /// </summary>
        public async Task<ServiceResult<List<SupervisorStatisticsDto>>> GetSupervisorStatisticsAsync()
        {
            try
            {
                var allOrders = (await _unitOfWork.Orders.GetAllAsync()).ToList();
                var supervisorStats = new List<SupervisorStatisticsDto>();

                _logger.LogInformation("📊 بدء حساب إحصائيات {Count} مشرف", SupervisorMappings.Count);

                foreach (var (fieldName, supervisorName) in SupervisorMappings)
                {
                    var ordersWithSupervisor = allOrders
                        .Where(o => !string.IsNullOrEmpty(GetSupervisorValue(o, fieldName)))
                        .ToList();

                    if (!ordersWithSupervisor.Any()) continue;

                    var stats = new SupervisorStatisticsDto { SupervisorName = supervisorName };

                    // حساب الإحصائيات الأساسية (استخدام الدالة المحسنة)
                    CalculateBasicStatisticsFixed(stats, ordersWithSupervisor, fieldName);

                    // حساب الإحصائيات الزمنية (استخدام الدالة الموجودة)
                    CalculateSupervisorTimeStatistics(stats, ordersWithSupervisor, fieldName);

                    // دمج حساب الطلبات المعلقة
                    IntegratePendingOrdersData(stats, ordersWithSupervisor, fieldName);

                    supervisorStats.Add(stats);
                }

                // حساب الترتيب (استخدام الدالة الموجودة)
                CalculateSupervisorRankings(supervisorStats);

                _logger.LogInformation("✅ تم حساب إحصائيات {Count} مشرف بنجاح", supervisorStats.Count);
                return ServiceResult<List<SupervisorStatisticsDto>>.Success(supervisorStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب إحصائيات المشرفين");
                return ServiceResult<List<SupervisorStatisticsDto>>.Failure($"حدث خطأ: {ex.Message}");
            }
        }


        /// <summary>
        /// حساب الإحصائيات الأساسية (بديل للدالة المفقودة)
        /// </summary>
        private void CalculateBasicStatisticsFixed(SupervisorStatisticsDto stats, List<OrdersTable> orders, string fieldName)
        {
            try
            {
                var supervisorValues = orders.Select(o => GetSupervisorValue(o, fieldName)).ToList();

                stats.UnderExecution = CountByKeyword(supervisorValues, "الطلب قيد التنفيذ");
                stats.Completed = CountByKeyword(supervisorValues, "اعتماد");
                stats.NeedsAction = CountByKeyword(supervisorValues, "طلب إجراء");
                stats.Returned = CountByKeyword(supervisorValues, "تمت الإعادة");
                stats.AverageCompletionTime = CalculateSupervisorAverageTime(orders, fieldName);

                _logger.LogInformation("📊 {SupervisorName}: تحت التنفيذ={UnderExecution}, مكتمل={Completed}, إجراءات={NeedsAction}, معاد={Returned}",
                    stats.SupervisorName, stats.UnderExecution, stats.Completed, stats.NeedsAction, stats.Returned);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب الإحصائيات الأساسية للمشرف: {SupervisorName}", stats.SupervisorName);
            }
        }

        /// <summary>
        /// دمج بيانات الطلبات المعلقة في SupervisorStatisticsDto (الدالة الرئيسية)
        /// </summary>
        private void IntegratePendingOrdersData(SupervisorStatisticsDto stats, List<OrdersTable> orders, string fieldName)
        {
            try
            {
                var pendingOrders = new List<PendingOrderDetails>();

                _logger.LogInformation("🔍 دمج بيانات الطلبات المعلقة للمشرف: {SupervisorName}", stats.SupervisorName);

                foreach (var order in orders)
                {
                    var fieldValue = GetSupervisorValue(order, fieldName);
                    if (string.IsNullOrEmpty(fieldValue)) continue;

                    // فقط الطلبات المعلقة
                    if (fieldValue.Contains("الطلب قيد التنفيذ") || fieldValue.Contains("طلب إجراء"))
                    {
                        _logger.LogInformation("📋 طلب معلق {OrderId}: {FieldValue}", order.Id, fieldValue);

                        var startDate = ExtractProcessingStartDateAdvanced(fieldValue, order.CreatedAt);
                        var pendingDays = (int)CalculateBusinessDays(startDate, DateTime.Now);

                        var pendingOrder = new PendingOrderDetails
                        {
                            OrderId = order.Id,
                            OrderNumber = $"ORD-{order.Id}",
                            EmployeeName = order.EmployeeName ?? "غير محدد",
                            CreatedDate = order.CreatedAt,
                            ProcessingStartDate = startDate,
                            PendingDays = Math.Max(0, pendingDays),
                            Status = fieldValue.Contains("طلب إجراء") ? "يحتاج إجراء" : "قيد التنفيذ",
                            Department = order.Department ?? "غير محدد",
                            OrderType = order.OrderType ?? "غير محدد",
                            IsCritical = pendingDays > 3,
                            IsOverdue = pendingDays > 5,
                            IsUrgent = pendingDays > 7
                        };

                        pendingOrders.Add(pendingOrder);

                        _logger.LogInformation("✅ دُمج طلب معلق {OrderId}: {PendingDays} يوم، الحالة: {Priority}",
                            pendingOrder.OrderId, pendingOrder.PendingDays,
                            pendingOrder.IsUrgent ? "عاجل" : pendingOrder.IsOverdue ? "متأخر" : pendingOrder.IsCritical ? "حرج" : "عادي");
                    }
                }

                // ملء البيانات في SupervisorStatisticsDto
                stats.PendingOrdersList = pendingOrders.OrderByDescending(p => p.PendingDays).ToList();
                stats.AveragePendingDays = pendingOrders.Any()
                    ? Math.Round(pendingOrders.Average(p => (double)p.PendingDays), 1)
                    : 0;
                stats.LongPendingCount = pendingOrders.Count(p => p.PendingDays > 3);

                _logger.LogInformation("🎯 نتيجة الدمج - {SupervisorName}: {PendingCount} طلب معلق، متوسط {Average:F1} يوم، حرجة: {LongCount}",
                    stats.SupervisorName, pendingOrders.Count, stats.AveragePendingDays, stats.LongPendingCount);

                // التأكد من التطابق
                if (stats.UnderExecution != pendingOrders.Count)
                {
                    _logger.LogWarning("⚠️ تضارب في البيانات - {SupervisorName}: UnderExecution={UnderExecution}, PendingCount={PendingCount}",
                        stats.SupervisorName, stats.UnderExecution, pendingOrders.Count);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في دمج بيانات الطلبات المعلقة للمشرف: {SupervisorName}", stats.SupervisorName);

                // قيم افتراضية في حالة الخطأ
                stats.PendingOrdersList = new List<PendingOrderDetails>();
                stats.AveragePendingDays = 0;
                stats.LongPendingCount = 0;
            }
        }
        // 🎯 نهاية المرحلة الأولى: الإحصائيات الأساسية)



        // ===================================================================
        // 🎯 المرحلة الثانية الجزء الاول: الطلبات المعلقة)
        // ===================================================================

        /// <summary>
        /// GetPendingOrdersStatisticsAsync - مُبسط ليستخدم البيانات المدمجة )
        /// </summary>
        public async Task<ServiceResult<List<PendingOrdersStatisticsDto>>> GetPendingOrdersStatisticsAsync()
        {
            try
            {
                var allOrders = (await _unitOfWork.Orders.GetAllAsync()).ToList();
                var pendingStats = new List<PendingOrdersStatisticsDto>();

                _logger.LogInformation($"🔍 بدء تحليل الطلبات المعلقة لـ {allOrders.Count} طلب");

                // التحقق من وجود البيانات
                if (allOrders == null || !allOrders.Any())
                {
                    _logger.LogWarning("⚠️ لا توجد طلبات للتحليل");
                    return ServiceResult<List<PendingOrdersStatisticsDto>>.Success(new List<PendingOrdersStatisticsDto>());
                }

                foreach (var kvp in SupervisorMappings)
                {
                    var fieldName = kvp.Key;
                    var supervisorName = kvp.Value;

                    try
                    {
                        var pendingStat = AnalyzePendingOrders(supervisorName, allOrders, fieldName);
                        pendingStats.Add(pendingStat);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"❌ خطأ في تحليل الطلبات المعلقة للمشرف: {supervisorName}");
                        pendingStats.Add(new PendingOrdersStatisticsDto { SupervisorName = supervisorName });
                    }
                }

                _logger.LogInformation($"✅ انتهى تحليل الطلبات المعلقة لـ {pendingStats.Count} مشرف");
                return ServiceResult<List<PendingOrdersStatisticsDto>>.Success(pendingStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في تحليل الطلبات المعلقة");
                return ServiceResult<List<PendingOrdersStatisticsDto>>.Failure($"حدث خطأ: {ex.Message}");
            }
        }
        /// <summary>
        /// تحليل الطلبات المعلقة لمشرف واحد
        /// </summary>
        /// <summary>
        /// تحليل الطلبات المعلقة لمشرف واحد
        /// </summary>
        private PendingOrdersStatisticsDto AnalyzePendingOrders(
    string supervisorName,
    List<Core.Models.OrdersTable> allOrders,
    string fieldName)
        {
            var pendingStat = new PendingOrdersStatisticsDto
            {
                SupervisorName = supervisorName
            };

            try
            {
                // فصل الطلبات إلى فئتين بدقة
                var (pendingOrders, actionRequiredOrders) = GetSeparatedOrders(allOrders, fieldName);

                // ✅ التأكد من عدم وجود تداخل بين الفئتين
                var pendingOrderDetails = new List<PendingOrderDetails>();
                var actionRequiredOrderDetails = new List<PendingOrderDetails>();

                // معالجة الطلبات المعلقة العادية
                foreach (var order in pendingOrders)
                {
                    var detail = CreatePendingOrderDetails(order, fieldName, "معلق");
                    if (detail != null)
                    {
                        pendingOrderDetails.Add(detail);
                    }
                }

                // معالجة الطلبات التي تتطلب إجراءات (بدون تداخل)
                foreach (var order in actionRequiredOrders)
                {
                    // تأكد أن الطلب ليس موجود في القائمة الأولى
                    if (!pendingOrderDetails.Any(p => p.OrderId == order.Id))
                    {
                        var detail = CreatePendingOrderDetails(order, fieldName, "يتطلب إجراء");
                        if (detail != null)
                        {
                            actionRequiredOrderDetails.Add(detail);
                        }
                    }
                }

                // دمج القوائم
                var allOrderDetails = new List<PendingOrderDetails>();
                allOrderDetails.AddRange(pendingOrderDetails);
                allOrderDetails.AddRange(actionRequiredOrderDetails);

                // تعيين قائمة الطلبات
                pendingStat.PendingOrdersList = allOrderDetails;

                // حساب الإحصائيات
                pendingStat.RegularPendingCount = pendingOrderDetails.Count;
                pendingStat.ActionRequiredCount = actionRequiredOrderDetails.Count;

                if (allOrderDetails.Any())
                {
                    pendingStat.AveragePendingDays = allOrderDetails.Average(p => p.PendingDays);

                    if (pendingOrderDetails.Any())
                        pendingStat.RegularPendingAverage = pendingOrderDetails.Average(p => p.PendingDays);

                    if (actionRequiredOrderDetails.Any())
                        pendingStat.ActionRequiredAverage = actionRequiredOrderDetails.Average(p => p.PendingDays);

                    pendingStat.LongPendingCount = allOrderDetails.Count(p => p.PendingDays > 5);

                    // تحديد خصائص الطلبات
                    foreach (var order in allOrderDetails)
                    {
                        if (order.PendingDays > 10)
                            order.IsUrgent = true;
                        else if (order.PendingDays > 7)
                            order.IsOverdue = true;
                        else if (order.PendingDays > 5)
                            order.IsCritical = true;
                    }
                }

                _logger.LogInformation($"📊 {supervisorName}: {pendingStat.RegularPendingCount} معلق، {pendingStat.ActionRequiredCount} يتطلب إجراء، إجمالي: {pendingStat.TotalPendingOrders}");

                return pendingStat;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ خطأ في تحليل الطلبات المعلقة للمشرف: {supervisorName}");
                return pendingStat;
            }
        }

        /// <summary>
        /// فصل الطلبات إلى معلقة ويتطلب إجراءات
        /// </summary>
        private (List<Core.Models.OrdersTable> PendingOrders, List<Core.Models.OrdersTable> ActionRequiredOrders)
      GetSeparatedOrders(List<Core.Models.OrdersTable> allOrders, string fieldName)
        {
            var pendingOrders = new List<Core.Models.OrdersTable>();
            var actionRequiredOrders = new List<Core.Models.OrdersTable>();

            foreach (var order in allOrders)
            {
                var value = GetSupervisorValue(order, fieldName);

                if (value.Contains("الطلب قيد التنفيذ"))
                {
                    pendingOrders.Add(order);
                }
                else if (value.Contains("طلب إجراء من المشرف"))
                {
                    actionRequiredOrders.Add(order);
                }
            }

            return (pendingOrders, actionRequiredOrders);
        }
        /// <summary>
        /// إنشاء تفاصيل الطلب المعلق
        /// </summary>
        private PendingOrderDetails CreatePendingOrderDetails(
     Core.Models.OrdersTable order,
     string fieldName,
     string orderType)
        {
            try
            {
                var supervisorValue = GetSupervisorValue(order, fieldName);
                var processingStartDate = ExtractProcessingStartDate(supervisorValue, order.CreatedAt);
                var pendingDays = CalculateBusinessDays(processingStartDate, DateTime.Now);

                // تحديد الحالة بناءً على النوع
                string status = orderType switch
                {
                    "معلق" => "الطلب قيد التنفيذ",
                    "يتطلب إجراء" => ExtractActionRequiredDetails(supervisorValue),
                    _ => "غير محدد"
                };

                return new PendingOrderDetails
                {
                    OrderId = order.Id,
                    OrderNumber = order.Id.ToString(),
                    EmployeeName = order.EmployeeName ?? "",
                    CreatedDate = order.CreatedAt,
                    ProcessingStartDate = processingStartDate,
                    PendingDays = (int)pendingDays,
                    Status = status,
                    Department = order.Department ?? "",
                    OrderType = order.OrderType ?? "",
                    OrderCategory = orderType  // ✅ إضافة فئة الطلب
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ خطأ في إنشاء تفاصيل الطلب المعلق: {order.Id}");
                return null;
            }
        }

        /// <summary>
        /// استخراج تفاصيل الطلبات التي تتطلب إجراءات
        /// </summary>
        private string ExtractActionRequiredDetails(string supervisorValue)
        {
            try
            {
                // البحث عن تفاصيل طلب الإجراء
                if (supervisorValue.Contains("طلب إجراء من المشرف:"))
                {
                    // استخراج اسم المطلوب منه الإجراء والتاريخ
                    var actionDetails = supervisorValue.Substring(
                        supervisorValue.IndexOf("طلب إجراء من المشرف:"));

                    return actionDetails.Split('|')[0].Trim();
                }

                return "يتطلب إجراء من المشرف";
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في استخراج تفاصيل طلب الإجراء");
                return "يتطلب إجراء من المشرف";
            }
        }


        // ===================================================================
        // 🎯 المرحلة الثانية الجزء الثاني: الطلبات المعلقة)
        // ===================================================================
        /// <summary>
        /// خدمة موحدة للطلبات المعلقة (مشرفين + أقسام) 🚀
        /// </summary>
        public async Task<ServiceResult<List<UnifiedPendingStatisticsDto>>> GetUnifiedPendingStatisticsAsync()
        {
            try
            {
                var allOrders = (await _unitOfWork.Orders.GetAllAsync()).ToList();
                var unifiedStats = new List<UnifiedPendingStatisticsDto>();

                _logger.LogInformation("🔍 بدء تحليل الطلبات المعلقة الموحد الشامل");

                // 1️⃣ المشرفين
                foreach (var kvp in SupervisorMappings)
                {
                    var supervisorStat = AnalyzePendingOrders(kvp.Value, allOrders, kvp.Key);
                    if (supervisorStat.PendingOrdersList.Any())
                    {
                        unifiedStats.Add(new UnifiedPendingStatisticsDto
                        {
                            EntityName = supervisorStat.SupervisorName,
                            EntityType = "Supervisor",
                            PendingOrdersList = supervisorStat.PendingOrdersList,
                            AveragePendingDays = supervisorStat.AveragePendingDays,
                            LongPendingCount = supervisorStat.LongPendingCount,
                            EfficiencyScore = supervisorStat.EfficiencyScore
                        });
                    }
                }

                // 2️⃣ الأقسام
                var departmentGroups = allOrders
                    .Where(o => !string.IsNullOrEmpty(o.Department))
                    .GroupBy(o => o.Department);

                foreach (var deptGroup in departmentGroups)
                {
                    var deptStat = AnalyzeDepartmentPending(deptGroup.Key, deptGroup.ToList());
                    if (deptStat.TotalPendingOrders > 0)
                    {
                        deptStat.EntityType = "Department";
                        unifiedStats.Add(deptStat);
                    }
                }

                // 3️⃣ المساعدين
                var assistantManagers = new Dictionary<string, string>
        {
            { "A1", "مساعد المدير للخدمات الطبية" },
            { "A2", "مساعد المدير لخدمات التمريض" },
            { "A3", "مساعد المدير للخدمات الإدارية" },
            { "A4", "مساعد المدير للموارد البشرية" }
        };

                foreach (var assistant in assistantManagers)
                {
                    var assistantStat = AnalyzeAssistantPending(assistant.Key, assistant.Value, allOrders);
                    if (assistantStat.TotalPendingOrders > 0)
                    {
                        assistantStat.EntityType = "Assistant";
                        unifiedStats.Add(assistantStat);
                    }
                }

                // 4️⃣ المنسق
                var coordinatorStat = AnalyzeCoordinatorPending(allOrders);
                if (coordinatorStat != null && coordinatorStat.TotalPendingOrders > 0)
                {
                    coordinatorStat.EntityType = "Coordinator";
                    unifiedStats.Add(coordinatorStat);
                }

                // 5️⃣ ✅ المدير - جديد
                var managerStat = AnalyzeManagerPending(allOrders);
                if (managerStat != null && managerStat.TotalPendingOrders > 0)
                {
                    managerStat.EntityType = "Manager";
                    unifiedStats.Add(managerStat);
                }

                // 6️⃣ حساب الأولويات
                CalculateUnifiedPriorities(unifiedStats);

                _logger.LogInformation($"✅ تم تحليل {unifiedStats.Count} كيان بطلبات معلقة");
                return ServiceResult<List<UnifiedPendingStatisticsDto>>.Success(unifiedStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في التحليل الموحد");
                return ServiceResult<List<UnifiedPendingStatisticsDto>>.Failure($"حدث خطأ: {ex.Message}");
            }
        }

        /// <summary>
        /// تحليل الطلبات المعلقة لمدير الموارد البشرية 👨‍💼
        /// </summary>
        private UnifiedPendingStatisticsDto AnalyzeManagerPending(List<Core.Models.OrdersTable> allOrders)
        {
            var stat = new UnifiedPendingStatisticsDto
            {
                EntityName = "مدير الموارد البشرية",
                EntityType = "Manager"  // ✅ إضافة نوع الكيان
            };

            // الطلبات المعلقة عند المدير - نفس المنطق الأصلي
            var pendingOrders = allOrders.Where(o => IsManagerPending(o)).ToList();

            var regularPendingDetails = new List<PendingOrderDetails>();

            foreach (var order in pendingOrders)
            {
                var detail = new PendingOrderDetails
                {
                    OrderId = order.Id,
                    OrderNumber = order.Id.ToString(),
                    EmployeeName = order.EmployeeName ?? "",
                    CreatedDate = order.CreatedAt,
                    Department = order.Department ?? "",
                    OrderType = order.OrderType ?? "",
                    Status = "في انتظار المدير",
                    OrderCategory = "معلق"  // ✅ إضافة فئة الطلب
                };

                // حساب أيام الانتظار - نفس المنطق الأصلي
                detail.ProcessingStartDate = ExtractManagerProcessingDate(order);
                detail.PendingDays = (int)CalculateBusinessDays(detail.ProcessingStartDate, DateTime.Now);

                // تحديد مستوى الأولوية - نفس المنطق الأصلي
                if (detail.PendingDays > 2) detail.IsUrgent = true;
                else if (detail.PendingDays > 1) detail.IsOverdue = true;
                else if (detail.PendingDays >= 1) detail.IsCritical = true;

                regularPendingDetails.Add(detail);
            }

            stat.PendingOrdersList = regularPendingDetails;

            if (regularPendingDetails.Any())
            {
                // ✅ حساب الإحصائيات
                stat.RegularPendingCount = regularPendingDetails.Count;
                stat.ReturnedCount = 0;  // المدير عادة لا يحتوي على طلبات معادة

                stat.AveragePendingDays = regularPendingDetails.Average(p => p.PendingDays);
                stat.RegularPendingAverage = regularPendingDetails.Average(p => p.PendingDays);
                stat.LongPendingCount = regularPendingDetails.Count(p => p.PendingDays > 2);

                _logger.LogInformation($"📊 المدير: {stat.RegularPendingCount} معلق، إجمالي: {stat.TotalPendingOrders}");

                return stat;
            }

            return null;
        }

        /// <summary>
        /// فحص هل الطلب معلق عند المدير
        /// </summary>
        private bool IsManagerPending(Core.Models.OrdersTable order)
        {
            // الطلب في مرحلة المدير
            if (order.OrderStatus == OrderStatus.D)
            {
                var managerField = order.HumanResourcesManager ?? "";

                // إذا لم يكن هناك إجراء = معلق
                if (string.IsNullOrWhiteSpace(managerField) ||
                    managerField.Contains("تم التحويل لمدير الموارد البشرية"))
                {
                    return true;
                }

                // إذا لم يتم الاعتماد أو الإلغاء أو الإعادة = معلق
                if (!managerField.Contains("اعتماد بواسطة") &&
                    !managerField.Contains("تم الإلغاء بواسطة") &&
                    !managerField.Contains("تم الإعادة بواسطة"))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// استخراج تاريخ وصول الطلب للمدير
        /// </summary>
        private DateTime ExtractManagerProcessingDate(Core.Models.OrdersTable order)
        {
            var managerField = order.HumanResourcesManager ?? "";

            if (managerField.Contains("تم التحويل لمدير الموارد البشرية"))
            {
                var date = ExtractProcessingStartDate(managerField, order.CreatedAt);
                if (date != order.CreatedAt) return date;
            }

            // البحث في حقل المنسق أيضاً
            var coordinatorField = order.ConfirmedByCoordinator ?? "";
            if (coordinatorField.Contains("تم التحويل مباشر للمدير"))
            {
                var date = ExtractProcessingStartDate(coordinatorField, order.CreatedAt);
                if (date != order.CreatedAt) return date;
            }

            return order.CreatedAt;
        }
        /// <summary>
        /// تحليل الطلبات المعلقة للمنسق 📋
        /// </summary>
        private UnifiedPendingStatisticsDto AnalyzeCoordinatorPending(List<Core.Models.OrdersTable> allOrders)
        {
            var stat = new UnifiedPendingStatisticsDto
            {
                EntityName = "منسق الموارد البشرية",
                EntityType = "Coordinator"  // ✅ إضافة نوع الكيان
            };

            // الطلبات المعلقة عند المنسق (تشمل المعادة) - نفس المنطق الأصلي
            var pendingOrders = allOrders.Where(o => IsCoordinatorPending(o)).ToList();

            _logger.LogInformation($"📋 المنسق: {pendingOrders.Count} طلب معلق (يشمل المعادة)");

            // فصل الطلبات حسب النوع
            var regularPendingDetails = new List<PendingOrderDetails>();
            var returnedOrderDetails = new List<PendingOrderDetails>();

            foreach (var order in pendingOrders)
            {
                var orderStatus = order.OrderStatus.ToString();

                // تحديد نوع الطلب وحالة العرض - نفس المنطق الأصلي
                var statusDisplay = orderStatus switch
                {
                    "B" => "في انتظار المنسق",
                    var s when s.Contains("ReturnedByManager") => "معاد من المدير",
                    var s when s.Contains("ReturnedBySupervisor") => "معاد من المشرف",
                    var s when s.Contains("ActionRequired") => "يتطلب إجراء",
                    _ => "في انتظار المنسق"
                };

                // ✅ تحديد فئة الطلب للعرض في HTML
                string orderCategory;
                if (statusDisplay.Contains("معاد"))
                {
                    orderCategory = "معاد";
                }
                else if (statusDisplay.Contains("يتطلب إجراء"))
                {
                    orderCategory = "يتطلب إجراء";
                }
                else
                {
                    orderCategory = "معلق";
                }

                var detail = new PendingOrderDetails
                {
                    OrderId = order.Id,
                    OrderNumber = order.Id.ToString(),
                    EmployeeName = order.EmployeeName ?? "",
                    CreatedDate = order.CreatedAt,
                    Department = order.Department ?? "",
                    OrderType = order.OrderType ?? "",
                    Status = statusDisplay,
                    OrderCategory = orderCategory  // ✅ إضافة فئة الطلب
                };

                // حساب أيام الانتظار - نفس المنطق الأصلي
                detail.ProcessingStartDate = ExtractCoordinatorProcessingDate(order);
                detail.PendingDays = (int)CalculateBusinessDays(detail.ProcessingStartDate, DateTime.Now);

                // تحديد مستوى الأولوية - نفس المنطق الأصلي
                if (detail.PendingDays > 3) detail.IsUrgent = true;
                else if (detail.PendingDays > 2) detail.IsOverdue = true;
                else if (detail.PendingDays > 1) detail.IsCritical = true;

                // إضافة للقائمة المناسبة
                if (orderCategory == "معاد")
                {
                    returnedOrderDetails.Add(detail);
                }
                else
                {
                    regularPendingDetails.Add(detail);
                }
            }

            // دمج القوائم
            var allOrderDetails = new List<PendingOrderDetails>();
            allOrderDetails.AddRange(regularPendingDetails);
            allOrderDetails.AddRange(returnedOrderDetails);

            stat.PendingOrdersList = allOrderDetails;

            if (allOrderDetails.Any())
            {
                // ✅ حساب الإحصائيات المنفصلة
                stat.RegularPendingCount = regularPendingDetails.Count;
                stat.ReturnedCount = returnedOrderDetails.Count;

                // حساب المتوسطات
                stat.AveragePendingDays = allOrderDetails.Average(p => p.PendingDays);

                if (regularPendingDetails.Any())
                    stat.RegularPendingAverage = regularPendingDetails.Average(p => p.PendingDays);

                if (returnedOrderDetails.Any())
                    stat.ReturnedAverage = returnedOrderDetails.Average(p => p.PendingDays);

                stat.LongPendingCount = allOrderDetails.Count(p => p.PendingDays > 2);

                _logger.LogInformation($"📊 المنسق: {stat.RegularPendingCount} معلق، {stat.ReturnedCount} معاد، إجمالي: {stat.TotalPendingOrders}");

                return stat;
            }

            return null;
        }



        /// <summary>
        /// فحص هل الطلب معلق عند المنسق
        /// </summary>

        private bool IsCoordinatorPending(Core.Models.OrdersTable order)
        {
            try
            {
                // التحقق من صحة البيانات
                if (order?.OrderStatus == null)
                {
                    _logger.LogWarning("⚠️ Order or OrderStatus is null for order {OrderId}", order?.Id);
                    return false;
                }

                string orderStatus = order.OrderStatus.ToString();
                string coordinatorField = order.ConfirmedByCoordinator ?? string.Empty;

                // استخدام switch expression للحصول على أداء أفضل
                return orderStatus switch
                {
                    // 1️⃣ الطلبات في مرحلة المنسق العادية
                    "B" => IsCoordinatorStillPending(coordinatorField),

                    // 2️⃣ الطلبات التي تحتاج إجراء من المنسق
                    "ActionRequired" => true,

                    // 3️⃣ الطلبات المعادة (تُعتبر معلقة دائماً)
                    var status when status.Contains("ReturnedByManager") => true,
                    var status when status.Contains("ReturnedBySupervisor") => true,

                    // ❌ أي حالات أخرى (مثل ActionRequiredBySupervisor)
                    _ => false
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error in IsCoordinatorPending for order {OrderId}", order?.Id);
                return false;
            }
        }

        /// <summary>
        /// تحقق تفصيلي لحالة B - هل لا يزال الطلب معلقاً عند المنسق؟
        /// </summary>
        private bool IsCoordinatorStillPending(string coordinatorField)
        {
            try
            {
                // إذا كان الحقل فارغاً = معلق
                if (string.IsNullOrWhiteSpace(coordinatorField))
                    return true;

                // إذا تم التحويل للمنسق فقط = معلق
                if (coordinatorField.Contains("تم التحويل للمنسق"))
                    return true;

                // إذا طُلب إجراء من المنسق ولم يتم الاعتماد = معلق
                if (coordinatorField.Contains("طلب إجراء من المنسق") &&
                    !coordinatorField.Contains("اعتماد بواسطة"))
                    return true;

                // في جميع الحالات الأخرى = غير معلق
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error in IsCoordinatorStillPending");
                // في حالة الخطأ، نفترض أنه معلق للأمان
                return true;
            }
        }



        /// <summary>
        /// استخراج تاريخ وصول الطلب للمنسق
        /// </summary>
        private DateTime ExtractCoordinatorProcessingDate(Core.Models.OrdersTable order)
        {
            var coordinatorField = order.ConfirmedByCoordinator ?? "";
            var orderStatus = order.OrderStatus.ToString();

            // للطلبات المعادة، نبحث عن آخر تاريخ إعادة
            if (orderStatus.Contains("ReturnedByManager"))
            {
                var managerField = order.HumanResourcesManager ?? "";
                if (managerField.Contains("تم الإعادة بواسطة"))
                {
                    var date = ExtractProcessingStartDate(managerField, order.CreatedAt);
                    if (date != order.CreatedAt) return date;
                }
            }

            if (orderStatus.Contains("ReturnedBySupervisor"))
            {
                // نبحث في حقول المشرفين عن آخر تاريخ إعادة
                foreach (var kvp in SupervisorMappings)
                {
                    var supervisorField = GetSupervisorValue(order, kvp.Key);
                    if (supervisorField.Contains("تمت الإعادة بواسطة"))
                    {
                        var date = ExtractProcessingStartDate(supervisorField, order.CreatedAt);
                        if (date != order.CreatedAt) return date;
                    }
                }
            }

            // للطلبات العادية
            if (coordinatorField.Contains("تم التحويل للمنسق"))
            {
                var date = ExtractProcessingStartDate(coordinatorField, order.CreatedAt);
                if (date != order.CreatedAt) return date;
            }

            return order.CreatedAt;
        }
        /// <summary>
        /// تحليل الطلبات المعلقة للمساعدين 👔
        /// </summary>
        /// <summary>
        /// تحليل الطلبات المعلقة للمساعدين 👔 - محدث ومُصحح
        /// </summary>
        private UnifiedPendingStatisticsDto AnalyzeAssistantPending(string assistantCode, string assistantName, List<Core.Models.OrdersTable> allOrders)
        {
            var stat = new UnifiedPendingStatisticsDto
            {
                EntityName = assistantName,
                EntityType = "Assistant"  // ✅ إضافة نوع الكيان
            };

            // الطلبات المعلقة عند المساعد
            var pendingOrders = allOrders.Where(o => IsAssistantPending(o, assistantCode)).ToList();

            var regularPendingDetails = new List<PendingOrderDetails>();
            var returnedOrderDetails = new List<PendingOrderDetails>();

            foreach (var order in pendingOrders)
            {
                var assistantConfirmation = order.ConfirmedByAssistantManager ?? "";

                // تحديد نوع الطلب وحالة العرض
                string orderCategory;
                string statusDisplay;

                // فحص إذا كان الطلب معاد
                if (assistantConfirmation.Contains("تمت الإعادة من") ||
                    assistantConfirmation.Contains("معاد من") ||
                    order.OrderStatus.ToString().Contains("ReturnedBy"))
                {
                    orderCategory = "معاد";
                    statusDisplay = "معاد لمساعد المدير";
                }
                // فحص إذا كان يتطلب إجراء
                else if (assistantConfirmation.Contains("طلب إجراء") ||
                         assistantConfirmation.Contains("يتطلب إجراء"))
                {
                    orderCategory = "يتطلب إجراء";
                    statusDisplay = "يتطلب إجراء من المساعد";
                }
                // الطلبات المعلقة العادية
                else
                {
                    orderCategory = "معلق";
                    statusDisplay = "في انتظار المساعد";
                }

                var detail = new PendingOrderDetails
                {
                    OrderId = order.Id,
                    OrderNumber = order.Id.ToString(),
                    EmployeeName = order.EmployeeName ?? "",
                    CreatedDate = order.CreatedAt,
                    Department = order.Department ?? "",
                    OrderType = order.OrderType ?? "",
                    Status = statusDisplay,
                    OrderCategory = orderCategory  // ✅ إضافة فئة الطلب
                };

                // حساب أيام الانتظار
                detail.ProcessingStartDate = ExtractAssistantProcessingDate(order);
                detail.PendingDays = (int)CalculateBusinessDays(detail.ProcessingStartDate, DateTime.Now);

                // تحديد مستوى الأولوية
                if (detail.PendingDays > 5) detail.IsUrgent = true;
                else if (detail.PendingDays > 3) detail.IsOverdue = true;
                else if (detail.PendingDays > 2) detail.IsCritical = true;

                // إضافة للقائمة المناسبة
                if (orderCategory == "معاد")
                {
                    returnedOrderDetails.Add(detail);
                }
                else
                {
                    regularPendingDetails.Add(detail);
                }
            }

            // دمج القوائم
            var allOrderDetails = new List<PendingOrderDetails>();
            allOrderDetails.AddRange(regularPendingDetails);
            allOrderDetails.AddRange(returnedOrderDetails);

            stat.PendingOrdersList = allOrderDetails;

            if (allOrderDetails.Any())
            {
                // ✅ حساب الإحصائيات المنفصلة
                stat.RegularPendingCount = regularPendingDetails.Count;
                stat.ActionRequiredCount = regularPendingDetails.Count(p => p.OrderCategory == "يتطلب إجراء");
                stat.ReturnedCount = returnedOrderDetails.Count;

                // حساب المتوسطات
                stat.AveragePendingDays = allOrderDetails.Average(p => p.PendingDays);

                if (regularPendingDetails.Any())
                    stat.RegularPendingAverage = regularPendingDetails.Average(p => p.PendingDays);

                var actionRequiredOrders = regularPendingDetails.Where(p => p.OrderCategory == "يتطلب إجراء").ToList();
                if (actionRequiredOrders.Any())
                    stat.ActionRequiredAverage = actionRequiredOrders.Average(p => p.PendingDays);

                if (returnedOrderDetails.Any())
                    stat.ReturnedAverage = returnedOrderDetails.Average(p => p.PendingDays);

                stat.LongPendingCount = allOrderDetails.Count(p => p.PendingDays > 3);

                _logger.LogInformation($"📊 {assistantName}: {stat.RegularPendingCount} معلق، {stat.ActionRequiredCount} يتطلب إجراء، {stat.ReturnedCount} معاد، إجمالي: {stat.TotalPendingOrders}");
            }

            return stat;
        }
        /// <summary>
        /// فحص هل الطلب معلق عند المساعد
        /// </summary>
        private bool IsAssistantPending(Core.Models.OrdersTable order, string assistantCode)
        {
            // الطلب في مرحلة المساعد
            if (order.OrderStatus.ToString() == assistantCode)
            {
                var assistantConfirmation = order.ConfirmedByAssistantManager ?? "";
                // إذا لم يعتمد أو يلغي أو يعيد = معلق
                return !assistantConfirmation.Contains("اعتماد بواسطة") &&
                       !assistantConfirmation.Contains("تم الإلغاء بواسطة") &&
                       !assistantConfirmation.Contains("تمت الإعادة بواسطة");
            }

            return false;
        }
        // <summary>
        /// استخراج تاريخ وصول الطلب للمساعد
        /// </summary>
        private DateTime ExtractAssistantProcessingDate(Core.Models.OrdersTable order)
        {
            var assistantField = order.ConfirmedByAssistantManager ?? "";

            // البحث عن تاريخ التحويل للمساعد
            if (assistantField.Contains("تم التحويل لمساعد المدير"))
            {
                var date = ExtractProcessingStartDate(assistantField, order.CreatedAt);
                if (date != order.CreatedAt) return date;
            }

            // البحث في حقل المنسق إذا تم التحويل مباشرة
            var coordinatorField = order.ConfirmedByCoordinator ?? "";
            if (coordinatorField.Contains("تم التحويل لمساعد المدير"))
            {
                var date = ExtractProcessingStartDate(coordinatorField, order.CreatedAt);
                if (date != order.CreatedAt) return date;
            }

            return order.CreatedAt;
        }
        /// <summary>
        /// تحليل الطلبات المعلقة للأقسام 🏢
        /// </summary>
        private UnifiedPendingStatisticsDto AnalyzeDepartmentPending(string deptName, List<Core.Models.OrdersTable> orders)
        {
            var stat = new UnifiedPendingStatisticsDto
            {
                EntityName = deptName,
                EntityType = "Department"
            };

            try
            {
                // فصل الطلبات إلى فئتين بدقة
                var (regularPendingOrders, returnedOrders) = GetSeparatedDepartmentOrders(orders);

                // ✅ التأكد من عدم وجود تداخل
                var regularPendingDetails = new List<PendingOrderDetails>();
                var returnedOrderDetails = new List<PendingOrderDetails>();

                // معالجة الطلبات المعلقة العادية
                foreach (var order in regularPendingOrders)
                {
                    var detail = CreateDepartmentPendingOrderDetails(order, "معلق");
                    if (detail != null)
                    {
                        regularPendingDetails.Add(detail);
                    }
                }

                // معالجة الطلبات المعادة (بدون تداخل)
                foreach (var order in returnedOrders)
                {
                    // تأكد أن الطلب ليس موجود في القائمة الأولى
                    if (!regularPendingDetails.Any(p => p.OrderId == order.Id))
                    {
                        var detail = CreateDepartmentPendingOrderDetails(order, "معاد");
                        if (detail != null)
                        {
                            returnedOrderDetails.Add(detail);
                        }
                    }
                }

                // دمج القوائم
                var allOrderDetails = new List<PendingOrderDetails>();
                allOrderDetails.AddRange(regularPendingDetails);
                allOrderDetails.AddRange(returnedOrderDetails);

                // تعيين قائمة الطلبات
                stat.PendingOrdersList = allOrderDetails;

                // حساب الإحصائيات للأقسام
                stat.RegularPendingCount = regularPendingDetails.Count;
                stat.ReturnedCount = returnedOrderDetails.Count;

                if (allOrderDetails.Any())
                {
                    stat.AveragePendingDays = allOrderDetails.Average(p => p.PendingDays);

                    if (regularPendingDetails.Any())
                        stat.RegularPendingAverage = regularPendingDetails.Average(p => p.PendingDays);

                    if (returnedOrderDetails.Any())
                        stat.ReturnedAverage = returnedOrderDetails.Average(p => p.PendingDays);

                    stat.LongPendingCount = allOrderDetails.Count(p => p.PendingDays > 5);

                    // تحديد خصائص الطلبات
                    foreach (var order in allOrderDetails)
                    {
                        if (order.PendingDays > 7)
                            order.IsUrgent = true;
                        else if (order.PendingDays > 5)
                            order.IsOverdue = true;
                        else if (order.PendingDays > 3)
                            order.IsCritical = true;
                    }
                }

                _logger.LogInformation($"📊 {deptName}: {stat.RegularPendingCount} معلق، {stat.ReturnedCount} معاد، إجمالي: {stat.TotalPendingOrders}");

                return stat;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ خطأ في تحليل الطلبات المعلقة للقسم: {deptName}");
                return stat;
            }
        }
        /// <summary>
        /// فصل طلبات الأقسام إلى معلقة ومعادة
        /// </summary>
        private (List<Core.Models.OrdersTable> RegularPending, List<Core.Models.OrdersTable> ReturnedOrders)
            GetSeparatedDepartmentOrders(List<Core.Models.OrdersTable> orders)
        {
            var regularPending = new List<Core.Models.OrdersTable>();
            var returnedOrders = new List<Core.Models.OrdersTable>();

            foreach (var order in orders)
            {
                var status = order.OrderStatus.ToString();

                if (status == "DM")
                {
                    var deptConfirmation = order.ConfirmedByDepartmentManager ?? "";
                    // إذا لم يعتمد أو يلغي = معلق
                    if (!deptConfirmation.Contains("اعتماد بواسطة") &&
                        !deptConfirmation.Contains("تم الإلغاء بواسطة"))
                    {
                        regularPending.Add(order);
                    }
                }
                else if (status == "ReturnedByCoordinator" || status == "ReturnedByAssistantManager")
                {
                    returnedOrders.Add(order);
                }
            }

            return (regularPending, returnedOrders);
        }
        /// <summary>
        /// إنشاء تفاصيل الطلب المعلق للقسم
        /// </summary>
        private PendingOrderDetails CreateDepartmentPendingOrderDetails(
            Core.Models.OrdersTable order,
            string orderType)
        {
            try
            {
                var status = order.OrderStatus.ToString();
                var processingStartDate = ExtractDepartmentProcessingDate(order);
                var pendingDays = CalculateBusinessDays(processingStartDate, DateTime.Now);

                // تحديد الحالة بناءً على النوع
                string statusDisplay = orderType switch
                {
                    "معلق" => "في انتظار مدير القسم",
                    "معاد" when status == "ReturnedByCoordinator" => "معاد من المنسق",
                    "معاد" when status == "ReturnedByAssistantManager" => "معاد من المساعد",
                    "معاد" => "معاد للقسم",
                    _ => "قيد المعالجة"
                };

                return new PendingOrderDetails
                {
                    OrderId = order.Id,
                    OrderNumber = order.Id.ToString(),
                    EmployeeName = order.EmployeeName ?? "",
                    CreatedDate = order.CreatedAt,
                    ProcessingStartDate = processingStartDate,
                    PendingDays = (int)pendingDays,
                    Status = statusDisplay,
                    Department = order.Department ?? "",
                    OrderType = order.OrderType ?? "",
                    OrderCategory = orderType  // ✅ إضافة فئة الطلب
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ خطأ في إنشاء تفاصيل الطلب المعلق للقسم: {order.Id}");
                return null;
            }
        }
        /// <summary>
        /// فحص هل الطلب معلق في القسم 🔍
        /// </summary>
        private bool IsDepartmentPending(Core.Models.OrdersTable order)
        {
            var status = order.OrderStatus.ToString();

            // 1️⃣ الطلب في القسم (DM)
            if (status == "DM")
            {
                var deptConfirmation = order.ConfirmedByDepartmentManager ?? "";
                // إذا لم يعتمد أو يلغي = معلق
                return !deptConfirmation.Contains("اعتماد بواسطة") &&
                       !deptConfirmation.Contains("تم الإلغاء بواسطة");
            }

            // 2️⃣ الطلبات المعادة للقسم
            if (status == "ReturnedByAssistantManager" || status == "ReturnedByCoordinator")
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// استخراج تاريخ وصول الطلب للقسم 📅
        /// </summary>
        private DateTime ExtractDepartmentProcessingDate(Core.Models.OrdersTable order)
        {
            var deptManagerField = order.ConfirmedByDepartmentManager ?? "";

            // البحث عن "تم التحويل لمدير القسم"
            if (deptManagerField.Contains("تم التحويل لمدير القسم"))
            {
                var date = ExtractProcessingStartDate(deptManagerField, order.CreatedAt);
                if (date != order.CreatedAt) return date;
            }

            return order.CreatedAt;
        }

        /// <summary>
        /// حساب الأولويات الموحدة 🎯
        /// </summary>
        private void CalculateUnifiedPriorities(List<UnifiedPendingStatisticsDto> stats)
        {
            foreach (var stat in stats)
            {
                stat.PriorityScore = (stat.TotalPendingOrders * 0.6) + (stat.AveragePendingDays * 0.4);

                // أولويات مختلفة حسب النوع
                if (stat.EntityType == "Manager")
                    stat.PriorityScore *= 1.5;  // المدير أعلى أولوية
                else if (stat.EntityType == "Department")
                    stat.PriorityScore *= 1.4;
                else if (stat.EntityType == "Coordinator")
                    stat.PriorityScore *= 1.3;
                else if (stat.EntityType == "Assistant")
                    stat.PriorityScore *= 1.2;
            }

            var ranked = stats.OrderByDescending(s => s.PriorityScore).ToList();
            for (int i = 0; i < ranked.Count; i++)
            {
                ranked[i].Ranking = i + 1;
            }
        }
        /// <summary>
        /// الحصول على حالة الطلب المعلق في القسم
        /// </summary>
        private string GetDepartmentPendingStatus(Core.Models.OrdersTable order)
        {
            var status = order.OrderStatus.ToString();

            if (status == "DM")
            {
                return "في انتظار مدير القسم";
            }
            else if (status == "ReturnedByAssistantManager")
            {
                return "معاد من المساعد";
            }
            else if (status == "ReturnedByCoordinator")
            {
                return "معاد من المنسق";
            }

            return "قيد المعالجة";
        }
        // 🎯 نهاية المرحلة الثانية: الطلبات المعلقة 
        // ===================================================================
        // 🎯 المرحلة الثالثة: التحليل التاريخي 
        // ===================================================================
        /// <summary>
        /// حساب إحصائيات التأخير للمشرفين - جديد ومبسط
        /// يركز على الطلبات المكتملة (المعتمدة) فقط
        /// </summary>
        public async Task<ServiceResult<List<SupervisorDelayStatisticsDto>>> GetSupervisorDelayStatisticsAsync()
        {
            try
            {
                var allOrders = (await _unitOfWork.Orders.GetAllAsync()).ToList();
                var delayStats = new List<SupervisorDelayStatisticsDto>();

                _logger.LogInformation("📊 بدء حساب إحصائيات التأخير لـ {Count} مشرف", SupervisorMappings.Count);

                // إنشاء إحصائية لكل مشرف حتى لو لم يكن لديه طلبات
                foreach (var (fieldName, supervisorName) in SupervisorMappings)
                {
                    var stats = new SupervisorDelayStatisticsDto { SupervisorName = supervisorName };

                    var ordersWithSupervisor = allOrders
                        .Where(o => !string.IsNullOrEmpty(GetSupervisorValue(o, fieldName)))
                        .ToList();

                    _logger.LogInformation("📊 {SupervisorName}: {OrderCount} طلب إجمالي",
                        supervisorName, ordersWithSupervisor.Count);

                    if (ordersWithSupervisor.Any())
                    {
                        // حساب إحصائيات التأخير للمشرفين الذين لديهم طلبات
                        CalculateDelayStatistics(stats, ordersWithSupervisor, fieldName);
                    }
                    else
                    {
                        // المشرفين بدون طلبات - قيم افتراضية
                        stats.TotalCompletedOrders = 0;
                        stats.AverageDelayDays = 0;
                        stats.PerformanceLevel = "لا توجد بيانات";
                        _logger.LogInformation("⚠️ {SupervisorName}: لا توجد طلبات", supervisorName);
                    }

                    delayStats.Add(stats);
                }

                // ترتيب المشرفين (تحديث الدالة لتتعامل مع المشرفين بدون طلبات)
                CalculateDelayRankingsImproved(delayStats);

                _logger.LogInformation("✅ تم حساب إحصائيات التأخير لـ {Count} مشرف بنجاح", delayStats.Count);
                return ServiceResult<List<SupervisorDelayStatisticsDto>>.Success(delayStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب إحصائيات التأخير للمشرفين");
                return ServiceResult<List<SupervisorDelayStatisticsDto>>.Failure($"حدث خطأ: {ex.Message}");
            }
        }
        /// <summary>
        /// حساب ترتيب المشرفين محسن ليتعامل مع جميع المشرفين
        /// </summary>
        private void CalculateDelayRankingsImproved(List<SupervisorDelayStatisticsDto> supervisors)
        {
            try
            {
                _logger.LogInformation("🏆 بدء ترتيب {Count} مشرف حسب الأداء (التأخير)", supervisors.Count);

                // تقسيم المشرفين: من لديهم طلبات ومن ليس لديهم
                var supervisorsWithOrders = supervisors.Where(s => s.TotalCompletedOrders > 0).ToList();
                var supervisorsWithoutOrders = supervisors.Where(s => s.TotalCompletedOrders == 0).ToList();

                // ترتيب المشرفين الذين لديهم طلبات
                var rankedSupervisorsWithOrders = supervisorsWithOrders
                    .OrderBy(s => s.AverageDelayDays)                // الأقل تأخيراً أولاً
                    .ThenByDescending(s => s.OnTimePercentage)       // الأعلى في النسبة في الوقت المحدد
                    .ThenByDescending(s => s.TotalCompletedOrders)   // الأكثر إنجازاً
                    .ThenBy(s => s.SupervisorName)                   // الترتيب الأبجدي
                    .ToList();

                // ترتيب المشرفين بدون طلبات أبجدياً
                var rankedSupervisorsWithoutOrders = supervisorsWithoutOrders
                    .OrderBy(s => s.SupervisorName)
                    .ToList();

                // تطبيق الترتيب للمشرفين الذين لديهم طلبات
                for (int i = 0; i < rankedSupervisorsWithOrders.Count; i++)
                {
                    rankedSupervisorsWithOrders[i].DelayRanking = i + 1;

                    _logger.LogInformation("📍 ترتيب #{Rank}: {Name} " +
                        "(متوسط تأخير: {AvgDelay:F1} يوم, في الوقت: {OnTime:F1}%, مكتمل: {Completed})",
                        i + 1, rankedSupervisorsWithOrders[i].SupervisorName,
                        rankedSupervisorsWithOrders[i].AverageDelayDays,
                        rankedSupervisorsWithOrders[i].OnTimePercentage,
                        rankedSupervisorsWithOrders[i].TotalCompletedOrders);
                }

                // المشرفين بدون طلبات يحصلون على ترتيب 0 (غير مصنف)
                foreach (var supervisor in rankedSupervisorsWithoutOrders)
                {
                    supervisor.DelayRanking = 0;
                    _logger.LogInformation("📍 غير مصنف: {Name} (لا توجد طلبات)", supervisor.SupervisorName);
                }

                // إعادة تنظيم القائمة الأصلية
                supervisors.Clear();
                supervisors.AddRange(rankedSupervisorsWithOrders);   // المرتبين أولاً
                supervisors.AddRange(rankedSupervisorsWithoutOrders); // غير المصنفين آخراً

                _logger.LogInformation("✅ تم ترتيب {WithOrders} مشرف + {WithoutOrders} غير مصنف",
                    rankedSupervisorsWithOrders.Count, rankedSupervisorsWithoutOrders.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في ترتيب المشرفين");
            }
        }
        /// <summary>
        /// حساب إحصائيات التأخير للمشرف الواحد
        /// </summary>
        private void CalculateDelayStatistics(SupervisorDelayStatisticsDto stats, List<OrdersTable> orders, string fieldName)
        {
            try
            {
                var completedOrders = new List<SupervisorDelayOrderInfo>();

                foreach (var order in orders)
                {
                    var fieldValue = GetSupervisorValue(order, fieldName);
                    if (string.IsNullOrEmpty(fieldValue)) continue;

                    // التعامل مع الحالات الثلاثة: اعتماد، إجراء، إعادة
                    if (fieldValue.Contains("اعتماد") ||
     fieldValue.Contains("طلب إجراء") ||
     fieldValue.Contains("تمت الإعادة"))
                    {
                        var delayInfo = CalculateOrderDelayImproved(order, fieldValue, fieldName);
                        if (delayInfo != null)
                        {
                            completedOrders.Add(delayInfo);
                        }
                    }
                }

                // تعبئة الإحصائيات
                stats.TotalCompletedOrders = completedOrders.Count;

                if (completedOrders.Any())
                {
                    var delays = completedOrders.Select(o => o.DelayDays).ToList();

                    stats.AverageDelayDays = Math.Round(delays.Average(), 1);
                    stats.MaxDelayDays = delays.Max();
                    stats.MinDelayDays = delays.Min();

                    // تطبيق التصنيف الجديد الخماسي
                    stats.DelayedOrders = completedOrders.Count(o => o.DelayDays > 1); // أكثر من يوم واحد
                    stats.CriticalDelayOrders = completedOrders.Count(o => o.DelayDays > 5); // أكثر من 5 أيام

                    // حساب النسب بناءً على التصنيف الجديد
                    var onTimeOrders = completedOrders.Count(o => o.DelayDays <= 1); // فوري وسريع
                    stats.OnTimePercentage = Math.Round((double)onTimeOrders / stats.TotalCompletedOrders * 100, 1);
                    stats.DelayPercentage = Math.Round((double)stats.DelayedOrders / stats.TotalCompletedOrders * 100, 1);

                    // تحديد مستوى الأداء بناءً على التصنيف الجديد
                    stats.PerformanceLevel = GetPerformanceLevelImproved(stats.AverageDelayDays);

                    // قائمة الطلبات المتأخرة (أكثر من يوم واحد)
                    stats.DelayedOrdersList = completedOrders
                        .Where(o => o.DelayDays > 1)
                        .OrderByDescending(o => o.DelayDays)
                        .ToList();
                }

                _logger.LogInformation("📊 {SupervisorName}: مكتمل={TotalCompleted}, متوسط التأخير={AverageDelay:F1} يوم, متأخر={DelayedCount}",
                    stats.SupervisorName, stats.TotalCompletedOrders, stats.AverageDelayDays, stats.DelayedOrders);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب إحصائيات التأخير للمشرف: {SupervisorName}", stats.SupervisorName);
            }
        }
        /// <summary>
        /// تحديد مستوى الأداء بناءً على التصنيف الخماسي المطلوب
        /// </summary>
        private string GetPerformanceLevelImproved(double averageDelayDays)
        {
            // التصنيف الخماسي المطلوب
            if (averageDelayDays == 0) return "إنجاز فوري";        // نفس اليوم
            if (averageDelayDays <= 1) return "إنجاز سريع";        // يوم واحد
            if (averageDelayDays <= 3) return "إنجاز متوسط المدة";  // 2-3 أيام
            if (averageDelayDays <= 5) return "تأخير طفيف";        // 4-5 أيام
            return "تأخير في الإنجاز";                           // أكثر من 5 أيام
        }
        /// <summary>
        /// حساب تأخير طلب واحد - محسن ليستخدم الدوال الموجودة
        /// </summary>
        private SupervisorDelayOrderInfo CalculateOrderDelayImproved(OrdersTable order, string fieldValue, string fieldName)
        {
            try
            {
                // استخدام OrderHelper للدقة
                var inDate = OrderHelper.ExtractInDate(fieldValue);
                var outDate = OrderHelper.ExtractOutDate(fieldValue);

                DateTime arrivalDate, completionDate;

                // تحويل التواريخ باستخدام الدالة المتقدمة الموجودة
                if (DateTime.TryParse(inDate, out arrivalDate))
                {
                    arrivalDate = ConvertToGregorian(arrivalDate);
                }
                else
                {
                    arrivalDate = order.CreatedAt;
                }

                if (DateTime.TryParse(outDate, out completionDate))
                {
                    completionDate = ConvertToGregorian(completionDate);
                }
                else
                {
                    return null; // لا يمكن حساب التأخير بدون تاريخ إنجاز
                }

                // تحديد نوع الإجراء
                string actionType = "اعتماد";
                if (fieldValue.Contains("طلب إجراء")) actionType = "إجراء";
                else if (fieldValue.Contains("تمت الإعادة")) actionType = "إعادة";

                // **الحل السهل البسيط: معالجة حالة طلب الإجراء**
                int delayDays;

                if (fieldValue.Contains("اعتماد"))
                {
                    // التحقق من وجود المشرف في SupervisorNotes (يعني طلب إجراء سابقاً)
                    var unitName = GetUnitNameFromSupervisorMapping(fieldName);
                    if (!string.IsNullOrEmpty(unitName) && HasSupervisorActionRequest(order.SupervisorNotes, unitName))
                    {
                        // المشرف طلب إجراء سابقاً، التأخير = يوم واحد فقط
                        delayDays = 1;
                    }
                    else
                    {
                        // حساب التأخير العادي
                        delayDays = (int)CalculateBusinessDays(arrivalDate, completionDate);
                    }
                }
                else
                {
                    // حساب التأخير العادي للحالات الأخرى
                    delayDays = (int)CalculateBusinessDays(arrivalDate, completionDate);
                }

                return new SupervisorDelayOrderInfo
                {
                    OrderId = order.Id,
                    EmployeeName = order.EmployeeName ?? "غير محدد",
                    ArrivalDate = arrivalDate,
                    ApprovalDate = completionDate,
                    DelayDays = Math.Max(0, delayDays),
                    Department = order.Department ?? "غير محدد",
                    OrderType = order.OrderType ?? "غير محدد",
                    ActionType = actionType,
                    IsCriticalDelay = delayDays > 5
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب تأخير الطلب: {OrderId}", order.Id);
                return null;
            }
        }


        /// <summary>
        /// التحقق من وجود طلب إجراء من القسم في SupervisorNotes
        /// </summary>
        private bool HasSupervisorActionRequest(string supervisorNotes, string unitName)
        {
            try
            {
                if (string.IsNullOrEmpty(supervisorNotes) || string.IsNullOrEmpty(unitName))
                    return false;

                // البحث عن نمط "مشرف [اسم القسم]:" في ملاحظات المشرفين
                var pattern = $"مشرف {unitName}:";
                return supervisorNotes.Contains(pattern);
            }
            catch
            {
                return false;
            }
        }
        /// <summary>
        /// استخراج اسم الوحدة من مفتاح المشرف
        /// </summary>
        private string GetUnitNameFromSupervisorMapping(string supervisorFieldName)
        {
            var unitMappings = new Dictionary<string, string>
    {
        {"SupervisorOfEmployeeServices", "خدمات الموظفين"},
        {"SupervisorOfHumanResourcesPlanning", "إدارة تخطيط الموارد البشرية"},
        {"SupervisorOfInformationTechnology", "إدارة تقنية المعلومات"},
        {"SupervisorOfAttendance", "مراقبة الدوام"},
        {"SupervisorOfMedicalRecords", "السجلات الطبية"},
        {"SupervisorOfPayrollAndBenefits", "إدارة الرواتب والاستحقاقات"},
        {"SupervisorOfLegalAndCompliance", "إدارة القانونية والالتزام"},
        {"SupervisorOfHumanResourcesServices", "خدمات الموارد البشرية"},
        {"SupervisorOfHousing", "إدارة الإسكان"},
        {"SupervisorOfFiles", "قسم الملفات"},
        {"SupervisorOfOutpatientClinics", "العيادات الخارجية"},
        {"SupervisorOfSocialSecurity", "التأمينات الاجتماعية"},
        {"SupervisorOfInventoryControl", "وحدة مراقبة المخزون"},
        {"SupervisorOfRevenueDevelopment", "إدارة تنمية الإيرادات"},
        {"SupervisorOfSecurity", "إدارة الأمن و السلامة"},
        {"SupervisorOfMedicalConsultation", "الطب الاتصالي"}
    };

            return unitMappings.TryGetValue(supervisorFieldName, out var unitName) ? unitName : string.Empty;
        }
        /// <summary>
        /// دالة مساعدة لتحديد لون الشارة حسب أيام التأخير
        /// </summary>
        public static string GetOrderBadgeClass(int delayDays)
        {
            if (delayDays <= 1) return "bg-success";      // أخضر - ممتاز
            if (delayDays <= 2) return "bg-info";         // أزرق - جيد
            if (delayDays <= 5) return "bg-warning";      // أصفر - متوسط
            return "bg-danger";                           // أحمر - يحتاج تحسين
        }






        // 🎯 نهاية المرحلة الثالثة: التحليل التاريخي 

        // ===================================================================
        // 🔧 الدوال المساعدة للتواريخ )
        // ===================================================================

        /// <summary>
        /// استخراج تاريخ بداية المعالجة مع التحويل الهجري الصحيح 
        /// </summary>
        private DateTime ExtractProcessingStartDateAdvanced(string supervisorValue, DateTime orderCreatedDate)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(supervisorValue))
                {
                    _logger.LogWarning("⚠️ قيمة فارغة لمُعامل supervisorValue");
                    return orderCreatedDate;
                }

                foreach (var (pattern, isHijri) in new[]
                {
            (pattern: @"\b(14\d{2}-\d{1,2}-\d{1,2})\b", isHijri: true),
            (pattern: @"\b(20\d{2}-\d{1,2}-\d{1,2})\b", isHijri: false)
        })
                {
                    var match = Regex.Match(supervisorValue, pattern);
                    if (!match.Success) continue;

                    var dateStr = match.Groups[1].Value;
                    var parts = dateStr.Split('-');
                    if (parts.Length != 3) continue;

                    if (int.TryParse(parts[0], out var year) &&
                        int.TryParse(parts[1], out var month) &&
                        int.TryParse(parts[2], out var day))
                    {
                        if ((isHijri && (year < 1300 || year > 1500)) ||
                            month < 1 || month > 12 ||
                            day < 1 || day > 30)
                        {
                            _logger.LogWarning("⚠️ تاريخ غير صالح: {DateStr}", dateStr);
                            continue;
                        }

                        try
                        {
                            if (isHijri)
                            {
                                var hijriCalendar = new HijriCalendar();
                                var gregorianDate = hijriCalendar.ToDateTime(year, month, day, 0, 0, 0, 0);
                                _logger.LogInformation("📅 تحويل تاريخ هجري: {DateStr} → {GregorianDate}",
                                    dateStr, gregorianDate.ToString("yyyy-MM-dd"));
                                return gregorianDate;
                            }
                            else if (DateTime.TryParseExact(dateStr, "yyyy-MM-dd",
                                CultureInfo.InvariantCulture, DateTimeStyles.None, out var gregorianDate))
                            {
                                _logger.LogInformation("📅 تم العثور على تاريخ ميلادي: {GregorianDate}",
                                    gregorianDate.ToString("yyyy-MM-dd"));
                                return gregorianDate;
                            }
                        }
                        catch (Exception ex)
                        {
                            if (isHijri)
                            {
                                _logger.LogWarning(ex, "⚠️ خطأ في تحويل التاريخ الهجري: {DateStr}", dateStr);
                                var approxYear = (int)(year * 0.97 + 621);
                                var safeMonth = Math.Min(month, 12);
                                var safeDay = Math.Min(day, DateTime.DaysInMonth(approxYear, safeMonth));
                                var approxDate = new DateTime(approxYear, safeMonth, safeDay);

                                _logger.LogInformation("🔄 استخدام التحويل التقريبي: {ApproxDate}",
                                    approxDate.ToString("yyyy-MM-dd"));
                                return approxDate;
                            }
                        }
                    }
                }

                _logger.LogWarning("⚠️ لم يتم العثور على تاريخ صالح في: {SupervisorValue}", supervisorValue);
                return orderCreatedDate;
            }
            catch (Exception ex)
            {
                _logger.LogCritical(ex, "❌ خطأ حرج في استخراج تاريخ المعالجة من: {SupervisorValue}", supervisorValue);
                return orderCreatedDate;
            }
        }


        /// <summary>
        /// استخراج تاريخ بداية المعالجة من نص المشرف
        /// </summary>
        private DateTime ExtractProcessingStartDate(string text, DateTime fallbackDate)
        {
            try
            {
                // البحث عن تاريخ في النص بصيغ مختلفة
                var datePatterns = new[]
                {
            @"\d{4}-\d{2}-\d{2}",  // YYYY-MM-DD
            @"\d{2}/\d{2}/\d{4}",  // DD/MM/YYYY
            @"\d{4}/\d{2}/\d{2}"   // YYYY/MM/DD
        };

                foreach (var pattern in datePatterns)
                {
                    var matches = System.Text.RegularExpressions.Regex.Matches(text, pattern);
                    if (matches.Count > 0)
                    {
                        // أخذ آخر تاريخ في النص
                        var lastMatch = matches[matches.Count - 1].Value;
                        if (DateTime.TryParse(lastMatch, out DateTime parsedDate))
                        {
                            return parsedDate;
                        }
                    }
                }

                return fallbackDate;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في استخراج تاريخ بداية المعالجة");
                return fallbackDate;
            }
        }
        /// <summary>
        /// حساب أيام العمل محسن (استثناء الجمعة والسبت مع معالجة الحالات الحدية)
        /// </summary>
        private double CalculateBusinessDays(DateTime startDate, DateTime endDate)
        {
            try
            {
                _logger.LogInformation($"📅 حساب الأيام بين {startDate:yyyy-MM-dd} و {endDate:yyyy-MM-dd}");

                double businessDays = 0;
                DateTime currentDate = startDate.Date;

                while (currentDate <= endDate.Date)
                {
                    if (currentDate.DayOfWeek != DayOfWeek.Friday &&
                        currentDate.DayOfWeek != DayOfWeek.Saturday)
                    {
                        businessDays++;
                    }
                    currentDate = currentDate.AddDays(1);
                }

                _logger.LogInformation($"📅 عدد أيام العمل: {businessDays}");
                return businessDays;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في حساب أيام العمل");
                return (endDate - startDate).TotalDays;
            }
        }
        /// <summary>
        /// استخراج جميع التواريخ من النص (بدون تغيير الاسم)
        /// </summary>
        private List<DateTime> ExtractAllDatesFromText(string text)
        {
            var dates = new List<DateTime>();
            if (string.IsNullOrEmpty(text)) return dates;

            var datePatterns = new[]
            {
        @"\b14\d{2}-\d{1,2}-\d{1,2}\b",  // التاريخ الهجري
        @"\b20\d{2}-\d{1,2}-\d{1,2}\b",  // التاريخ الميلادي
        @"\b\d{4}-\d{1,2}-\d{1,2}\b"    // أي تاريخ
    };

            foreach (var pattern in datePatterns)
            {
                var matches = System.Text.RegularExpressions.Regex.Matches(text, pattern);
                foreach (System.Text.RegularExpressions.Match match in matches)
                {
                    if (DateTime.TryParse(match.Value, out var date))
                    {
                        var gregorianDate = ConvertToGregorian(date);
                        if (!dates.Any(d => d.Date == gregorianDate.Date))
                            dates.Add(gregorianDate);
                    }
                }
            }

            return dates.OrderBy(d => d).ToList();
        }

        /// <summary>
        /// تحويل التاريخ الهجري إلى الميلادي (بدون تغيير الاسم)
        /// </summary>
        private DateTime ConvertToGregorian(DateTime inputDate)
        {
            if (inputDate.Year >= 1400 && inputDate.Year <= 1500)
            {
                try
                {
                    var hijriCalendar = new System.Globalization.HijriCalendar();
                    return hijriCalendar.ToDateTime(inputDate.Year, inputDate.Month, inputDate.Day, 0, 0, 0, 0);
                }
                catch
                {
                    return inputDate.AddYears(-622); // تحويل تقريبي كاحتياط
                }
            }
            return inputDate;
        }

        // 🔧 نهاية الدوال المساعدة للتواريخ )








        //نهاية احصائيات المشرفين




        /// <summary>
        /// الحصول على إحصائيات الطلبات المنجزة حسب أسماء المشرفين - محدث ومنظف
        /// </summary>
        public async Task<ServiceResult<List<CompletedSupervisorReportDto>>> GetCompletedSupervisorStatisticsAsync()
        {
            try
            {
                var allOrders = (await _unitOfWork.Orders.GetAllAsync()).ToList();
                var supervisorStats = new Dictionary<string, CompletedSupervisorReportDto>();

                _logger.LogInformation($"إجمالي الطلبات لإحصائيات الطلبات المنجزة: {allOrders.Count}");

                // 🔧 معالجة جميع الطلبات لكل مشرف
                foreach (var order in allOrders)
                {
                    ProcessOrderForSupervisors(order, supervisorStats);
                }

                // 🔧 حساب الترتيب والنسب النهائية
                var supervisorList = supervisorStats.Values.Where(s => s.TotalOperations > 0).ToList();
                var totalCompleted = supervisorList.Sum(s => s.CompletedCount);

                var rankedSupervisors = supervisorList
                    // ✅ ترتيب محدث بناءً على المنجز مباشرة
                    .OrderByDescending(s => s.CompletedCount)
                    .ThenByDescending(s => s.TotalWorksDone)
                    .Select((supervisor, index) =>
                    {
                        supervisor.Ranking = index + 1;
                        supervisor.CompletionPercentage = totalCompleted > 0 ?
                            (double)supervisor.CompletedCount / totalCompleted * 100 : 0;


                        // ✅ تحديد اتجاه الأداء
                        supervisor.TrendDirection = DetermineTrend(supervisor.ThisMonthOperations, supervisor.Last3MonthsOperations);

                        return supervisor;
                    })
                    .ToList();

                _logger.LogInformation($"إجمالي الطلبات المنجزة: {totalCompleted}, عدد المشرفين: {rankedSupervisors.Count}");

                return ServiceResult<List<CompletedSupervisorReportDto>>.Success(rankedSupervisors);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting completed supervisor statistics");
                return ServiceResult<List<CompletedSupervisorReportDto>>.Failure($"حدث خطأ أثناء جلب إحصائيات الطلبات المنجزة: {ex.Message}");
            }
        }

        /// <summary>
        /// معالجة طلب واحد لجميع المشرفين - دالة موحدة
        /// </summary>
        private void ProcessOrderForSupervisors(Core.Models.OrdersTable order, Dictionary<string, CompletedSupervisorReportDto> supervisorStats)
        {
            foreach (var fieldName in SupervisorFieldNames)
            {
                var fieldValue = GetSupervisorValue(order, fieldName);

                if (!string.IsNullOrEmpty(fieldValue))
                {
                    var supervisorName = ExtractSupervisorName(fieldValue);
                    var actionType = DetermineActionType(fieldValue);

                    if (!string.IsNullOrEmpty(supervisorName) && actionType != "غير محدد")
                    {
                        // إنشاء أو جلب إحصائيات المشرف
                        if (!supervisorStats.ContainsKey(supervisorName))
                        {
                            supervisorStats[supervisorName] = new CompletedSupervisorReportDto
                            {
                                SupervisorName = supervisorName,
                                TrendDirection = "مستقر"
                            };
                        }

                        // تحديث الإحصائيات
                        UpdateStats(supervisorStats[supervisorName], order, actionType);
                    }
                }
            }
        }

        /// <summary>
        /// استخراج اسم المشرف - دالة موحدة ومُبسطة
        /// </summary>
        private string ExtractSupervisorName(string fieldValue)
        {
            if (string.IsNullOrWhiteSpace(fieldValue))
                return string.Empty;

            try
            {
                fieldValue = fieldValue.Trim();

                var patterns = new[] { "اعتماد بواسطة", "تمت الإعادة بواسطة", "طلب إجراء بواسطة" };

                foreach (var pattern in patterns)
                {
                    if (fieldValue.Contains(pattern))
                    {
                        return ExtractNameAfterKeyword(fieldValue, pattern);
                    }
                }

                return string.Empty;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"خطأ في استخراج اسم المشرف من النص: {fieldValue}. الخطأ: {ex.Message}");
                return string.Empty;
            }
        }

        /// <summary>
        /// استخراج الاسم بعد كلمة مفتاحية - دالة موحدة
        /// </summary>
        private string ExtractNameAfterKeyword(string text, string keyword)
        {
            var index = text.IndexOf(keyword, StringComparison.OrdinalIgnoreCase);
            if (index == -1) return string.Empty;

            var afterKeyword = text.Substring(index + keyword.Length).Trim();

            if (afterKeyword.StartsWith(":"))
                afterKeyword = afterKeyword.Substring(1).Trim();

            var parts = afterKeyword.Split(new char[] { '|', '\n', '\r', '-' }, StringSplitOptions.RemoveEmptyEntries);
            if (parts.Length > 0)
            {
                var name = parts[0].Trim();
                name = System.Text.RegularExpressions.Regex.Replace(name, @"\d{4}-\d{2}-\d{2}", "").Trim();
                return string.IsNullOrWhiteSpace(name) ? string.Empty : name;
            }

            return string.Empty;
        }

        /// <summary>
        /// تحديد نوع العملية - دالة موحدة
        /// </summary>
        private string DetermineActionType(string fieldValue)
        {
            if (string.IsNullOrWhiteSpace(fieldValue))
                return "غير محدد";

            var lowerText = fieldValue.ToLower();

            if (lowerText.Contains("اعتماد بواسطة"))
                return "منجز";

            if (lowerText.Contains("تمت الإعادة بواسطة"))
                return "معاد";

            if (lowerText.Contains("طلب إجراء بواسطة"))
                return "يتطلب إجراء";

            return "غير محدد";
        }

        /// <summary>
        /// تحديث إحصائيات المشرف - دالة موحدة
        /// </summary>
        private void UpdateStats(CompletedSupervisorReportDto stats, Core.Models.OrdersTable order, string actionType)
        {
            // تحديث العداد حسب نوع العملية
            switch (actionType)
            {
                case "منجز":
                    stats.CompletedCount++;
                    stats.TotalWorksDone++;
                    break;
                case "معاد":
                    stats.Returned++;
                    stats.TotalWorksDone++;
                    break;
                case "يتطلب إجراء":
                    stats.NeedsAction++;
                    stats.TotalWorksDone++;
                    break;
            }

            // ✅ حساب الإحصائيات الزمنية
            UpdateTimeStats(stats, order);
        }

        /// <summary>
        /// تحديث الإحصائيات الزمنية - دالة محسنة
        /// </summary>
        private void UpdateTimeStats(CompletedSupervisorReportDto stats, Core.Models.OrdersTable order)
        {
            try
            {
                var now = DateTime.Now;
                var orderDate = order.CreatedAt;

                // حساب بداية الفترات الزمنية المختلفة
                var startOfMonth = new DateTime(now.Year, now.Month, 1);
                var start3MonthsAgo = now.AddMonths(-3);
                var start6MonthsAgo = now.AddMonths(-6);
                var startOfYear = now.AddYears(-1);

                // إحصائيات هذا الشهر
                if (orderDate >= startOfMonth)
                {
                    stats.ThisMonthOperations++;
                }

                // إحصائيات آخر 3 أشهر
                if (orderDate >= start3MonthsAgo)
                {
                    stats.Last3MonthsOperations++;
                }

                // إحصائيات آخر 6 أشهر
                if (orderDate >= start6MonthsAgo)
                {
                    stats.Last6MonthsOperations++;
                }

                // إحصائيات آخر سنة
                if (orderDate >= startOfYear)
                {
                    stats.LastYearOperations++;
                }

                // فحص الطلبات متعددة المراحل
                if (CountActionsInOrder(order, stats.SupervisorName) > 1)
                {
                    stats.MultiStageOrders++;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "خطأ في تحديث الإحصائيات الزمنية للمشرف");
            }
        }

        /// <summary>
        /// عد عدد إجراءات المشرف في طلب واحد - دالة موحدة
        /// </summary>
        private int CountActionsInOrder(Core.Models.OrdersTable order, string supervisorName)
        {
            int count = 0;

            foreach (var fieldName in SupervisorFieldNames)
            {
                var fieldValue = GetSupervisorValue(order, fieldName);
                if (!string.IsNullOrEmpty(fieldValue) &&
                    ExtractSupervisorName(fieldValue) == supervisorName)
                {
                    count++;
                }
            }

            return count;
        }

        /// <summary>
        /// تحديد اتجاه الأداء - دالة محسنة
        /// </summary>
        private string DetermineTrend(int thisMonthCount, int last3MonthsCount)
        {
            if (thisMonthCount == 0 && last3MonthsCount == 0)
                return "مستقر";

            // حساب متوسط شهري من آخر 3 أشهر
            var avgMonthlyIn3Months = last3MonthsCount / 3.0;

            if (thisMonthCount > avgMonthlyIn3Months * 1.3)
                return "متزايد";
            else if (thisMonthCount < avgMonthlyIn3Months * 0.7)
                return "متناقص";
            else
                return "مستقر";
        }
        ///  نهاية لإحصائيات الطلبات المنجزة حسب أسماء المشرفين 







        ///توزيع الطلبات حسب مراحل المعالجة
        /// <summary>
        /// حساب إحصائيات مساعدي المدير - الإصدار النهائي المُصحح والمنظم
        /// </summary>
        /// <summary>
        /// حساب إحصائيات مساعدي المدير - الإصدار المُحسن باستخدام الدوال الموجودة
        /// </summary>
        /// <summary>
        /// حساب إحصائيات مساعدي المدير - الإصدار المُحسن باستخدام الدوال الموجودة
        /// </summary>
        /// <summary>
        /// حساب إحصائيات مساعدي المدير - النسخة النهائية المُصححة
        /// </summary>
        public async Task<ServiceResult<StageStatisticsDto>> GetStageStatisticsAsync()
        {
            try
            {
                var allOrders = (await _unitOfWork.Orders.GetAllAsync()).ToList();

                var statusCounts = allOrders
                    .GroupBy(o => o.OrderStatus)
                    .ToDictionary(g => g.Key, g => g.Count());

                var assistantsCount = new Dictionary<AssistantManagerType, int>
                {
                    [AssistantManagerType.A1] = statusCounts.GetValueOrDefault(OrderStatus.A1, 0),
                    [AssistantManagerType.A2] = statusCounts.GetValueOrDefault(OrderStatus.A2, 0),
                    [AssistantManagerType.A3] = statusCounts.GetValueOrDefault(OrderStatus.A3, 0),
                    [AssistantManagerType.A4] = statusCounts.GetValueOrDefault(OrderStatus.A4, 0)
                };

                // إصلاح: حساب مدير القسم + الطلبات المعادة
                var departmentManagerCount = statusCounts.GetValueOrDefault(OrderStatus.DM, 0) +
                                           statusCounts.GetValueOrDefault(OrderStatus.ReturnedByCoordinator, 0) +
                                           statusCounts.GetValueOrDefault(OrderStatus.ReturnedByAssistantManager, 0) +
                                           statusCounts.GetValueOrDefault(OrderStatus.ReturnedByManager, 0);

                // إصلاح: حساب منسق الموارد البشرية مباشرة
                var hrCoordinatorCount = allOrders.Count(order => IsCoordinatorPending(order));

                // إصلاح: حساب الطلبات الفريدة للمشرفين باستخدام الحقول الصحيحة
                var supervisorFieldNames = new[]
                {
            "SupervisorOfEmployeeServices",
            "SupervisorOfHumanResourcesPlanning",
            "SupervisorOfInformationTechnology",
            "SupervisorOfAttendance",
            "SupervisorOfMedicalRecords",
            "SupervisorOfPayrollAndBenefits",
            "SupervisorOfLegalAndCompliance",
            "SupervisorOfHumanResourcesServices",
            "SupervisorOfHousing",
            "SupervisorOfFiles",
            "SupervisorOfOutpatientClinics",
            "SupervisorOfSocialSecurity",
            "SupervisorOfInventoryControl",
            "SupervisorOfRevenueDevelopment",
            "SupervisorOfSecurity",
            "SupervisorOfMedicalConsultation"
        };

                var supervisorsCount = allOrders.Count(order =>
                {
                    // فحص إذا كان الطلب معلق عند أي مشرف
                    return supervisorFieldNames.Any(fieldName =>
                    {
                        var supervisorValue = GetSupervisorValue(order, fieldName);
                        return supervisorValue.Contains("الطلب قيد التنفيذ") ||
                               supervisorValue.Contains("طلب إجراء من المشرف");
                    });
                });

                var stageStats = new StageStatisticsDto
                {
                    DepartmentManager = departmentManagerCount,
                    AssistantManagerA1 = assistantsCount[AssistantManagerType.A1],
                    AssistantManagerA2 = assistantsCount[AssistantManagerType.A2],
                    AssistantManagerA3 = assistantsCount[AssistantManagerType.A3],
                    AssistantManagerA4 = assistantsCount[AssistantManagerType.A4],
                    HRCoordinator = hrCoordinatorCount,  // مُصحح
                    Supervisors = supervisorsCount,      // مُصحح
                    HRManager = statusCounts.GetValueOrDefault(OrderStatus.D, 0)
                };

                // لوق تفصيلي للتحقق من الإصلاح
                _logger.LogInformation("إحصائيات المراحل المُصححة (النسخة النهائية): " +
                    "مدير القسم={DM}, A1={A1}, A2={A2}, A3={A3}, A4={A4}, " +
                    "منسق HR={HRCoord}, مشرفين={Super}, مدير HR={HRMgr}",
                    departmentManagerCount, assistantsCount[AssistantManagerType.A1], assistantsCount[AssistantManagerType.A2],
                    assistantsCount[AssistantManagerType.A3], assistantsCount[AssistantManagerType.A4],
                    hrCoordinatorCount, supervisorsCount, statusCounts.GetValueOrDefault(OrderStatus.D, 0));

                // لوق إضافي للتحقق من إصلاح المشرفين
                _logger.LogInformation("تفاصيل إضافية للمشرفين:");
                for (int i = 0; i < Math.Min(3, supervisorFieldNames.Length); i++)
                {
                    var fieldName = supervisorFieldNames[i];
                    var count = allOrders.Count(order =>
                    {
                        var value = GetSupervisorValue(order, fieldName);
                        return value.Contains("الطلب قيد التنفيذ") || value.Contains("طلب إجراء من المشرف");
                    });
                    _logger.LogInformation($"  {fieldName}: {count} طلب");
                }

                return ServiceResult<StageStatisticsDto>.Success(stageStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting stage statistics");
                return ServiceResult<StageStatisticsDto>.Failure($"حدث خطأ أثناء جلب إحصائيات المراحل: {ex.Message}");
            }
        }

        /// <summary>
        /// حساب عدد الطلبات المعلقة عند منسق الموارد البشرية - باستخدام الدالة الموجودة
        /// </summary>
        private int CountCoordinatorPendingOrders(List<Core.Models.OrdersTable> allOrders)
        {
            int count = 0;

            foreach (var order in allOrders)
            {
                // استخدام الدالة الموجودة IsCoordinatorPending
                if (IsCoordinatorPending(order))
                {
                    count++;
                }
            }

            _logger.LogInformation($"منسق الموارد البشرية: {count} طلب معلق");
            return count;
        }

        /// <summary>
        /// حساب عدد الطلبات المعلقة عند المشرفين - باستخدام الدوال الموجودة
        /// </summary>
        private int CountSupervisorsPendingOrders(List<Core.Models.OrdersTable> allOrders)
        {
            int totalCount = 0;

            // استخدام SupervisorMappings الموجود
            foreach (var kvp in SupervisorMappings)
            {
                var fieldName = kvp.Key;
                var supervisorName = kvp.Value;

                int supervisorCount = 0;

                foreach (var order in allOrders)
                {
                    // استخدام GetSupervisorValue الموجودة
                    var supervisorValue = GetSupervisorValue(order, fieldName);

                    // نفس المنطق من AnalyzePendingOrders الموجود
                    if (supervisorValue.Contains("الطلب قيد التنفيذ") ||
                        supervisorValue.Contains("طلب إجراء من المشرف"))
                    {
                        supervisorCount++;
                    }
                }

                totalCount += supervisorCount;

                if (supervisorCount > 0)
                {
                    _logger.LogInformation($"{supervisorName}: {supervisorCount} طلب معلق");
                }
            }

            _logger.LogInformation($"إجمالي المشرفين: {totalCount} طلب معلق");
            return totalCount;
        }


        public async Task<ServiceResult<List<AssistantManagerStatisticsDto>>> GetAssistantManagerStatisticsAsync()
        {
            try
            {
                var allOrders = (await _unitOfWork.Orders.GetAllAsync()).ToList();
                var allDepartments = (await _unitOfWork.Departments.GetAllAsync()).ToList();

                _logger.LogInformation($"بدء حساب إحصائيات مساعدي المدير - إجمالي الطلبات: {allOrders.Count}");

                var assistantManagers = new Dictionary<AssistantManagerType, (string Name, string Type, OrderStatus TargetStatus)>
        {
            { AssistantManagerType.A1, ("مساعد المدير للخدمات الطبية", "A1", OrderStatus.A1) },
            { AssistantManagerType.A2, ("مساعد المدير لخدمات التمريض", "A2", OrderStatus.A2) },
            { AssistantManagerType.A3, ("مساعد المدير للخدمات الإدارية والتشغيل", "A3", OrderStatus.A3) },
            { AssistantManagerType.A4, ("مساعد المدير للموارد البشرية", "A4", OrderStatus.A4) }
        };

                var statisticsList = new List<AssistantManagerStatisticsDto>();

                foreach (var assistant in assistantManagers)
                {
                    var assistantType = assistant.Key;
                    var (name, type, targetStatus) = assistant.Value;

                    // جلب الطلبات الخاصة بهذا المساعد
                    var assistantOrders = GetOrdersForSpecificAssistant(allOrders, allDepartments, assistantType, targetStatus);
                    var statistics = CalculateStatistics(assistantOrders, name, type);
                    statisticsList.Add(statistics);

                    _logger.LogInformation($"{name}: {assistantOrders.Count} طلبات");
                }

                return ServiceResult<List<AssistantManagerStatisticsDto>>.Success(
                    statisticsList.OrderByDescending(s => s.TotalOrdersProcessed).ToList());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب إحصائيات مساعدي المدير");
                return ServiceResult<List<AssistantManagerStatisticsDto>>.Failure($"حدث خطأ: {ex.Message}");
            }
        }
        /// <summary>
        /// جلب الطلبات الخاصة بمساعد محدد - باستخدام جدول الأقسام
        /// </summary>
        private List<Core.Models.OrdersTable> GetOrdersForSpecificAssistant(
            List<Core.Models.OrdersTable> allOrders,
            List<DepartmentDto> allDepartments,
            AssistantManagerType assistantType,
            OrderStatus targetStatus)
        {
            // الحصول على الأقسام المخصصة لهذا المساعد
            var assignedDepartments = allDepartments
                .Where(dept => dept.AssistantManagerId == assistantType && dept.IsActive)
                .Select(dept => dept.Name.Trim())
                .ToList();

            var assistantOrders = new List<Core.Models.OrdersTable>();

            foreach (var order in allOrders)
            {
                bool belongsToThisAssistant = false;

                // المعيار الأول: الطلب في المرحلة الحالية لهذا المساعد
                if (order.OrderStatus == targetStatus)
                {
                    belongsToThisAssistant = true;
                }
                // المعيار الثاني: الطلب من قسم مخصص لهذا المساعد + معالج منه
                else if (!string.IsNullOrEmpty(order.Department) &&
                         IsDepartmentBelongsToAssistant(order.Department, assignedDepartments))
                {
                    var confirmationText = order.ConfirmedByAssistantManager ?? "";
                    if (!string.IsNullOrWhiteSpace(confirmationText) && IsAssistantAction(confirmationText))
                    {
                        belongsToThisAssistant = true;
                    }
                }
                // المعيار الثالث: طلبات معادة/ملغاة من هذا المساعد (من أقسامه فقط)
                else if ((order.OrderStatus == OrderStatus.ReturnedByAssistantManager ||
                          order.OrderStatus == OrderStatus.CancelledByAssistantManager) &&
                         !string.IsNullOrEmpty(order.Department) &&
                         IsDepartmentBelongsToAssistant(order.Department, assignedDepartments))
                {
                    var confirmationText = order.ConfirmedByAssistantManager ?? "";
                    if (IsAssistantAction(confirmationText))
                    {
                        belongsToThisAssistant = true;
                    }
                }

                if (belongsToThisAssistant)
                {
                    assistantOrders.Add(order);
                }
            }

            return assistantOrders.GroupBy(o => o.Id).Select(g => g.First()).ToList();
        }

        /// <summary>
        /// فحص ما إذا كان القسم ينتمي للمساعد
        /// </summary>
        private bool IsDepartmentBelongsToAssistant(string orderDepartment, List<string> assignedDepartments)
        {
            if (string.IsNullOrEmpty(orderDepartment) || !assignedDepartments.Any())
                return false;

            return assignedDepartments.Any(dept =>
                orderDepartment.Trim().Equals(dept, StringComparison.OrdinalIgnoreCase) ||
                orderDepartment.Contains(dept, StringComparison.OrdinalIgnoreCase) ||
                dept.Contains(orderDepartment.Trim(), StringComparison.OrdinalIgnoreCase));
        }



        /// <summary>
        /// فحص ما إذا كان النص يحتوي على عمل من مساعد المدير
        /// </summary>
        private bool IsAssistantAction(string confirmationText)
        {
            if (string.IsNullOrWhiteSpace(confirmationText))
                return false;

            var patterns = new[]
            {
        "اعتماد بواسطة",
        "تمت الإعادة بواسطة",
        "تم الإلغاء بواسطة",
        "تم التحويل لمساعد المدير"
    };

            return patterns.Any(pattern =>
                confirmationText.Contains(pattern, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// حساب الإحصائيات الفعلية
        /// </summary>
        private AssistantManagerStatisticsDto CalculateStatistics(
            List<Core.Models.OrdersTable> orders,
            string assistantName,
            string assistantType)
        {
            int underExecution = 0, completed = 0, returned = 0, cancelled = 0, totalWorksDone = 0;

            foreach (var order in orders)
            {
                var confirmationText = order.ConfirmedByAssistantManager ?? "";

                if (string.IsNullOrWhiteSpace(confirmationText))
                {
                    underExecution++;
                }
                else if (confirmationText.Contains("اعتماد بواسطة"))
                {
                    completed++;
                    totalWorksDone++;
                }
                else if (confirmationText.Contains("تمت الإعادة بواسطة"))
                {
                    returned++;
                    totalWorksDone++;
                }
                else if (confirmationText.Contains("تم الإلغاء بواسطة"))
                {
                    cancelled++;
                    totalWorksDone++;
                }
                else if (confirmationText.Contains("تم التحويل لمساعد المدير"))
                {
                    underExecution++; // تحويل = لا يزال تحت التنفيذ
                }
                else
                {
                    underExecution++;
                }
            }

            var completedOrders = orders.Where(o =>
                !string.IsNullOrWhiteSpace(o.ConfirmedByAssistantManager) &&
                (o.ConfirmedByAssistantManager.Contains("اعتماد بواسطة") ||
                 o.ConfirmedByAssistantManager.Contains("تمت الإعادة بواسطة") ||
                 o.ConfirmedByAssistantManager.Contains("تم الإلغاء بواسطة")))
                .ToList();

            var statistics = new AssistantManagerStatisticsDto
            {
                AssistantManagerName = assistantName,
                AssistantManagerType = assistantType,
                UnderExecution = underExecution,
                Completed = completed,
                Returned = returned,
                Cancelled = cancelled,
                TotalWorksDone = totalWorksDone,
                AverageCompletionTime = CalculateAverageTime(completedOrders)
            };

            CalculateTimeStatistics(statistics, completedOrders);
            return statistics;
        }
        // <summary>
        /// حساب متوسط وقت الإنجاز - محدث بحساب أيام العمل والتواريخ الهجرية
        /// </summary>
        private double CalculateAverageTime(List<Core.Models.OrdersTable> completedOrders)
        {
            if (!completedOrders.Any()) return 0;

            var totalBusinessDays = 0.0;
            var validOrders = 0;

            foreach (var order in completedOrders)
            {
                var confirmationText = order.ConfirmedByAssistantManager ?? "";

                // استخراج التاريخ الأخير من النص (تاريخ الإنجاز)
                var completionDate = ExtractLastDate(confirmationText);

                if (completionDate.HasValue)
                {
                    var businessDays = CalculateBusinessDays(order.CreatedAt, completionDate.Value);
                    if (businessDays >= 0)
                    {
                        totalBusinessDays += businessDays;
                        validOrders++;
                    }
                }
            }

            return validOrders > 0 ? Math.Round(totalBusinessDays / validOrders, 1) : 1.0;
        }
        /// <summary>
        /// استخراج أول تاريخ من النص
        /// </summary>
        private DateTime? ExtractFirstDate(string text)
        {
            if (string.IsNullOrEmpty(text))
            {
                _logger.LogWarning("❌ نص فارغ لاستخراج التاريخ");
                return null;
            }

            // نمط للتواريخ الهجرية (مثال: 1447-03-01)
            var pattern = @"\d{4}-\d{2}-\d{2}";
            var matches = Regex.Matches(text, pattern);

            if (matches.Count > 0)
            {
                var firstMatch = matches[0].Value;
                _logger.LogInformation($"📅 تم العثور على تاريخ: {firstMatch}");

                // محاولة تحليل التاريخ الهجري
                try
                {
                    var parts = firstMatch.Split('-');
                    if (parts.Length == 3 &&
                        int.TryParse(parts[0], out int year) &&
                        int.TryParse(parts[1], out int month) &&
                        int.TryParse(parts[2], out int day))
                    {
                        // تحويل الهجري إلى ميلادي (هذه مجرد مثال،可能需要 مكتبة متخصصة)
                        var hijriDate = new DateTime(year, month, day, new HijriCalendar());
                        _logger.LogInformation($"📅 التاريخ المحول: {hijriDate:yyyy-MM-dd}");
                        return hijriDate;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"❌ خطأ في تحويل التاريخ الهجري: {firstMatch}");
                }

                // إذا فشل التحويل، حاول التحليل كتاريخ ميلادي
                if (DateTime.TryParse(firstMatch, out DateTime date))
                    return date;

                _logger.LogWarning($"❌ تعذر تحليل التاريخ: {firstMatch}");
            }
            else
            {
                _logger.LogWarning($"❌ لم يتم العثور على تاريخ في النص: {text}");
            }

            return null;
        }


        /// <summary>
        /// استخراج آخر تاريخ من النص (للإنجاز)
        /// </summary>
        private DateTime? ExtractLastDate(string text)
        {
            var dates = ExtractAllDatesFromText(text);
            return dates.LastOrDefault();
        }




        /// <summary>
        /// حساب الإحصائيات الزمنية
        /// </summary>
        private void CalculateTimeStatistics(AssistantManagerStatisticsDto stats, List<Core.Models.OrdersTable> completedOrders)
        {
            var now = DateTime.Now;
            var startOfMonth = new DateTime(now.Year, now.Month, 1);
            var start3MonthsAgo = now.AddMonths(-3);
            var start6MonthsAgo = now.AddMonths(-6);
            var startOfYear = now.AddYears(-1);

            stats.ThisMonthOperations = completedOrders.Count(o => o.CreatedAt >= startOfMonth);
            stats.Last3MonthsOperations = completedOrders.Count(o => o.CreatedAt >= start3MonthsAgo);
            stats.Last6MonthsOperations = completedOrders.Count(o => o.CreatedAt >= start6MonthsAgo);
            stats.LastYearOperations = completedOrders.Count(o => o.CreatedAt >= startOfYear);

            stats.TrendDirection = DetermineTrend(stats);
        }

        /// <summary>
        /// تحديد اتجاه الأداء
        /// </summary>
        private string DetermineTrend(AssistantManagerStatisticsDto stats)
        {
            if (stats.Last3MonthsOperations == 0) return "مستقر";

            var avgMonthly = stats.Last3MonthsOperations / 3.0;

            if (stats.ThisMonthOperations > avgMonthly * 1.2)
                return "متزايد";
            else if (stats.ThisMonthOperations < avgMonthly * 0.8)
                return "متناقص";
            else
                return "مستقر";
        }


















        //نهاية اكواد مساعد المدير


        /// <summary>
        /// لإحصائيات منسق الموارد البشرية - مُحدث مع الإحصائيات الزمنية
        /// </summary>
        public async Task<ServiceResult<List<HRCoordinatorReportDto>>> GetHRCoordinatorStatisticsAsync()
        {
            try
            {
                var allOrders = (await _unitOfWork.Orders.GetAllAsync()).ToList();

                _logger.LogInformation($"إجمالي الطلبات لإحصائيات منسقي الموارد البشرية: {allOrders.Count}");

                var ordersWithCoordinators = allOrders.Where(o =>
                    !string.IsNullOrEmpty(o.ConfirmedByCoordinator)).ToList();

                _logger.LogInformation($"الطلبات مع منسقي الموارد البشرية: {ordersWithCoordinators.Count}");

                var incomingOrdersCount = ordersWithCoordinators.Count(o =>
                    DetermineHRCoordinatorActionType(o.ConfirmedByCoordinator ?? string.Empty) == "طلبات قادمة");

                LogHRCoordinatorSampleData(ordersWithCoordinators.Take(5).ToList());

                // ✅ استخدام الاسم الصحيح للدالة
                var coordinatorStats = CalculateHRCoordinatorStatistics(ordersWithCoordinators);

                if (coordinatorStats.Any())
                {
                    var globalStats = new HRCoordinatorReportDto
                    {
                        CoordinatorName = "___GLOBAL_STATS___",
                        IncomingOrders = incomingOrdersCount
                    };
                    coordinatorStats.Insert(0, globalStats);
                }

                // 🔧 فلترة نهائية محسنة - استبعاد جميع الحالات غير المرغوبة
                var filteredStats = coordinatorStats.Where(c =>
                    c.CoordinatorName == "___GLOBAL_STATS___" ||
                    (c.TotalOperations > 0 &&
                     !string.IsNullOrEmpty(c.CoordinatorName) &&
                     c.CoordinatorName != "طلب إجراء من المنسق" &&
                     c.CoordinatorName != "طلبات قادمة" &&
                     c.CoordinatorName != "غير محدد" &&
                     c.CoordinatorName != "يتطلب إجراء" &&
                     !c.CoordinatorName.Contains("طلب إجراء") &&
                     !c.CoordinatorName.Contains("إجراء من المنسق")))
                .ToList();

                // ✨ طباعة ملخص الإحصائيات المهمة (مثل المديرين)
                var actualCoordinators = filteredStats.Where(c => c.CoordinatorName != "___GLOBAL_STATS___").Take(3);
                foreach (var coordinator in actualCoordinators)
                {
                    _logger.LogInformation($"🏆 {coordinator.CoordinatorName}: " +
                        $"عمليات={coordinator.TotalOperations}, " +
                        $"أعمال فعلية={coordinator.TotalWorksDone}, " +
                        $"متوسط عمل/طلب={coordinator.WorksPerOrder:F1}, " +
                        $"نسبة نجاح={coordinator.SuccessRate:F1}%");
                }

                _logger.LogInformation($"✅ تم معالجة {filteredStats.Count - 1} منسق بنجاح (+ الإحصائيات العامة)");

                return ServiceResult<List<HRCoordinatorReportDto>>.Success(filteredStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في جلب إحصائيات منسقي الموارد البشرية");
                return ServiceResult<List<HRCoordinatorReportDto>>.Failure($"حدث خطأ أثناء جلب إحصائيات منسقي الموارد البشرية: {ex.Message}");
            }
        }

        // ✅ تحديث CalculateHRCoordinatorStatistics - مع الإحصائيات الزمنية الجديدة
        private List<HRCoordinatorReportDto> CalculateHRCoordinatorStatistics(List<Core.Models.OrdersTable> ordersWithCoordinators)
        {
            var coordinatorStatsDict = new Dictionary<string, HRCoordinatorReportDto>();

            // 🔧 فلترة محسنة - استبعاد جميع الحالات غير المرغوبة
            var coordinatorGroups = ordersWithCoordinators
                .GroupBy(o => ExtractCoordinatorName(o.ConfirmedByCoordinator ?? string.Empty))
                .Where(g => !string.IsNullOrEmpty(g.Key) &&
                           g.Key != "___EXCLUDED___" &&
                           g.Key != "الطلبات القادمة" &&
                           g.Key != "غير محدد" &&
                           g.Key != "طلب إجراء من المنسق" &&
                           g.Key != "طلبات قادمة" &&
                           g.Key != "يتطلب إجراء" &&
                           !g.Key.Contains("طلب إجراء") &&
                           !g.Key.Contains("إجراء من المنسق"))
                .ToList();

            foreach (var group in coordinatorGroups)
            {
                var coordinatorName = group.Key;
                var ordersForCoordinator = group.ToList();

                var stats = new HRCoordinatorReportDto
                {
                    CoordinatorName = coordinatorName,
                    // ✨ تهيئة الخصائص الزمنية الجديدة
                    ThisMonthOperations = 0,
                    Last3MonthsOperations = 0,
                    Last6MonthsOperations = 0,
                    LastYearOperations = 0,
                    ThisWeekOperations = 0, // للتوافق مع الكود القديم
                    MultiStageOrders = 0,
                    TotalWorksDone = 0,
                    TrendDirection = "مستقر"
                };

                // معالجة مجمعة للطلبات مع الإحصائيات الزمنية
                foreach (var order in ordersForCoordinator)
                {
                    var coordinatorText = order.ConfirmedByCoordinator ?? string.Empty;
                    var actionType = DetermineHRCoordinatorActionType(coordinatorText);
                    UpdateCoordinatorStats(stats, order, actionType);
                }

                // حساب متوسط زمن الإنجاز
                stats.AverageCompletionTime = CalculateCoordinatorAverageTime(ordersForCoordinator, coordinatorName);

                // ✅ تحديد اتجاه الأداء المحسن (مرة واحدة فقط)
                stats.TrendDirection = DetermineCoordinatorTrend(stats.ThisMonthOperations, stats.Last3MonthsOperations, stats.LastYearOperations);

                coordinatorStatsDict[coordinatorName] = stats;

                // طباعة معلومات تشخيصية للمنسق مع الإحصائيات الزمنية
                _logger.LogInformation($"منسق: {coordinatorName} - منجز: {stats.Completed}, مستعاد: {stats.Restored}, معاد تكليف: {stats.Reassigned}, تحويل مباشر: {stats.DirectToManager}, يتطلب إجراء: {stats.NeedsAction}, ملغي: {stats.Cancelled}");
                _logger.LogInformation($"📅 {coordinatorName}: شهر={stats.ThisMonthOperations}, ٣ أشهر={stats.Last3MonthsOperations}, ٦ أشهر={stats.Last6MonthsOperations}, سنة={stats.LastYearOperations}, الاتجاه={stats.TrendDirection}");
                _logger.LogInformation($"💼 {coordinatorName}: أعمال فعلية={stats.TotalWorksDone}, متوسط عمل/طلب={stats.WorksPerOrder:F1}, طلبات متعددة={stats.MultiStageOrders}");
                _logger.LogInformation($"🏆 {coordinatorName}: معدل الإنجاز الفعلي={stats.ActualCompletionRateDisplay}, منجز فعلياً={stats.ActualCompleted}/{stats.TotalProcessedOrders}");
            }

            return coordinatorStatsDict.Values.OrderBy(c => c.CoordinatorName).ToList();
        }

        /// <summary>
        /// تحديث الإحصائيات الزمنية لمنسق الموارد البشرية (مثل المديرين)
        /// </summary>
        private void UpdateCoordinatorTimeStats(HRCoordinatorReportDto stats, Core.Models.OrdersTable order)
        {
            try
            {
                var now = DateTime.Now;
                var orderDate = order.CreatedAt;

                // حساب بداية الفترات الزمنية المختلفة
                var startOfMonth = new DateTime(now.Year, now.Month, 1);
                var start3MonthsAgo = now.AddMonths(-3);
                var start6MonthsAgo = now.AddMonths(-6);
                var startOfYear = now.AddYears(-1);
                var startOfWeek = now.AddDays(-(int)now.DayOfWeek);

                // إحصائيات هذا الشهر
                if (orderDate >= startOfMonth)
                {
                    stats.ThisMonthOperations++;
                }

                // إحصائيات آخر 3 أشهر
                if (orderDate >= start3MonthsAgo)
                {
                    stats.Last3MonthsOperations++;
                }

                // إحصائيات آخر 6 أشهر
                if (orderDate >= start6MonthsAgo)
                {
                    stats.Last6MonthsOperations++;
                }

                // إحصائيات آخر سنة
                if (orderDate >= startOfYear)
                {
                    stats.LastYearOperations++;
                }

                // إحصائيات هذا الأسبوع (للتوافق مع الكود القديم)
                if (orderDate >= startOfWeek)
                {
                    stats.ThisWeekOperations++;
                }

                // فحص الطلبات متعددة المراحل
                var coordinatorText = order.ConfirmedByCoordinator ?? string.Empty;
                if (CountCoordinatorActions(coordinatorText) > 1)
                {
                    stats.MultiStageOrders++;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "خطأ في تحديث الإحصائيات الزمنية لمنسق الموارد البشرية");
            }
        }


        /// <summary>
        /// تحديد اتجاه الأداء للمنسق )
        /// </summary>
        private string DetermineCoordinatorTrend(int thisMonthCount, int last3MonthsCount, int lastYearCount)
        {
            if (thisMonthCount == 0 && last3MonthsCount == 0)
                return "مستقر";

            // حساب متوسط الأشهر
            var avgMonthlyInQuarter = last3MonthsCount / 3.0;
            var avgMonthlyInYear = lastYearCount / 12.0;

            // مقارنة هذا الشهر مع المتوسطات
            if (thisMonthCount > avgMonthlyInQuarter * 1.3) // زيادة 30% عن متوسط الربع
                return "متزايد";
            else if (thisMonthCount < avgMonthlyInYear * 0.7) // نقص 30% عن متوسط السنة
                return "متناقص";
            else
                return "مستقر";
        }


        // ✅ . ExtractCoordinatorName - تحسين مع معالجة أفضل للأخطاء
        private string ExtractCoordinatorName(string coordinatorText)
        {
            if (string.IsNullOrWhiteSpace(coordinatorText))
                return "غير محدد";

            try
            {
                coordinatorText = coordinatorText.Trim();

                if (coordinatorText.Contains("طلب إجراء من المنسق:"))
                {
                    var parts = coordinatorText.Split('|');
                    if (parts.Length >= 2)
                    {
                        var middlePart = parts[1].Trim();
                        if (middlePart.Contains("طلب إجراء من المنسق:"))
                        {
                            var name = middlePart.Replace("طلب إجراء من المنسق:", "").Trim();
                            if (!string.IsNullOrWhiteSpace(name))
                                return name; // إرجاع اسم المنسق
                        }
                    }
                }

                // فلترة الحالات التي لا تُعتبر منسقين حقيقيين
                var excludedPatterns = new[]
                {
            "تم التحويل للمنسق",
            "طلبات قادمة",
            "غير محدد",
            "فارغ"
        };

                // فحص إذا كان النص يحتوي على أي من الأنماط المستبعدة (بدون طلب إجراء)
                foreach (var pattern in excludedPatterns)
                {
                    if (coordinatorText.ToLower().Contains(pattern.ToLower()))
                    {
                        return "___EXCLUDED___";
                    }
                }

                if (coordinatorText.Contains("اعتماد بواسطة:"))
                {
                    var parts = coordinatorText.Split('|');
                    if (parts.Length >= 2)
                    {
                        var middlePart = parts[1].Trim();
                        if (middlePart.Contains("اعتماد بواسطة:"))
                        {
                            var name = middlePart.Replace("اعتماد بواسطة:", "").Trim();
                            if (!string.IsNullOrWhiteSpace(name))
                                return name;
                        }
                    }
                }

                if (coordinatorText.Contains("تم التحويل مباشر للمدير:"))
                {
                    var parts = coordinatorText.Split('|');
                    if (parts.Length >= 2)
                    {
                        var middlePart = parts[1].Trim();
                        if (middlePart.Contains("تم التحويل مباشر للمدير:"))
                        {
                            var name = middlePart.Replace("تم التحويل مباشر للمدير:", "").Trim();
                            if (!string.IsNullOrWhiteSpace(name))
                                return name;
                        }
                    }
                }

                if (coordinatorText.Contains("تمت الإعادة بواسطة:"))
                {
                    var parts = coordinatorText.Split('|');
                    if (parts.Length >= 2)
                    {
                        var middlePart = parts[1].Trim();
                        if (middlePart.Contains("تمت الإعادة بواسطة:"))
                        {
                            var name = middlePart.Replace("تمت الإعادة بواسطة:", "").Trim();
                            if (!string.IsNullOrWhiteSpace(name))
                                return name;
                        }
                    }
                }

                if (coordinatorText.Contains("استعادة بواسطة:"))
                {
                    var parts = coordinatorText.Split('|');
                    if (parts.Length >= 2)
                    {
                        var middlePart = parts[1].Trim();
                        if (middlePart.Contains("استعادة بواسطة:"))
                        {
                            var name = middlePart.Replace("استعادة بواسطة:", "").Trim();
                            if (!string.IsNullOrWhiteSpace(name))
                                return name;
                        }
                    }
                }

                if (coordinatorText.Contains("تم الالغاء بواسطة:"))
                {
                    var parts = coordinatorText.Split('|');
                    if (parts.Length >= 2)
                    {
                        var middlePart = parts[1].Trim();
                        if (middlePart.Contains("تم الالغاء بواسطة:"))
                        {
                            var name = middlePart.Replace("تم الالغاء بواسطة:", "").Trim();
                            if (!string.IsNullOrWhiteSpace(name))
                                return name;
                        }
                    }
                }

                // البحث عن أي نمط عام "بواسطة:"
                if (coordinatorText.Contains("بواسطة:"))
                {
                    var parts = coordinatorText.Split('|');
                    foreach (var part in parts)
                    {
                        var trimmedPart = part.Trim();
                        if (trimmedPart.Contains("بواسطة:"))
                        {
                            var colonIndex = trimmedPart.IndexOf("بواسطة:");
                            if (colonIndex != -1)
                            {
                                var name = trimmedPart.Substring(colonIndex + "بواسطة:".Length).Trim();
                                if (!string.IsNullOrWhiteSpace(name))
                                    return name;
                            }
                        }
                    }
                }

                return "___EXCLUDED___";
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"خطأ في استخراج اسم المنسق من النص: {coordinatorText}. الخطأ: {ex.Message}");
                return "___EXCLUDED___";
            }
        }
        private string DetermineHRCoordinatorActionType(string coordinatorText)
        {
            if (string.IsNullOrWhiteSpace(coordinatorText))
                return "غير محدد";

            coordinatorText = coordinatorText.ToLower();

            // ترتيب الفحص من الأكثر تحديداً للأقل

            // ✅ النمط: "تم التحويل مباشر للمدير:" (بدون "بواسطة")
            if (coordinatorText.Contains("تم التحويل مباشر للمدير:"))
                return "تحويل مباشر للمدير";

            // ✅ النمط: "اعتماد بواسطة:"
            if (coordinatorText.Contains("اعتماد بواسطة:"))
                return "منجز";

            // ✅ النمط: "تمت الإعادة بواسطة:"
            if (coordinatorText.Contains("تمت الإعادة بواسطة:"))
                return "معاد تكليف";

            // النمط: "تم التحويل للمنسق" (بدون ذكر شخص محدد)
            if (coordinatorText.Contains("تم التحويل للمنسق") && !coordinatorText.Contains("بواسطة"))
                return "طلبات قادمة";

            // أنماط أخرى (للتوافق مع البيانات القديمة)
            if (coordinatorText.Contains("تمت استعادة الطلب") || coordinatorText.Contains("استعادة"))
                return "تمت استعادة الطلب";

            if (coordinatorText.Contains("إلغاء") || coordinatorText.Contains("ملغي"))
                return "ملغي";

            if (coordinatorText.Contains("يتطلب إجراء") || coordinatorText.Contains("إجراء"))
                return "يتطلب إجراء";

            // الافتراضي
            return "منجز";
        }


        // ✅ تصحيح UpdateCoordinatorStats - الإصدار النهائي مع إصلاح العد المزدوج
        private void UpdateCoordinatorStats(HRCoordinatorReportDto stats, Core.Models.OrdersTable order, string actionType)
        {
            // ✨ متغير لتتبع إضافة الأعمال
            bool workAdded = false;

            // تحديث الإحصائيات بناءً على نوع العملية
            switch (actionType)
            {
                case "تمت استعادة الطلب":
                    stats.Restored++;
                    stats.TotalWorksDone++; // ✅ عمل واحد
                    workAdded = true;
                    break;
                case "معاد تكليف":
                    stats.Reassigned++;
                    stats.TotalWorksDone++; // ✅ عمل واحد
                    workAdded = true;
                    break;
                case "ملغي":
                    stats.Cancelled++;
                    stats.TotalWorksDone++; // ✅ عمل واحد (المنسق راجع واطلع)
                    workAdded = true;
                    break;
                case "يتطلب إجراء":
                    stats.NeedsAction++;
                    stats.TotalWorksDone++; // ✅ عمل واحد
                    workAdded = true;
                    break;
                case "تحويل مباشر للمدير":
                    // ✅ يُحسب للمنسق الذي قام بالتحويل
                    stats.DirectToManager++;
                    stats.TotalWorksDone++; // ✅ عمل واحد
                    workAdded = true;
                    break;
                case "طلبات قادمة":
                    // ✅ هذه الحالة تُحسب في الإحصائيات العامة فقط
                    // ❌ لا نضيف أي عمل لأنها ليست عمل فعلي للمنسق
                    workAdded = false;
                    break;
                case "منجز":
                default:
                    stats.Completed++;
                    stats.TotalWorksDone++; // ✅ عمل واحد
                    workAdded = true;
                    break;
            }

            // ✨ حساب الطلبات متعددة المراحل (مفقود في الكود الأصلي)
            var coordinatorText = order.ConfirmedByCoordinator ?? string.Empty;
            var actionCount = CountCoordinatorActions(coordinatorText);
            if (actionCount > 1)
            {
                stats.MultiStageOrders++;
                // إضافة الأعمال الإضافية للطلبات متعددة المراحل
                if (workAdded) // فقط إذا كان هناك عمل أساسي
                {
                    stats.TotalWorksDone += (actionCount - 1); // أعمال إضافية
                }
            }

            // فحص دقيق لحالة الطلب (بدون تكرار في العد)
            var orderStatusStr = order.OrderStatus.ToString();

            if (orderStatusStr.Contains("ActionRequired") ||
                orderStatusStr.Equals("ActionRequired", StringComparison.OrdinalIgnoreCase))
            {
                if (actionType != "يتطلب إجراء") // تجنب التكرار
                {
                    stats.NeedsAction++;
                    if (!workAdded) // ✅ أضف عمل فقط إذا لم يتم إضافته من قبل
                    {
                        stats.TotalWorksDone++;
                        workAdded = true;
                    }
                }
            }

            if (order.OrderStatus == OrderStatus.CancelledByCoordinator)
            {
                if (actionType != "ملغي") // تجنب التكرار
                {
                    stats.Cancelled++;
                    if (!workAdded) // ✅ أضف عمل فقط إذا لم يتم إضافته من قبل
                    {
                        stats.TotalWorksDone++;
                        workAdded = true;
                    }
                }
            }

            // ✅ فحص شامل لنوع التحويل
            var transferType = (order.TransferType ?? string.Empty).ToLower();

            if (transferType.Contains("تلقائي") || transferType.Contains("auto") || transferType.Contains("automatic"))
            {
                stats.AutomaticTransfers++;
            }
            else if (transferType.Contains("يدوي") || transferType.Contains("manual") || transferType.Contains("manually"))
            {
                stats.ManualTransfers++;
            }
            else if (transferType.Contains("directtomanager") || transferType.Contains("مباشر"))
            {
                stats.ManualTransfers++; // التحويل المباشر للمدير يُعتبر يدوي
            }
            else if (!string.IsNullOrWhiteSpace(order.TransferType))
            {
                stats.ManualTransfers++; // أي نوع تحويل آخر يُعتبر يدوي افتراضياً
            }

            // ✨ حساب الإحصائيات الزمنية الجديدة
            UpdateCoordinatorTimeStats(stats, order);
        }

        // ✨ دالة مساعدة جديدة لحساب عدد الإجراءات في النص
        private int CountCoordinatorActions(string coordinatorText)
        {
            if (string.IsNullOrWhiteSpace(coordinatorText))
                return 0;

            var actionPatterns = new[]
            {
        "طلب إجراء من المنسق:",
        "اعتماد بواسطة:",
        "تم التحويل مباشر للمدير:",
        "تمت الإعادة بواسطة:",
        "استعادة بواسطة:",
        "تم الالغاء بواسطة:"
    };

            int count = 0;
            var lowerText = coordinatorText.ToLower();

            foreach (var pattern in actionPatterns)
            {
                if (lowerText.Contains(pattern.ToLower()))
                {
                    count++;
                }
            }

            return Math.Max(count, 1); // على الأقل إجراء واحد
        }


        // ✅ إضافة helper method لتحديد نوع النمط
        private string GetPatternType(string coordinatorText)
        {
            if (string.IsNullOrWhiteSpace(coordinatorText))
                return "فارغ";

            if (coordinatorText.Contains("طلب إجراء من المنسق:"))
                return "نمط طلب الإجراء";

            if (coordinatorText.Contains("اعتماد بواسطة:"))
                return "نمط الاعتماد";

            if (coordinatorText.Contains("تم التحويل مباشر للمدير:"))
                return "نمط التحويل المباشر للمدير";

            if (coordinatorText.Contains("تمت الإعادة بواسطة:"))
                return "نمط الإعادة";

            if (coordinatorText.Contains("استعادة بواسطة:"))
                return "نمط الاستعادة";

            if (coordinatorText.Contains("تم الالغاء بواسطة:"))
                return "نمط الإلغاء";

            if (coordinatorText.Contains("تم التحويل للمنسق") && !coordinatorText.Contains("بواسطة"))
                return "نمط الطلبات القادمة";

            return "نمط غير محدد";
        }
        private void LogHRCoordinatorSampleData(List<Core.Models.OrdersTable> sampleOrders)
        {
            try
            {
                _logger.LogInformation("=== عينة مفصلة من بيانات منسقي الموارد البشرية (الأنماط الفعلية) ===");

                foreach (var order in sampleOrders)
                {
                    var coordinatorText = order.ConfirmedByCoordinator ?? "فارغ";
                    var extractedName = ExtractCoordinatorName(coordinatorText);
                    var actionType = DetermineHRCoordinatorActionType(coordinatorText);

                    _logger.LogInformation($"طلب {order.Id}:");
                    _logger.LogInformation($"  - النص الأصلي: '{coordinatorText}'");
                    _logger.LogInformation($"  - النمط المحدد: {GetPatternType(coordinatorText)}");
                    _logger.LogInformation($"  - الاسم المستخرج: '{extractedName}'");
                    _logger.LogInformation($"  - نوع العملية: '{actionType}'");
                    _logger.LogInformation($"  - نوع التحويل: '{order.TransferType}'");
                    _logger.LogInformation($"  - حالة الطلب: '{order.OrderStatus}'");
                    _logger.LogInformation("  ---");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في طباعة عينة البيانات المفصلة");
            }
        }
        // ✅ . CalculateCoordinatorAverageTime - تحسين الأداء
        private double CalculateCoordinatorAverageTime(List<Core.Models.OrdersTable> coordinatorOrders, string coordinatorName)
        {
            try
            {
                // ✅ تحسين: استخدام الطلبات المفلترة مسبقاً - يشمل التحويل المباشر
                var completedOrders = coordinatorOrders.Where(o =>
                {
                    var actionType = DetermineHRCoordinatorActionType(o.ConfirmedByCoordinator ?? string.Empty);
                    return actionType == "منجز" || actionType == "تحويل مباشر للمدير"; // ✅ شمل التحويل المباشر
                }).ToList();

                if (!completedOrders.Any())
                    return 0;

                var validDurations = new List<double>();

                foreach (var order in completedOrders)
                {
                    try
                    {
                        var confirmationText = order.ConfirmedByCoordinator ?? string.Empty;
                        var dates = ExtractDatesFromCoordinatorText(confirmationText);

                        if (dates.startDate.HasValue && dates.endDate.HasValue)
                        {
                            var businessDays = CalculateBusinessDays(dates.startDate.Value, dates.endDate.Value);
                            if (businessDays >= 0 && businessDays <= 365) // فلترة القيم غير المنطقية
                            {
                                validDurations.Add(businessDays);
                            }
                        }
                        else
                        {
                            // إذا لم نجد التواريخ، نستخدم تاريخ الإنشاء والتاريخ الحالي
                            var businessDays = CalculateBusinessDays(order.CreatedAt, DateTime.Now);
                            if (businessDays >= 0 && businessDays <= 365)
                            {
                                validDurations.Add(businessDays);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug(ex, "خطأ في حساب زمن الإنجاز للطلب {OrderId}", order.Id);
                    }
                }

                return validDurations.Any() ? Math.Round(validDurations.Average(), 1) : 0;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "خطأ في حساب متوسط زمن الإنجاز للمنسق {CoordinatorName}", coordinatorName);
                return 0;
            }
        }
        // ✅ . ExtractDatesFromCoordinatorText - تحسين مع دعم تنسيقات متعددة
        private (DateTime? startDate, DateTime? endDate) ExtractDatesFromCoordinatorText(string confirmationText)
        {
            if (string.IsNullOrWhiteSpace(confirmationText))
                return (null, null);

            try
            {
                var parts = confirmationText.Split('|');

                DateTime? startDate = null;
                DateTime? endDate = null;

                // البحث عن التواريخ في الجزء الأول والأخير
                if (parts.Length >= 1)
                {
                    var firstPart = parts[0].Trim();
                    if (DateTime.TryParse(firstPart, out var parsedStartDate))
                    {
                        startDate = parsedStartDate;
                    }
                }

                if (parts.Length >= 3)
                {
                    var lastPart = parts[2].Trim();
                    if (DateTime.TryParse(lastPart, out var parsedEndDate))
                    {
                        endDate = parsedEndDate;
                    }
                }

                // التأكد من أن تاريخ النهاية أكبر من تاريخ البداية
                if (startDate.HasValue && endDate.HasValue && startDate > endDate)
                {
                    (startDate, endDate) = (endDate, startDate);
                }

                return (startDate, endDate);
            }
            catch (Exception ex)
            {
                _logger.LogDebug(ex, "خطأ في استخراج التواريخ من النص: {Text}", confirmationText);
                return (null, null);
            }
        }


        /// <summary>
        /// الحصول على إحصائيات مديري الموارد البشرية
        /// </summary>

        public async Task<ServiceResult<List<HRManagerReportDto>>> GetHRManagerStatisticsAsync()
        {
            try
            {
                var allOrders = (await _unitOfWork.Orders.GetAllAsync()).ToList();
                _logger.LogInformation($"🔍 معالجة {allOrders.Count} طلب لإحصائيات مديري الموارد البشرية");

                // فلترة الطلبات التي لها تعامل مع مديري الموارد البشرية
                var ordersWithManagers = allOrders.Where(o =>
                    !string.IsNullOrEmpty(o.HumanResourcesManager)).ToList();

                _logger.LogInformation($"📋 الطلبات مع مديري الموارد البشرية: {ordersWithManagers.Count}");

                // حساب الطلبات تحت التنفيذ لجميع المديرين
                var underExecutionOrders = allOrders.Count(o => o.OrderStatus == OrderStatus.D);

                // استخراج أسماء المديرين وحساب الإحصائيات المحسنة
                var managerStats = CalculateEnhancedHRManagerStatistics(ordersWithManagers, underExecutionOrders);

                // ✅ فلترة النتائج وترتيبها - مُصحح للـ DTO الجديد
                var filteredStats = managerStats
                    .Where(m => m.TotalOrdersProcessed > 0 || m.TotalWorksDone > 0 || !string.IsNullOrEmpty(m.ManagerName)) // ✅ مُصحح
                    .OrderByDescending(m => m.TotalOrdersProcessed) // ✅ الطلبات المُعالجة
                    .ThenByDescending(m => m.TotalWorksDone)        // ✅ الأعمال الفعلية
                    .ThenByDescending(m => m.ProcessingRate)        // ✅ نسبة المعالجة
                    .ToList();

                _logger.LogInformation($"✅ تم معالجة {filteredStats.Count} مدير بنجاح");

                // ✅ طباعة ملخص الإحصائيات المهمة
                foreach (var manager in filteredStats.Take(3)) // أفضل 3 مديرين
                {
                    _logger.LogInformation($"🏆 {manager.ManagerName}: " +
                        $"طلبات معالجة={manager.TotalOrdersProcessed}, " +
                        $"أعمال فعلية={manager.TotalWorksDone}, " +
                        $"متوسط عمل/طلب={manager.WorksPerOrder:F1}, " +
                        $"نسبة معالجة={manager.ProcessingRate:F1}%");
                }

                return ServiceResult<List<HRManagerReportDto>>.Success(filteredStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في جلب إحصائيات مديري الموارد البشرية");
                return ServiceResult<List<HRManagerReportDto>>.Failure($"حدث خطأ أثناء جلب الإحصائيات: {ex.Message}");
            }
        }

        /// <summary>
        /// حساب الإحصائيات المحسنة لمديري الموارد البشرية - مُصحح
        /// </summary>
        private List<HRManagerReportDto> CalculateEnhancedHRManagerStatistics(List<Core.Models.OrdersTable> ordersWithManagers, int totalUnderExecution)
        {
            var managerStatsDict = new Dictionary<string, HRManagerReportDto>();

            try
            {
                // معالجة مجمعة ومحسنة للطلبات - مع فلترة محسنة
                var managerGroups = ordersWithManagers
                    .GroupBy(o => ExtractHRManagerName(o.HumanResourcesManager ?? string.Empty))
                    .Where(g => !string.IsNullOrEmpty(g.Key) &&
                               g.Key != "___EXCLUDED___" &&
                               g.Key.Trim() != "المدير" &&
                               g.Key != "غير محدد" &&
                               g.Key != "قيد المراجعة")
                    .ToList();

                _logger.LogInformation($"📊 تم العثور على {managerGroups.Count} مدير فريد بعد الفلترة");

                // 🏆 توزيع الطلبات تحت التنفيذ بالتساوي على جميع المديرين الفعليين
                int underExecutionPerManager = managerGroups.Count > 0 ?
                    (int)Math.Ceiling((double)totalUnderExecution / managerGroups.Count) : 0;

                int remainingUnderExecution = totalUnderExecution;

                foreach (var group in managerGroups)
                {
                    var managerName = group.Key;
                    var ordersForManager = group.ToList();

                    var stats = new HRManagerReportDto
                    {
                        ManagerName = managerName,
                        // ✅ تهيئة الخصائص الزمنية المحسنة (مثل المشرفين)
                        ThisMonthOperations = 0,
                        Last3MonthsOperations = 0,
                        Last6MonthsOperations = 0,
                        LastYearOperations = 0,
                        MultiStageOrders = 0,
                        TotalWorksDone = 0,
                        TrendDirection = "مستقر"
                    };

                    // معالجة مجمعة للطلبات مع تحسينات
                    foreach (var order in ordersForManager)
                    {
                        var managerText = order.HumanResourcesManager ?? string.Empty;
                        var actionType = DetermineHRManagerActionType(managerText, order.OrderStatus);
                        UpdateEnhancedHRManagerStats(stats, order, actionType);
                    }

                    // ✅ توزيع الطلبات تحت التنفيذ بطريقة ذكية
                    if (remainingUnderExecution > 0)
                    {
                        int assignedToThis = Math.Min(underExecutionPerManager, remainingUnderExecution);
                        stats.UnderExecution = assignedToThis;
                        remainingUnderExecution -= assignedToThis;
                    }

                    // ✅ حساب متوسط زمن الإنجاز المحسن
                    stats.AverageCompletionTime = CalculateHRManagerAverageTime(ordersForManager, managerName);

                    // ✅ تحديد اتجاه الأداء المحسن (بناءً على البيانات الجديدة)
                    stats.TrendDirection = DetermineHRManagerTrend(stats.ThisMonthOperations, stats.Last3MonthsOperations, stats.LastYearOperations);

                    managerStatsDict[managerName] = stats;

                    // ✅ طباعة معلومات تشخيصية مُحدثة للمدير
                    _logger.LogInformation($"👤 {managerName}: تحت التنفيذ={stats.UnderExecution}, منجز={stats.Completed}, تغيير حالة={stats.StatusChanged}, معاد={stats.Returned}, ملغي={stats.Cancelled}");
                    _logger.LogInformation($"📊 {managerName}: طلبات معالجة={stats.TotalOrdersProcessed}, أعمال فعلية={stats.TotalWorksDone}, متوسط أعمال/طلب={stats.WorksPerOrder:F1}");
                    _logger.LogInformation($"📅 {managerName}: شهر={stats.ThisMonthOperations}, ٣ أشهر={stats.Last3MonthsOperations}, ٦ أشهر={stats.Last6MonthsOperations}, سنة={stats.LastYearOperations}, الاتجاه={stats.TrendDirection}");
                    _logger.LogInformation($"📈 {managerName}: نسبة المعالجة={stats.ProcessingRate:F1}%, مستوى الأداء={stats.PerformanceLevel}, تصنيف={stats.ManagerCategory}");
                }

                return managerStatsDict.Values.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في حساب الإحصائيات المحسنة");
                return new List<HRManagerReportDto>();
            }
        }

        /// <summary>
        /// تحديث الإحصائيات الزمنية لمدير الموارد البشرية (مثل المنسقين)
        /// </summary>
        private void UpdateHRManagerTimeStats(HRManagerReportDto stats, Core.Models.OrdersTable order)
        {
            try
            {
                var now = DateTime.Now;
                var orderDate = order.CreatedAt;

                // حساب بداية الفترات الزمنية المختلفة (مثل المشرفين)
                var startOfMonth = new DateTime(now.Year, now.Month, 1);
                var start3MonthsAgo = now.AddMonths(-3);
                var start6MonthsAgo = now.AddMonths(-6);
                var startOfYear = now.AddYears(-1);

                // ✅ إحصائيات هذا الشهر
                if (orderDate >= startOfMonth)
                {
                    stats.ThisMonthOperations++;
                }

                // ✅ إحصائيات آخر 3 أشهر
                if (orderDate >= start3MonthsAgo)
                {
                    stats.Last3MonthsOperations++;
                }

                // ✅ إحصائيات آخر 6 أشهر
                if (orderDate >= start6MonthsAgo)
                {
                    stats.Last6MonthsOperations++;
                }

                // ✅ إحصائيات آخر سنة
                if (orderDate >= startOfYear)
                {
                    stats.LastYearOperations++;
                }

                // ✅ فحص الطلبات متعددة المراحل
                var managerText = order.HumanResourcesManager ?? string.Empty;
                if (CountHRManagerActions(managerText) > 1)
                {
                    stats.MultiStageOrders++;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "خطأ في تحديث الإحصائيات الزمنية لمدير الموارد البشرية");
            }
        }


        private int CountHRManagerActions(string managerText)
        {
            if (string.IsNullOrWhiteSpace(managerText))
                return 0;

            // الأنماط التي تدل على عمليات مختلفة
            var actionPatterns = new[]
            {
        "اعتماد",
        "إعادة",
        "إلغاء",
        "تغيير الحالة",
        "تحويل"
    };

            int count = 0;
            var lowerText = managerText.ToLower();

            foreach (var pattern in actionPatterns)
            {
                if (lowerText.Contains(pattern.ToLower()))
                {
                    count++;
                }
            }

            return Math.Max(count, 1); // على الأقل عمل واحد
        }

        /// <summary>
        /// استخراج اسم مدير الموارد البشرية من النص
        /// </summary>
        private string ExtractHRManagerName(string managerText)
        {
            if (string.IsNullOrWhiteSpace(managerText))
                return "غير محدد";

            try
            {
                managerText = managerText.Trim();

                // ✅ تجاهل النصوص التي تحتوي على "تم التحويل لمدير الموارد البشرية" بدون اسم محدد
                if (managerText.Contains("تم التحويل لمدير الموارد البشرية") && !managerText.Contains("بواسطة"))
                {
                    return "___EXCLUDED___"; // 🔧 استبعاد بدلاً من "قيد المراجعة"
                }

                // ✅ تجاهل النصوص التي تحتوي على "تغيير الحالة" فقط بدون مدير محدد
                if (managerText.Contains("تم تغيير الحالة") && !managerText.Contains("بواسطة"))
                {
                    return "___EXCLUDED___"; // 🔧 استبعاد
                }

                // ✅ معالجة محسنة للنصوص التي تحتوي على "بواسطة"
                if (managerText.Contains("بواسطة"))
                {
                    var startIndex = managerText.IndexOf("بواسطة", StringComparison.OrdinalIgnoreCase);
                    if (startIndex != -1)
                    {
                        var afterKeyword = managerText.Substring(startIndex + "بواسطة".Length).Trim();

                        // إزالة النقطتين إذا وجدت
                        if (afterKeyword.StartsWith(":"))
                            afterKeyword = afterKeyword.Substring(1).Trim();

                        // إزالة التواريخ والرموز الإضافية بشكل محسن
                        var parts = afterKeyword.Split(new char[] { '|', '\n', '\r', '-' }, StringSplitOptions.RemoveEmptyEntries);
                        if (parts.Length > 0)
                        {
                            var name = parts[0].Trim();

                            // 🔧 إزالة الأرقام من نهاية الاسم (مثل علي17 → علي)
                            name = System.Text.RegularExpressions.Regex.Replace(name, @"\d+$", "").Trim();

                            // تنظيف الاسم من التواريخ والأرقام الأخرى
                            name = System.Text.RegularExpressions.Regex.Replace(name, @"\d{4}-\d{2}-\d{2}", "").Trim();

                            return string.IsNullOrWhiteSpace(name) ? "___EXCLUDED___" : name;
                        }
                    }
                }

                // ✅ معالجة النصوص التي لا تحتوي على "بواسطة" ولا تحتوي على أنماط محددة
                var cleanedText = managerText;

                // إزالة أنماط التاريخ المختلفة بشكل محسن
                cleanedText = System.Text.RegularExpressions.Regex.Replace(cleanedText, @"\d{4}-\d{2}-\d{2}", "");
                cleanedText = System.Text.RegularExpressions.Regex.Replace(cleanedText, @"\d{2}/\d{2}/\d{4}", "");

                // إزالة الكلمات الشائعة
                var wordsToRemove = new[] { "تم", "الإعادة", "الإلغاء", "التحويل", "تغيير", "الحالة", "-", "|" };
                foreach (var word in wordsToRemove)
                {
                    cleanedText = cleanedText.Replace(word, " ");
                }

                // أخذ الجزء الأول وتنظيفه
                var firstPart = cleanedText.Split(new char[] { '|', '\n', '\r', '-' }, StringSplitOptions.RemoveEmptyEntries)
                                          .FirstOrDefault()?.Trim();

                return string.IsNullOrWhiteSpace(firstPart) ? "___EXCLUDED___" : firstPart;
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"⚠️ خطأ في استخراج اسم مدير الموارد البشرية من النص: {managerText}. الخطأ: {ex.Message}");
                return "___EXCLUDED___";
            }
        }
        private string DetermineHRManagerTrend(int thisMonthCount, int last3MonthsCount, int lastYearCount)
        {
            if (thisMonthCount == 0 && last3MonthsCount == 0)
                return "مستقر";

            // حساب متوسط الأشهر
            var avgMonthlyInQuarter = last3MonthsCount / 3.0;
            var avgMonthlyInYear = lastYearCount / 12.0;

            // مقارنة هذا الشهر مع المتوسطات
            if (thisMonthCount > avgMonthlyInQuarter * 1.3) // زيادة 30% عن متوسط الربع
                return "متزايد";
            else if (thisMonthCount < avgMonthlyInYear * 0.7) // نقص 30% عن متوسط السنة
                return "متناقص";
            else
                return "مستقر";
        }
        /// <summary>
        /// تحديد نوع العملية لمدير الموارد البشرية
        /// </summary>
        private string DetermineHRManagerActionType(string managerText, OrderStatus orderStatus)
        {
            if (string.IsNullOrWhiteSpace(managerText))
                return "غير محدد";

            var lowerText = managerText.ToLower();

            // ✅ معالجة خاصة للطلبات المحولة
            if (lowerText.Contains("تم التحويل لمدير الموارد البشرية"))
                return "تحت التنفيذ"; // يعتبر تحت التنفيذ

            // ✅ ترتيب الفحص من الأكثر تحديداً للأقل مع تحسينات
            if (lowerText.Contains("تم تغيير الحالة") || lowerText.Contains("تغيير الحالة"))
            {
                // 🔧 استخراج اسم المدير من نص تغيير الحالة
                if (lowerText.Contains("بواسطة"))
                {
                    return "تغيير الحالة";
                }
                else
                {
                    return "غير محدد"; // إذا لم يكن هناك مدير محدد
                }
            }

            if (orderStatus == OrderStatus.Accepted ||
                lowerText.Contains("اعتماد بواسطة") ||
                lowerText.Contains("تم الاعتماد"))
                return "منجز";

            if (orderStatus == OrderStatus.ReturnedByManager ||
                lowerText.Contains("تم الإعادة بواسطة") ||
                lowerText.Contains("إعادة") ||
                orderStatus.ToString().ToLower().Contains("returned"))
                return "معاد";

            if (orderStatus == OrderStatus.CancelledByManager ||
                lowerText.Contains("تم الإلغاء بواسطة") ||
                lowerText.Contains("إلغاء") ||
                orderStatus.ToString().ToLower().Contains("cancelled"))
                return "ملغي";

            // إذا كان هناك أي عملية معالجة، فهو منجز افتراضياً
            if (lowerText.Contains("بواسطة"))
                return "منجز";

            return "غير محدد";
        }

        /// <summary>
        /// تحديث إحصائيات مدير الموارد البشرية - مُصحح ومحسن
        /// </summary>
        private void UpdateEnhancedHRManagerStats(HRManagerReportDto stats, Core.Models.OrdersTable order, string actionType)
        {
            // تحديث الإحصائيات بناءً على نوع العملية مع تحسينات
            switch (actionType)
            {
                case "تحت التنفيذ":
                    // لا نزيد العداد هنا، سيتم التعامل معه في الدالة الرئيسية
                    break;
                case "تغيير الحالة":
                    stats.StatusChanged++;
                    // ✅ حساب عدد الأعمال الفعلية من النص
                    stats.TotalWorksDone += CountHRManagerActualActions(order.HumanResourcesManager ?? string.Empty);
                    break;
                case "معاد":
                    stats.Returned++;
                    // ✅ حساب عدد الأعمال الفعلية من النص
                    stats.TotalWorksDone += CountHRManagerActualActions(order.HumanResourcesManager ?? string.Empty);
                    break;
                case "ملغي":
                    stats.Cancelled++;
                    // ✅ حساب عدد الأعمال الفعلية من النص
                    stats.TotalWorksDone += CountHRManagerActualActions(order.HumanResourcesManager ?? string.Empty);
                    break;
                case "منجز":
                    stats.Completed++;
                    // ✅ حساب عدد الأعمال الفعلية من النص
                    stats.TotalWorksDone += CountHRManagerActualActions(order.HumanResourcesManager ?? string.Empty);
                    break;
                case "غير محدد":
                default:
                    // ✅ تحسين: فحص إضافي بناءً على حالة الطلب
                    if (order.OrderStatus == OrderStatus.Accepted)
                    {
                        stats.Completed++;
                        stats.TotalWorksDone += CountHRManagerActualActions(order.HumanResourcesManager ?? string.Empty);
                    }
                    else if (order.OrderStatus == OrderStatus.ReturnedByManager)
                    {
                        stats.Returned++;
                        stats.TotalWorksDone += CountHRManagerActualActions(order.HumanResourcesManager ?? string.Empty);
                    }
                    else if (order.OrderStatus == OrderStatus.CancelledByManager)
                    {
                        stats.Cancelled++;
                        stats.TotalWorksDone += CountHRManagerActualActions(order.HumanResourcesManager ?? string.Empty);
                    }
                    break;
            }

            // ✅ حساب الإحصائيات الزمنية
            UpdateHRManagerTimeStats(stats, order);
        }

        /// <summary>
        /// حساب عدد الأعمال الفعلية (النقرات) من نص المدير
        /// </summary>
        private int CountHRManagerActualActions(string managerText)
        {
            if (string.IsNullOrWhiteSpace(managerText))
                return 1; // عمل واحد افتراضي

            try
            {
                // فحص الأنماط التي تدل على عمليات متعددة
                var actionPatterns = new[]
                {
            "تم الإعادة بواسطة",     // عملية إعادة
            "تم الاعتماد بواسطة",   // عملية اعتماد
            "تم الإلغاء بواسطة",    // عملية إلغاء
            "تم تغيير الحالة بواسطة", // عملية تغيير حالة
            "بواسطة"                // أي عملية عامة
        };

                int actionCount = 0;
                var lowerText = managerText.ToLower();

                // عد كل نمط يدل على عملية
                foreach (var pattern in actionPatterns)
                {
                    var matches = System.Text.RegularExpressions.Regex.Matches(lowerText, pattern.ToLower());
                    actionCount += matches.Count;
                }

                return Math.Max(actionCount, 1);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "خطأ في حساب عدد الأعمال الفعلية للمدير");
                return 1; // عمل واحد افتراضي في حالة الخطأ
            }
        }

        /// <summary>
        /// حساب متوسط زمن الإنجاز لمدير الموارد البشرية
        /// </summary>
        private double CalculateHRManagerAverageTime(List<Core.Models.OrdersTable> managerOrders, string managerName)
        {
            try
            {
                // إصلاح Lambda expression - استخدام ToList() أولاً
                var allCompletedOrders = managerOrders.ToList();
                var completedOrders = new List<Core.Models.OrdersTable>();

                // فلترة الطلبات المنجزة
                foreach (var order in allCompletedOrders)
                {
                    var actionType = DetermineHRManagerActionType(order.HumanResourcesManager ?? string.Empty, order.OrderStatus);
                    if (actionType == "منجز" || actionType == "تغيير الحالة")
                    {
                        completedOrders.Add(order);
                    }
                }

                if (!completedOrders.Any())
                    return 0;

                var validDurations = new List<double>();

                foreach (var order in completedOrders)
                {
                    try
                    {
                        var managerText = order.HumanResourcesManager ?? string.Empty;
                        var outDate = OrderHelper.ExtractOutDate(managerText);

                        DateTime completionDate;
                        if (string.IsNullOrEmpty(outDate) || !DateTime.TryParse(outDate, out completionDate))
                        {
                            // ✅ تحسين: استخدام تاريخ آخر تحديث إذا لم نجد تاريخ الإنجاز
                            completionDate = DateTime.Now;
                        }

                        var businessDays = CalculateBusinessDays(order.CreatedAt, completionDate);
                        if (businessDays >= 0 && businessDays <= 365) // فلترة القيم غير المنطقية
                        {
                            validDurations.Add(businessDays);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug(ex, "خطأ في حساب زمن الإنجاز للطلب {OrderId}", order.Id);
                    }
                }

                var average = validDurations.Any() ? Math.Round(validDurations.Average(), 1) : 0;
                _logger.LogDebug($"📊 متوسط زمن الإنجاز للمدير {managerName}: {average} يوم ({validDurations.Count} طلبات صالحة)");

                return average;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "⚠️ خطأ في حساب متوسط زمن الإنجاز للمدير {ManagerName}", managerName);
                return 0;
            }
        }





        /// <summary>
        /// الحصول على إحصائيات أنواع التحويل - الكود الذي يعمل مع إضافات بسيطة
        /// </summary>
        public async Task<ServiceResult<List<TransferTypeStatisticsDto>>> GetTransferTypeStatisticsAsync()
        {
            try
            {
                var allOrders = (await _unitOfWork.Orders.GetAllAsync()).ToList();

                _logger.LogInformation($"🔍 إجمالي الطلبات: {allOrders.Count}");

                // ✅ تشخيص: فحص عمود TransferType
                var ordersWithTransferType = allOrders.Where(o => !string.IsNullOrEmpty(o.TransferType)).ToList();
                _logger.LogInformation($"📋 الطلبات التي تحتوي على TransferType: {ordersWithTransferType.Count}");

                // ✅ تشخيص: طباعة أمثلة من القيم
                var sampleTransferTypes = ordersWithTransferType.Take(10).Select(o => o.TransferType).ToList();
                _logger.LogInformation($"📝 عينة من أنواع التحويل: {string.Join(", ", sampleTransferTypes)}");

                // ✅ طريقة بسيطة جداً - نفس الكود الأصلي تماماً
                var transferTypeStats = allOrders
                    .Where(o => !string.IsNullOrEmpty(o.TransferType))
                    .GroupBy(o => o.TransferType.Trim()) // ✅ إضافة Trim للتأكد
                    .Select(g => new TransferTypeStatisticsDto
                    {
                        TransferType = g.Key,
                        Count = g.Count(),

                        // ✅ حساب بسيط للخصائص الإضافية
                        CompletedOrders = g.Count(o => o.OrderStatus == OrderStatus.Accepted),
                        PendingOrders = g.Count(o => o.OrderStatus == OrderStatus.D),
                        CancelledOrders = g.Count(o => o.OrderStatus == OrderStatus.CancelledByCoordinator ||
                                                       o.OrderStatus == OrderStatus.CancelledByManager),

                        // ✅ افتراضيات بسيطة
                        AverageProcessingTime = 0,
                        TrendDirection = "مستقر",
                        ThisWeekCount = 0,
                        ThisMonthCount = 0
                    })
                    .OrderByDescending(t => t.Count)
                    .ToList();

                // ✅ حساب النسب بعد الإنشاء
                var totalCount = transferTypeStats.Sum(t => t.Count);

                for (int i = 0; i < transferTypeStats.Count; i++)
                {
                    transferTypeStats[i].Ranking = i + 1;
                    transferTypeStats[i].Percentage = totalCount > 0 ?
                        (double)transferTypeStats[i].Count / totalCount * 100 : 0;
                }

                _logger.LogInformation($"📊 النتائج النهائية:");
                foreach (var stat in transferTypeStats)
                {
                    _logger.LogInformation($"   - {stat.TransferType}: {stat.Count} طلب ({stat.Percentage:F1}%)");
                }

                return ServiceResult<List<TransferTypeStatisticsDto>>.Success(transferTypeStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ خطأ في جلب إحصائيات نوع التحويل");
                return ServiceResult<List<TransferTypeStatisticsDto>>.Failure($"حدث خطأ أثناء جلب إحصائيات نوع التحويل: {ex.Message}");
            }
        }





        /// <summary>
        /// الإحصائيات العامة - النسخة المحدثة
        /// </summary>
        /// <summary>
        /// الإحصائيات العامة - النسخة المحسنة
        /// </summary>
        public async Task<ServiceResult<GeneralStatisticsDto>> GetGeneralStatisticsAsync()
        {
            try
            {
                var allOrders = (await _unitOfWork.Orders.GetAllAsync()).ToList();
                var completedOrders = allOrders.Where(o => o.OrderStatus == OrderStatus.Accepted).ToList();
                var cancelledOrders = allOrders.Where(o => CancelledStatuses.Contains(o.OrderStatus)).ToList();

                _logger.LogInformation("حساب الإحصائيات العامة لـ {OrderCount} طلب", allOrders.Count);

                var statistics = new GeneralStatisticsDto
                {
                    TotalRequests = allOrders.Count,
                    CompletedRequests = completedOrders.Count,
                    CancelledRequests = cancelledOrders.Count,
                    // الحل البسيط والدقيق: الباقي = الإجمالي - المنجز - الملغي
                    PendingRequests = allOrders.Count - completedOrders.Count - cancelledOrders.Count,
                    AverageTimeToComplete = _calculator.CalculateGeneralAverageTime(completedOrders)
                };

                // التحقق من صحة البيانات
                var calculatedTotal = statistics.CompletedRequests + statistics.PendingRequests + statistics.CancelledRequests;
                if (calculatedTotal != statistics.TotalRequests)
                {
                    _logger.LogWarning("تحذير: عدم تطابق في الإحصائيات - الإجمالي: {Total}, المحسوب: {Calculated}",
                        statistics.TotalRequests, calculatedTotal);
                }

                _logger.LogInformation("تم حساب الإحصائيات العامة: إجمالي={Total}, منجز={Completed}, تحت التنفيذ={Pending}, ملغي={Cancelled}",
                    statistics.TotalRequests, statistics.CompletedRequests, statistics.PendingRequests, statistics.CancelledRequests);

                return ServiceResult<GeneralStatisticsDto>.Success(statistics);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب الإحصائيات العامة");
                return ServiceResult<GeneralStatisticsDto>.Failure($"حدث خطأ: {ex.Message}");
            }
        }



        // ===============================================
        // 1.   للإحصائيات الشهرية
        // ===============================================
        /// <summary>
        /// الحصول على الإحصائيات الشهرية
        /// </summary>
        public async Task<ServiceResult<YearlyStatisticsSummaryDto>> GetMonthlyStatisticsAsync()
        {
            try
            {
                var currentYear = DateTime.Now.Year;
                var allOrders = (await _unitOfWork.Orders.GetAllAsync()).ToList();

                // فلترة طلبات السنة الحالية
                var yearOrders = allOrders.Where(o => o.CreatedAt.Year == currentYear).ToList();

                _logger.LogInformation($"حساب الإحصائيات الشهرية لـ {yearOrders.Count} طلب في سنة {currentYear}");

                // بناء الإحصائيات الشهرية
                var monthlyStats = BuildMonthlyStatistics(yearOrders);

                // حساب الإحصائيات السنوية
                var yearlyStats = BuildYearlyStatistics(monthlyStats, currentYear);

                _logger.LogInformation($"تم حساب الإحصائيات الشهرية: {yearlyStats.TotalYearlyRequests} طلب، متوسط شهري: {yearlyStats.MonthlyAverage:F1}");

                return ServiceResult<YearlyStatisticsSummaryDto>.Success(yearlyStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب الإحصائيات الشهرية");
                return ServiceResult<YearlyStatisticsSummaryDto>.Failure($"حدث خطأ: {ex.Message}");
            }
        }

        /// <summary>
        /// بناء الإحصائيات الشهرية من الطلبات
        /// </summary>
        private List<MonthlyStatisticsDto> BuildMonthlyStatistics(List<Core.Models.OrdersTable> yearOrders)
        {
            var monthlyStats = new List<MonthlyStatisticsDto>();
            var cultureInfo = new CultureInfo("ar-SA");

            // إنشاء إحصائيات لكل شهر (1-12)
            for (int month = 1; month <= 12; month++)
            {
                var monthOrders = yearOrders.Where(o => o.CreatedAt.Month == month).ToList();

                var monthStat = new MonthlyStatisticsDto
                {
                    MonthNumber = month,
                    MonthName = cultureInfo.DateTimeFormat.GetMonthName(month),
                    RequestCount = monthOrders.Count,
                    CompletedCount = monthOrders.Count(o => IsOrderCompleted(o)),
                    PendingCount = monthOrders.Count(o => IsOrderPending(o)),
                    CancelledCount = monthOrders.Count(o => IsOrderCancelled(o))
                };

                // حساب النسب
                if (yearOrders.Count > 0)
                {
                    monthStat.MonthlyPercentage = (double)monthStat.RequestCount / yearOrders.Count * 100;
                }

                if (monthStat.RequestCount > 0)
                {
                    monthStat.CompletionRate = (double)monthStat.CompletedCount / monthStat.RequestCount * 100;
                }

                monthlyStats.Add(monthStat);
            }

            return monthlyStats;
        }

        /// <summary>
        /// بناء ملخص الإحصائيات السنوية
        /// </summary>
        private YearlyStatisticsSummaryDto BuildYearlyStatistics(List<MonthlyStatisticsDto> monthlyStats, int currentYear)
        {
            var activeMonths = monthlyStats.Where(m => m.IsActiveMonth).ToList();

            var yearlyStats = new YearlyStatisticsSummaryDto
            {
                CurrentYear = currentYear,
                MonthlyBreakdown = monthlyStats,
                TotalYearlyRequests = monthlyStats.Sum(m => m.RequestCount),
                TotalCompletedRequests = monthlyStats.Sum(m => m.CompletedCount),
                TotalPendingRequests = monthlyStats.Sum(m => m.PendingCount),
                TotalCancelledRequests = monthlyStats.Sum(m => m.CancelledCount),
                ActiveMonthsCount = activeMonths.Count
            };

            // حساب المتوسط الشهري
            yearlyStats.MonthlyAverage = activeMonths.Count > 0 ?
                (double)yearlyStats.TotalYearlyRequests / 12 : 0;

            // حساب معدل الإنجاز السنوي
            yearlyStats.YearlyCompletionRate = yearlyStats.TotalYearlyRequests > 0 ?
                (double)yearlyStats.TotalCompletedRequests / yearlyStats.TotalYearlyRequests * 100 : 0;

            // تحديد أفضل وأسوأ شهر
            if (activeMonths.Any())
            {
                var bestMonth = activeMonths.OrderByDescending(m => m.RequestCount).First();
                var worstMonth = activeMonths.OrderBy(m => m.RequestCount).First();

                yearlyStats.BestMonth = bestMonth.MonthName;
                yearlyStats.BestMonthCount = bestMonth.RequestCount;
                yearlyStats.WorstMonth = worstMonth.MonthName;
                yearlyStats.WorstMonthCount = worstMonth.RequestCount;
            }

            // تحديد الاتجاه السنوي (مبسط)
            yearlyStats.YearlyTrend = DetermineYearlyTrend(monthlyStats);

            return yearlyStats;
        }

        /// <summary>
        /// تحديد الاتجاه السنوي بناءً على البيانات الشهرية
        /// </summary>
        private string DetermineYearlyTrend(List<MonthlyStatisticsDto> monthlyStats)
        {
            var activeMonths = monthlyStats.Where(m => m.IsActiveMonth).ToList();

            if (activeMonths.Count < 3)
                return "مستقر";

            // مقارنة النصف الأول بالنصف الثاني من السنة
            var firstHalf = activeMonths.Where(m => m.MonthNumber <= 6).Sum(m => m.RequestCount);
            var secondHalf = activeMonths.Where(m => m.MonthNumber > 6).Sum(m => m.RequestCount);

            if (secondHalf > firstHalf * 1.2)
                return "متزايد";
            else if (firstHalf > secondHalf * 1.2)
                return "متناقص";
            else
                return "مستقر";
        }

        /// <summary>
        /// فحص ما إذا كان الطلب مكتملاً
        /// </summary>
        private bool IsOrderCompleted(Core.Models.OrdersTable order)
        {
            return order.OrderStatus == OrderStatus.Accepted;
        }

        /// <summary>
        /// فحص ما إذا كان الطلب معلقاً
        /// </summary>
        private bool IsOrderPending(Core.Models.OrdersTable order)
        {
            var pendingStatuses = new[]
            {
        OrderStatus.D,
        OrderStatus.ActionRequiredBySupervisor,
        OrderStatus.ReturnedByCoordinator,
        OrderStatus.ReturnedByAssistantManager,
        OrderStatus.ReturnedByManager,
        OrderStatus.ActionRequired
    };

            return pendingStatuses.Contains(order.OrderStatus);
        }

        /// <summary>
        /// فحص ما إذا كان الطلب ملغياً
        /// </summary>
        private bool IsOrderCancelled(Core.Models.OrdersTable order)
        {
            var cancelledStatuses = new[]
            {
        OrderStatus.CancelledByDepartmentManager,
        OrderStatus.CancelledByAssistantManager,
        OrderStatus.CancelledByManager
    };

            return cancelledStatuses.Contains(order.OrderStatus);
        }
        // ===============================================
        // 1.   نهاية الاحصائيات الشهرية
        // ===============================================
        /// <summary>
        /// دالة بسيطة لحساب إحصائيات أنواع الطلبات - بدون DTO جديد
        /// </summary>
        public async Task<ServiceResult<List<TransferTypeStatisticsDto>>> GetOrderTypesStatisticsAsync()
        {
            try
            {
                var allOrders = (await _unitOfWork.Orders.GetAllAsync()).ToList();

                _logger.LogInformation($"حساب إحصائيات أنواع الطلبات لـ {allOrders.Count} طلب");

                // تجميع الطلبات حسب النوع
                var orderTypeGroups = allOrders
                    .Where(o => !string.IsNullOrEmpty(o.OrderType))
                    .GroupBy(o => o.OrderType.Trim())
                    .ToList();

                var orderTypesStats = new List<TransferTypeStatisticsDto>();
                int ranking = 1;

                foreach (var group in orderTypeGroups.OrderByDescending(g => g.Count()))
                {
                    var orderType = group.Key;
                    var typeOrders = group.ToList();

                    var stat = new TransferTypeStatisticsDto
                    {
                        TransferType = orderType, // استخدام نفس الخاصية
                        Count = typeOrders.Count,
                        Ranking = ranking++,
                        CompletedOrders = typeOrders.Count(o => o.OrderStatus == OrderStatus.Accepted),
                        PendingOrders = typeOrders.Count(o => IsOrderPending(o)),
                        CancelledOrders = typeOrders.Count(o => IsOrderCancelled(o)),
                        Percentage = allOrders.Count > 0 ? (double)typeOrders.Count / allOrders.Count * 100 : 0
                    };

                    orderTypesStats.Add(stat);
                }

                _logger.LogInformation($"تم حساب إحصائيات {orderTypesStats.Count} نوع طلب");

                return ServiceResult<List<TransferTypeStatisticsDto>>.Success(orderTypesStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب إحصائيات أنواع الطلبات");
                return ServiceResult<List<TransferTypeStatisticsDto>>.Failure($"حدث خطأ: {ex.Message}");
            }
        }
        //    نهاية إحصائيات أنواع الطلبات



        /// <summary>
        /// الحصول على إحصائيات توزيع الطلبات حسب الحالة - بدون 
        /// </summary>
        public async Task<ServiceResult<List<TransferTypeStatisticsDto>>> GetOrderStatusStatisticsAsync()
        {
            try
            {
                var allOrders = (await _unitOfWork.Orders.GetAllAsync()).ToList();

                _logger.LogInformation($"حساب توزيع الطلبات حسب الحالة لـ {allOrders.Count} طلب");

                // تجميع الطلبات حسب الحالة
                var statusGroups = allOrders
                    .GroupBy(o => o.OrderStatus)
                    .ToList();

                var statusStats = new List<TransferTypeStatisticsDto>();
                int ranking = 1;

                foreach (var group in statusGroups.OrderByDescending(g => g.Count()))
                {
                    var status = group.Key;
                    var statusOrders = group.ToList();

                    var stat = new TransferTypeStatisticsDto
                    {
                        TransferType = GetStatusDisplayName(status), // تحويل enum إلى نص عربي
                        Count = statusOrders.Count,
                        Ranking = ranking++,
                        Percentage = allOrders.Count > 0 ? (double)statusOrders.Count / allOrders.Count * 100 : 0,
                        // إضافة إحصائيات إضافية
                        ThisMonthCount = statusOrders.Count(o => o.CreatedAt.Month == DateTime.Now.Month && o.CreatedAt.Year == DateTime.Now.Year),
                        ThisWeekCount = statusOrders.Count(o => o.CreatedAt >= DateTime.Now.AddDays(-7))
                    };

                    statusStats.Add(stat);
                }

                _logger.LogInformation($"تم حساب توزيع {statusStats.Count} حالة مختلفة");

                return ServiceResult<List<TransferTypeStatisticsDto>>.Success(statusStats);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حساب توزيع الطلبات حسب الحالة");
                return ServiceResult<List<TransferTypeStatisticsDto>>.Failure($"حدث خطأ: {ex.Message}");
            }
        }

        /// <summary>
        /// تحويل OrderStatus إلى نص عربي مفهوم
        /// </summary>
        private string GetStatusDisplayName(OrderStatus status)
        {
            return status switch
            {
                OrderStatus.Accepted => "مقبول",
                OrderStatus.D => "مدير الموارد البشرية",
                OrderStatus.C => "المشرفون",
                OrderStatus.B => "منسق الموارد البشرية",
                OrderStatus.DM => "مدير القسم",
                OrderStatus.A1 => "مساعد المدير للخدمات الطبية (A1)",
                OrderStatus.A2 => "مساعد المدير لخدمات التمريض (A2)",
                OrderStatus.A3 => "مساعد المدير للخدمات الإدارية والتشغيل (A3)",
                OrderStatus.A4 => "مساعد المدير للموارد البشرية (A4)",
                OrderStatus.ActionRequiredBySupervisor => "يتطلب إجراء من المشرف",
                OrderStatus.ReturnedByCoordinator => "معاد من المنسق",
                OrderStatus.ReturnedBySupervisor => "معاد من المنسق",
                OrderStatus.ReturnedByAssistantManager => "معاد من مساعد المدير",
                OrderStatus.ReturnedByManager => "معاد من المدير",
                OrderStatus.CancelledByDepartmentManager => "ملغي من مدير القسم",
                OrderStatus.CancelledByAssistantManager => "ملغي من مساعد المدير",
                OrderStatus.CancelledByManager => "ملغي من المدير",
                OrderStatus.ActionRequired => "يتطلب إجراء",
                _ => status.ToString()
            };
        }
        /// الحصول على إحصائيات توزيع الطلبات حسب الحالة - نهاية



        /// <summary>
        /// النظام المتخصص لحساب أزمنة الإنجاز
        /// </summary>
        public class SpecializedCompletionTimeCalculator
        {
            private readonly ILogger<StatisticsService> _logger;

            public SpecializedCompletionTimeCalculator(ILogger<StatisticsService> logger)
            {
                _logger = logger;
            }

            /// <summary>
            /// حساب متوسط زمن الإنجاز للإحصائيات العامة
            /// من تاريخ التقديم إلى اعتماد مدير الموارد البشرية
            /// </summary>
            public double CalculateGeneralAverageTime(List<Core.Models.OrdersTable> orders)
            {
                if (!orders?.Any() == true) return 0;

                var validDurations = new List<double>();
                _logger.LogInformation("حساب الإحصائيات العامة لـ {Count} طلب", orders.Count);

                foreach (var order in orders)
                {
                    try
                    {
                        var hrText = order.HumanResourcesManager ?? string.Empty;
                        if (!ContainsApprovalKeywords(hrText)) continue;

                        var outDate = OrderHelper.ExtractOutDate(hrText);
                        if (string.IsNullOrEmpty(outDate) || !DateTime.TryParse(outDate, out var completionDate))
                            continue;

                        var businessDays = CalculateBusinessDays(order.CreatedAt, completionDate);
                        if (businessDays >= 0 && businessDays <= 365)
                            validDurations.Add(businessDays);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "خطأ في حساب طلب {OrderId}", order.Id);
                    }
                }

                return validDurations.Any() ? Math.Round(validDurations.Average(), 1) : 0;
            }

            /// <summary>
            /// حساب متوسط زمن الإنجاز للأقسام
            /// </summary>
            public double CalculateDepartmentAverageTime(List<Core.Models.OrdersTable> orders)
            {
                if (!orders?.Any() == true) return 0;

                var validDurations = new List<double>();
                _logger.LogInformation("حساب متوسط وقت معالجة الأقسام لـ {Count} طلب", orders.Count);

                foreach (var order in orders)
                {
                    try
                    {
                        var departmentTiming = CalculateDepartmentProcessingTime(order);
                        if (departmentTiming > 0 && departmentTiming <= 365)
                            validDurations.Add(departmentTiming);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "خطأ في حساب وقت معالجة الطلب {OrderId}", order.Id);
                    }
                }

                return validDurations.Any() ? Math.Round(validDurations.Average(), 1) : 0;
            }
            private double CalculateDepartmentProcessingTime(Core.Models.OrdersTable order)
            {
                // تحديد تاريخ وصول الطلب للقسم
                DateTime departmentStartDate = GetDepartmentStartDate(order);

                // تحديد تاريخ انتهاء المعالجة
                DateTime endDate = GetDepartmentEndDate(order);

                return CalculateBusinessDays(departmentStartDate, endDate);
            }
            private DateTime GetDepartmentStartDate(Core.Models.OrdersTable order)
            {
                var orderStatus = order.OrderStatus.ToString();

                // الطلبات المعادة - تاريخ الإعادة
                if (orderStatus == "ReturnedByAssistantManager")
                {
                    var returnDate = ExtractReturnDateFromText(order.ConfirmedByAssistantManager);
                    if (returnDate.HasValue) return returnDate.Value;
                }

                if (orderStatus == "ReturnedByCoordinator")
                {
                    var returnDate = ExtractReturnDateFromText(order.ConfirmedByCoordinator);
                    if (returnDate.HasValue) return returnDate.Value;
                }

                // الطلبات العادية - تاريخ التحويل للقسم أو الإنشاء
                var deptText = order.ConfirmedByDepartmentManager ?? "";
                var transferDate = ExtractTransferDateToManager(deptText);

                return transferDate ?? order.CreatedAt;
            }
            private DateTime GetDepartmentEndDate(Core.Models.OrdersTable order)
            {
                var deptText = order.ConfirmedByDepartmentManager ?? "";

                // إذا تم اتخاذ إجراء (اعتماد أو إلغاء)
                if (deptText.Contains("اعتماد بواسطة") || deptText.Contains("تم الإلغاء بواسطة"))
                {
                    var actionDate = ExtractLastDate(deptText);
                    if (actionDate.HasValue) return actionDate.Value;
                }

                // إذا مازال معلقاً، استخدم التاريخ الحالي
                return DateTime.Now;
            }
            private DateTime? ExtractReturnDateFromText(string text)
            {
                if (string.IsNullOrEmpty(text)) return null;

                var lines = text.Split('|', StringSplitOptions.RemoveEmptyEntries);
                foreach (var line in lines)
                {
                    if (line.Contains("تمت الإعادة بواسطة"))
                    {
                        return ExtractLastDate(line);
                    }
                }
                return null;
            }
            private DateTime? ExtractTransferDateToManager(string deptText)
            {
                if (string.IsNullOrEmpty(deptText)) return null;

                var lines = deptText.Split('|', StringSplitOptions.RemoveEmptyEntries);
                foreach (var line in lines)
                {
                    if (line.Contains("تم التحويل لمدير القسم"))
                    {
                        return ExtractFirstDate(line);
                    }
                }
                return null;
            }
            /// <summary>
            /// حساب متوسط زمن الإنجاز لمساعدي المدير
            /// </summary>
            public double CalculateAssistantManagerAverageTime(List<Core.Models.OrdersTable> orders)
            {
                return CalculateStandardAverageTime(orders, o => o.ConfirmedByAssistantManager, "مساعدي المدير");
            }


            private bool ContainsApprovalKeywords(string text)
            {
                if (string.IsNullOrEmpty(text)) return false;
                var keywords = new[] { "اعتماد بواسطة", "approved by", "تم الإلغاء بواسطة", "تمت الإعادة بواسطة" };
                return keywords.Any(k => text.Contains(k, StringComparison.OrdinalIgnoreCase));
            }

            private double CalculateStandardAverageTime(List<Core.Models.OrdersTable> orders,
                Func<Core.Models.OrdersTable, string> fieldSelector, string typeName)
            {
                if (!orders?.Any() == true) return 0;

                var validDurations = new List<double>();

                foreach (var order in orders)
                {
                    try
                    {
                        var text = fieldSelector(order) ?? string.Empty;
                        if (!ContainsApprovalKeywords(text)) continue;

                        var outDate = OrderHelper.ExtractOutDate(text);
                        if (string.IsNullOrEmpty(outDate) || !DateTime.TryParse(outDate, out var completionDate))
                            continue;

                        var businessDays = CalculateBusinessDays(order.CreatedAt, completionDate);
                        if (businessDays >= 0 && businessDays <= 365)
                            validDurations.Add(businessDays);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "خطأ في حساب طلب {OrderId}", order.Id);
                    }
                }

                return validDurations.Any() ? Math.Round(validDurations.Average(), 1) : 0;
            }

            /// <summary>
            /// استخراج أول تاريخ من النص
            /// </summary>
            private DateTime? ExtractFirstDate(string text)
            {
                var dates = ExtractAllDatesFromText(text);
                return dates.FirstOrDefault();
            }

            /// <summary>
            /// استخراج آخر تاريخ من النص (للإنجاز)
            /// </summary>
            private DateTime? ExtractLastDate(string text)
            {
                var dates = ExtractAllDatesFromText(text);
                return dates.LastOrDefault();
            }
            // <summary>
            /// استخراج جميع التواريخ من النص (بدون تغيير الاسم)
            /// </summary>
            private List<DateTime> ExtractAllDatesFromText(string text)
            {
                var dates = new List<DateTime>();
                if (string.IsNullOrEmpty(text)) return dates;

                var datePatterns = new[]
                {
        @"\b14\d{2}-\d{1,2}-\d{1,2}\b",  // التاريخ الهجري
        @"\b20\d{2}-\d{1,2}-\d{1,2}\b",  // التاريخ الميلادي
        @"\b\d{4}-\d{1,2}-\d{1,2}\b"    // أي تاريخ
    };

                foreach (var pattern in datePatterns)
                {
                    var matches = System.Text.RegularExpressions.Regex.Matches(text, pattern);
                    foreach (System.Text.RegularExpressions.Match match in matches)
                    {
                        if (DateTime.TryParse(match.Value, out var date))
                        {
                            var gregorianDate = ConvertToGregorian(date);
                            if (!dates.Any(d => d.Date == gregorianDate.Date))
                                dates.Add(gregorianDate);
                        }
                    }
                }

                return dates.OrderBy(d => d).ToList();
            }

            /// <summary>
            /// تحويل التاريخ الهجري إلى الميلادي (بدون تغيير الاسم)
            /// </summary>
            private DateTime ConvertToGregorian(DateTime inputDate)
            {
                if (inputDate.Year >= 1400 && inputDate.Year <= 1500)
                {
                    try
                    {
                        var hijriCalendar = new System.Globalization.HijriCalendar();
                        return hijriCalendar.ToDateTime(inputDate.Year, inputDate.Month, inputDate.Day, 0, 0, 0, 0);
                    }
                    catch
                    {
                        return inputDate.AddYears(-622); // تحويل تقريبي كاحتياط
                    }
                }
                return inputDate;
            }



            /// <summary>
            /// حساب أيام العمل محسن (استثناء الجمعة والسبت مع معالجة الحالات الحدية)
            /// </summary>
            private static double CalculateBusinessDays(DateTime startDate, DateTime endDate)
            {
                if (startDate.Date > endDate.Date) return 0;
                if (startDate.Date == endDate.Date) return 0;

                double businessDays = 0;
                var currentDate = startDate.Date;

                while (currentDate < endDate.Date)
                {
                    if (currentDate.DayOfWeek != DayOfWeek.Friday && currentDate.DayOfWeek != DayOfWeek.Saturday)
                        businessDays++;
                    currentDate = currentDate.AddDays(1);
                }

                return businessDays;
            }


        }

    }
}








