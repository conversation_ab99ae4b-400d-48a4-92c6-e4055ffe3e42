﻿using Microsoft.AspNetCore.Mvc.Rendering;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Services
{
    public class CertificatePrintService : ICertificatePrintService
    {
        private readonly IUnitOfWork _unitOfWork;

        public CertificatePrintService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<ServiceResult<List<Microsoft.AspNetCore.Mvc.Rendering.SelectListItem>>> GetPrintableCertificatesAsync(string searchTerm = "", string filter = "today")
        {
            var all = await _unitOfWork.Certificates.GetByStatusAsync(CertificateStatus.Approved);
            IEnumerable<SelectListItem> items = all
                .Select(c => new SelectListItem { Value = c.Id.ToString(), Text = $"{c.Id} | {c.<PERSON>Name} | {c.IssueDate:yyyy/MM/dd}" });

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var term = searchTerm.Trim();
                items = items.Where(i => i.Text.Contains(term, StringComparison.OrdinalIgnoreCase));
            }

            var today = DateTime.Today;
            items = filter switch
            {
                "today" => items.Where(i => i.Text.Contains(today.ToString("yyyy/MM/dd"))),
                _ => items
            };

            return ServiceResult<List<SelectListItem>>.Success(items.ToList());
        }
    }
}


