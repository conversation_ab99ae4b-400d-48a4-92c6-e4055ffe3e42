﻿using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Services
{
    public class CertificatePrintService : ICertificatePrintService
    {
        private readonly IUnitOfWork _unitOfWork;

        public CertificatePrintService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<ServiceResult<List<PrintableCertificateDto>>> GetPrintableCertificatesAsync(string searchTerm = "", string filter = "today")
        {
            var all = await _unitOfWork.Certificates.GetByStatusAsync(CertificateStatus.Approved);
            IEnumerable<PrintableCertificateDto> items = all
                .Select(c => new PrintableCertificateDto { Id = c.Id, DisplayName = $"{c.Id} | {c.FullName} | {c.IssueDate:yyyy/MM/dd}" });

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                var term = searchTerm.Trim();
                items = items.Where(i => i.DisplayName.Contains(term, StringComparison.OrdinalIgnoreCase));
            }

            var today = DateTime.Today.ToString("yyyy/MM/dd");
            items = filter switch
            {
                "today" => items.Where(i => i.DisplayName.Contains(today)),
                _ => items
            };

            return ServiceResult<List<PrintableCertificateDto>>.Success(items.ToList());
        }
    }
}


