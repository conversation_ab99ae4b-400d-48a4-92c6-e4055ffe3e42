@model List<OrderFlowCore.Core.Models.Certificate>
@{
    ViewData["Title"] = "شهادات بانتظار المنسق";
}

<section class="py-4">
    <div class="container">
        <h3 class="mb-4">قائمة الشهادات - المنسق</h3>
        @if (TempData["ErrorMessage"] != null)
        {
            <div class="alert alert-danger">@TempData["ErrorMessage"]</div>
        }
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>الاسم</th>
                    <th>القسم</th>
                    <th>الوظيفة</th>
                    <th>الجهة</th>
                    <th>تفاصيل</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
            @foreach (var c in Model)
            {
                <tr>
                    <td>@c.FullName</td>
                    <td>@c.Department</td>
                    <td>@c.JobTitle</td>
                    <td>@c.CertificateRecipient</td>
                    <td>
                        <input type="text" name="details" class="form-control form-control-sm" form="<EMAIL>" placeholder="تفاصيل المنسق" />
                    </td>
                    <td>
                        <form id="<EMAIL>" asp-action="ApproveByCoordinator" method="post" class="d-inline">
                            <input type="hidden" name="id" value="@c.Id" />
                            <button type="submit" class="btn btn-sm btn-primary">اعتماد</button>
                        </form>
                    </td>
                </tr>
            }
            </tbody>
        </table>
    </div>
    </section>




