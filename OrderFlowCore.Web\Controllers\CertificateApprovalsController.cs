using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Web.Attributes;
using System.Threading.Tasks;

namespace OrderFlowCore.Web.Controllers
{
    [Authorize]
    public class CertificateApprovalsController : Controller
    {
        private readonly ICertificateService _certificateService;
        private readonly ILogger<CertificateApprovalsController> _logger;

        public CertificateApprovalsController(ICertificateService certificateService, ILogger<CertificateApprovalsController> logger)
        {
            _certificateService = certificateService;
            _logger = logger;
        }

        [HttpGet]
        [AuthorizeRole(UserRole.DirectManager)]
        public async Task<IActionResult> DirectManager()
        {
            var result = await _certificateService.GetPendingAsync(CertificateStatus.PendingDirectManager);
            if (!result.IsSuccess) TempData["ErrorMessage"] = result.Message;
            return View(result.Data);
        }

        [HttpGet]
        [AuthorizeRole(UserRole.Coordinator)]
        public async Task<IActionResult> Coordinator()
        {
            var result = await _certificateService.GetPendingAsync(CertificateStatus.PendingCoordinator);
            if (!result.IsSuccess) TempData["ErrorMessage"] = result.Message;
            return View(result.Data);
        }

        [HttpGet]
        [AuthorizeRole(UserRole.Manager)]
        public async Task<IActionResult> HRManager()
        {
            var result = await _certificateService.GetPendingAsync(CertificateStatus.PendingHRManager);
            if (!result.IsSuccess) TempData["ErrorMessage"] = result.Message;
            return View(result.Data);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorizeRole(UserRole.DirectManager)]
        public async Task<IActionResult> ApproveByDirectManager(int id)
        {
            var userName = User.Identity?.Name ?? "System";
            var result = await _certificateService.ConfirmByDirectManagerAsync(id, userName);
            return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorizeRole(UserRole.Coordinator)]
        public async Task<IActionResult> ApproveByCoordinator(int id, string details)
        {
            var userName = User.Identity?.Name ?? "System";
            var result = await _certificateService.ConfirmByCoordinatorAsync(id, userName, details);
            return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [AuthorizeRole(UserRole.Manager)]
        public async Task<IActionResult> ApproveByHRManager(int id)
        {
            var userName = User.Identity?.Name ?? "System";
            var result = await _certificateService.ConfirmByHRManagerAsync(id, userName);
            return Json(new { success = result.IsSuccess, message = result.Message });
        }
    }
}


