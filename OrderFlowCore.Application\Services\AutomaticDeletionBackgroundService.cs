using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.Interfaces.Services;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Services
{
    public class AutomaticDeletionBackgroundService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<AutomaticDeletionBackgroundService> _logger;
        private readonly AutomaticDeletionOptions _options;

        public AutomaticDeletionBackgroundService(
            IServiceProvider serviceProvider,
            ILogger<AutomaticDeletionBackgroundService> logger,
            IOptions<AutomaticDeletionOptions> options)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _options = options.Value;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            if (!_options.IsEnabled)
            {
                _logger.LogInformation("Automatic Deletion Background Service is disabled");
                return;
            }

            _logger.LogInformation("Automatic Deletion Background Service started - Running every {Hours} hours", _options.ExecutionIntervalHours);

            // Calculate initial delay to run at the specified time
            var initialDelay = CalculateInitialDelay();
            if (initialDelay > TimeSpan.Zero)
            {
                _logger.LogInformation("Waiting {Delay} until next scheduled execution time", initialDelay);
                await Task.Delay(initialDelay, stoppingToken);
            }

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await ExecuteAutomaticDeletionAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error occurred while executing automatic deletion");
                }

                // Wait for the next execution period
                var delay = CalculateInitialDelay();
                await Task.Delay(delay, stoppingToken);
            }

            _logger.LogInformation("Automatic Deletion Background Service stopped");
        }

        private async Task ExecuteAutomaticDeletionAsync()
        {
            using var scope = _serviceProvider.CreateScope();
            var automaticDeletionService = scope.ServiceProvider.GetRequiredService<IAutomaticDeletionSettingsService>();

            try
            {
                _logger.LogInformation("Starting automatic deletion process");

                // Check if there are any enabled settings
                var activeSettingsResult = await automaticDeletionService.GetActiveSettingsAsync();
                if (!activeSettingsResult.IsSuccess || !activeSettingsResult.Data.Any(s => s.IsEnabled))
                {
                    _logger.LogInformation("No active automatic deletion settings found, skipping execution");
                    return;
                }

                // Execute the automatic deletion
                var result = await automaticDeletionService.ExecuteAutomaticDeletionAsync();
                
                if (result.IsSuccess)
                {
                    _logger.LogInformation("Automatic deletion completed successfully: {Message}", result.Message);
                }
                else
                {
                    _logger.LogWarning("Automatic deletion failed: {Message}", result.Message);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Exception occurred during automatic deletion execution");
            }
        }

        private TimeSpan CalculateInitialDelay()
        {
            try
            {
                var now = DateTime.Now;
                var executionTime = TimeSpan.Parse(_options.ExecutionTime);
                var nextExecution = now.Date.Add(executionTime);

                // If the execution time has already passed today, schedule for tomorrow
                if (nextExecution <= now)
                {
                    nextExecution = nextExecution.AddDays(1);
                }

                return nextExecution - now;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Invalid execution time format '{ExecutionTime}', using default delay", _options.ExecutionTime);
                return TimeSpan.FromHours(1); // Default to 1 hour delay
            }
        }

        public override async Task StopAsync(CancellationToken cancellationToken)
        {
            _logger.LogInformation("Automatic Deletion Background Service is stopping");
            await base.StopAsync(cancellationToken);
        }
    }
}
