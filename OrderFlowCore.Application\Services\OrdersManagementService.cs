using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using OfficeOpenXml;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Core.Models;

namespace OrderFlowCore.Application.Services
{
    public class OrdersManagementService : IOrdersManagementService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<OrdersManagementService> _logger;
        private readonly IFileService _fileService;

        public OrdersManagementService(IUnitOfWork unitOfWork, ILogger<OrdersManagementService> logger, IFileService fileService)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
            _fileService = fileService;
        }

        public async Task<ServiceResult<List<OrderSummaryDto>>> GetFilteredOrdersAsync(OrderFilterDto filter)
        {
            try
            {
                // Parse status filter
                OrderStatus? statusFilter = null;
                bool isPendingOrdersFilter = false;

                if (filter.Status != "All" && !string.IsNullOrEmpty(filter.Status))
                {
                    if (filter.Status == "PendingOrders")
                    {
                        isPendingOrdersFilter = true;
                    }
                    else if (Enum.TryParse<OrderStatus>(filter.Status, out var parsedStatus))
                    {
                        statusFilter = parsedStatus;
                    }
                }

                // Use optimized repository method with database-level filtering
                var (orders, totalCount) = await _unitOfWork.Orders.GetFilteredOrdersAsync(
                    status: statusFilter,
                    department: filter.Department,
                    fromDate: filter.FromDate,
                    toDate: filter.ToDate,
                    civilRecord: filter.CivilRecord,
                    page: filter.Page,
                    pageSize: filter.PageSize
                );

                // Apply pending orders filter if needed
                if (isPendingOrdersFilter)
                {
                    var pendingStatuses = new HashSet<OrderStatus>
                    {
                        OrderStatus.DM, OrderStatus.A1, OrderStatus.A2, OrderStatus.A3,
                        OrderStatus.A4, OrderStatus.B, OrderStatus.C, OrderStatus.D,
                        OrderStatus.ReturnedByCoordinator, OrderStatus.ReturnedBySupervisor,
                        OrderStatus.ReturnedByAssistantManager, OrderStatus.ReturnedByManager,
                        OrderStatus.ActionRequired, OrderStatus.ActionRequiredBySupervisor
                    };

                    orders = orders.Where(o => pendingStatuses.Contains(o.OrderStatus)).ToList();
                    totalCount = orders.Count;
                }

                // Map to DTOs (only the filtered and paged results)
                var orderSummaries = orders.Select(o => new OrderSummaryDto
                {
                    Id = o.Id,
                    EmployeeName = o.EmployeeName ?? string.Empty,
                    Department = o.Department ?? string.Empty,
                    OrderStatus = o.OrderStatus,
                    CreatedAt = o.CreatedAt,
                    OrderType = o.OrderType ?? string.Empty,
                    CivilRecord = o.CivilRecord ?? string.Empty
                }).ToList();

                return new ServiceResult<List<OrderSummaryDto>>
                {
                    IsSuccess = true,
                    Data = orderSummaries,
                    TotalCount = totalCount
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الطلبات المفلترة");
                return ServiceResult<List<OrderSummaryDto>>.Failure("حدث خطأ أثناء جلب الطلبات");
            }
        }

        public async Task<ServiceResult<List<OrderSummaryDto>>> GetAllOrdersAsync()
        {
            try
            {
                var orders = await _unitOfWork.Orders.GetAllAsync();
                var result = orders.Select(o => new OrderSummaryDto
                {
                    Id = o.Id,
                    EmployeeName = o.EmployeeName ?? string.Empty,
                    Department = o.Department ?? string.Empty,
                    OrderStatus = o.OrderStatus,
                    CreatedAt = o.CreatedAt,
                    OrderType = o.OrderType ?? string.Empty,
                    CivilRecord = o.CivilRecord ?? string.Empty
                }).OrderByDescending(o => o.CreatedAt).ToList();

                return ServiceResult<List<OrderSummaryDto>>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب جميع الطلبات");
                return ServiceResult<List<OrderSummaryDto>>.Failure("حدث خطأ أثناء جلب الطلبات");
            }
        }

        public async Task<ServiceResult<List<DropdownItemDto>>> GetOrderStatusesAsync()
        {
            try
            {
                var orders = await _unitOfWork.Orders.GetAllAsync();
                var statuses = orders.Select(o => o.OrderStatus)
                    .Distinct()
                    .Select(s => new DropdownItemDto { Value = s.ToString(), Text = s.ToDisplayString() })
                    .OrderBy(s => s.Text)
                    .ToList();

                statuses.Insert(0, new DropdownItemDto { Value = "All", Text = "كل الحالات" });

                // Add the new "Pending Orders" option
                statuses.Insert(1, new DropdownItemDto { Value = "PendingOrders", Text = "الطلبات المعلقة" });

                return ServiceResult<List<DropdownItemDto>>.Success(statuses);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب حالات الطلبات");
                return ServiceResult<List<DropdownItemDto>>.Failure("حدث خطأ أثناء جلب حالات الطلبات");
            }
        }

        public async Task<ServiceResult<List<DropdownItemDto>>> GetDepartmentsAsync()
        {
            try
            {
                var departments = await _unitOfWork.Departments.GetAllAsync();
                var result = departments.Select(d => new DropdownItemDto
                {
                    Value = d.Name,
                    Text = d.Name
                }).OrderBy(d => d.Text).ToList();

                result.Insert(0, new DropdownItemDto { Value = "All", Text = "كل الأقسام" });
                return ServiceResult<List<DropdownItemDto>>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الأقسام");
                return ServiceResult<List<DropdownItemDto>>.Failure("حدث خطأ أثناء جلب الأقسام");
            }
        }

        public async Task<ServiceResult<List<DropdownItemDto>>> GetOrderNumbersAsync()
        {
            try
            {
                var orders = await _unitOfWork.Orders.GetAllAsync();
                var result = orders.Select(o => new DropdownItemDto
                {
                    Value = o.Id.ToString(),
                    Text = $"{o.Id} - {o.EmployeeName}"
                }).OrderByDescending(o => int.Parse(o.Value)).ToList();

                return ServiceResult<List<DropdownItemDto>>.Success(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب أرقام الطلبات");
                return ServiceResult<List<DropdownItemDto>>.Failure("حدث خطأ أثناء جلب أرقام الطلبات");
            }
        }

        public async Task<ServiceResult<byte[]>> ExportOrdersToExcelAsync(OrderFilterDto filter)
        {
            try
            {
                // Parse status filter
                OrderStatus? statusFilter = null;
                bool isPendingOrdersFilter = false;

                if (filter.Status != "All" && !string.IsNullOrEmpty(filter.Status))
                {
                    if (filter.Status == "PendingOrders")
                    {
                        isPendingOrdersFilter = true;
                    }
                    else if (Enum.TryParse<OrderStatus>(filter.Status, out var parsedStatus))
                    {
                        statusFilter = parsedStatus;
                    }
                }

                // Use optimized export method (no paging, all filtered results)
                var orders = await _unitOfWork.Orders.GetFilteredOrdersForExportAsync(
                    status: statusFilter,
                    department: filter.Department,
                    fromDate: filter.FromDate,
                    toDate: filter.ToDate,
                    civilRecord: filter.CivilRecord
                );

                // Apply pending orders filter if needed
                if (isPendingOrdersFilter)
                {
                    var pendingStatuses = new HashSet<OrderStatus>
                    {
                        OrderStatus.DM, OrderStatus.A1, OrderStatus.A2, OrderStatus.A3,
                        OrderStatus.A4, OrderStatus.B, OrderStatus.C, OrderStatus.D,
                        OrderStatus.ReturnedByCoordinator, OrderStatus.ReturnedBySupervisor,
                        OrderStatus.ReturnedByAssistantManager, OrderStatus.ReturnedByManager,
                        OrderStatus.ActionRequired, OrderStatus.ActionRequiredBySupervisor
                    };

                    orders = orders.Where(o => pendingStatuses.Contains(o.OrderStatus)).ToList();
                }

                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("الطلبات");

                // Headers - All order data
                var headers = new string[]
                {
                    "رقم الطلب", "تاريخ الطلب", "نوع الطلب", "اسم الموظف", "المسمى الوظيفي",
                    "رقم الموظف", "السجل المدني", "الجنسية", "رقم الجوال", "نوع التوظيف",
                    "المؤهل", "القسم", "التفاصيل", "حالة الطلب", "تأكيد مدير القسم",
                    "تأكيد المدير المساعد", "تأكيد المنسق", "سبب الإلغاء", "تفاصيل المنسق",
                    "مشرف خدمات الموظفين", "مشرف تخطيط الموارد البشرية", "مشرف تقنية المعلومات",
                    "مشرف الحضور والانصراف", "مشرف السجلات الطبية", "مشرف الرواتب والمزايا",
                    "مشرف الشؤون القانونية والامتثال", "مشرف خدمات الموارد البشرية", "مشرف الإسكان",
                    "مشرف الملفات", "مشرف العيادات الخارجية", "مشرف الضمان الاجتماعي",
                    "مشرف مراقبة المخزون", "مشرف تطوير الإيرادات", "مشرف الأمن",
                    "مشرف الاستشارات الطبية", "مدير الموارد البشرية", "نوع النقل",
                    "ملاحظات المشرف", "رابط المرفق 1", "رابط المرفق 2", "رابط المرفق 3", "رابط المرفق 4"
                };

                for (int col = 0; col < headers.Length; col++)
                {
                    worksheet.Cells[1, col + 1].Value = headers[col];
                }

                // Data
                for (int i = 0; i < orders.Count; i++)
                {
                    var order = orders[i];
                    int row = i + 2;
                    int col = 1;

                    worksheet.Cells[row, col++].Value = order.Id;
                    worksheet.Cells[row, col++].Value = order.CreatedAt.ToString("yyyy-MM-dd HH:mm");
                    worksheet.Cells[row, col++].Value = order.OrderType;
                    worksheet.Cells[row, col++].Value = order.EmployeeName;
                    worksheet.Cells[row, col++].Value = order.JobTitle;
                    worksheet.Cells[row, col++].Value = order.EmployeeNumber;
                    worksheet.Cells[row, col++].Value = order.CivilRecord;
                    worksheet.Cells[row, col++].Value = order.Nationality;
                    worksheet.Cells[row, col++].Value = order.MobileNumber;
                    worksheet.Cells[row, col++].Value = order.EmploymentType;
                    worksheet.Cells[row, col++].Value = order.Qualification;
                    worksheet.Cells[row, col++].Value = order.Department;
                    worksheet.Cells[row, col++].Value = order.Details;
                    worksheet.Cells[row, col++].Value = order.OrderStatus.ToDisplayString();
                    worksheet.Cells[row, col++].Value = order.ConfirmedByDepartmentManager;
                    worksheet.Cells[row, col++].Value = order.ConfirmedByAssistantManager;
                    worksheet.Cells[row, col++].Value = order.ConfirmedByCoordinator;
                    worksheet.Cells[row, col++].Value = order.ReasonForCancellation;
                    worksheet.Cells[row, col++].Value = order.CoordinatorDetails;
                    worksheet.Cells[row, col++].Value = order.SupervisorOfEmployeeServices;
                    worksheet.Cells[row, col++].Value = order.SupervisorOfHumanResourcesPlanning;
                    worksheet.Cells[row, col++].Value = order.SupervisorOfInformationTechnology;
                    worksheet.Cells[row, col++].Value = order.SupervisorOfAttendance;
                    worksheet.Cells[row, col++].Value = order.SupervisorOfMedicalRecords;
                    worksheet.Cells[row, col++].Value = order.SupervisorOfPayrollAndBenefits;
                    worksheet.Cells[row, col++].Value = order.SupervisorOfLegalAndCompliance;
                    worksheet.Cells[row, col++].Value = order.SupervisorOfHumanResourcesServices;
                    worksheet.Cells[row, col++].Value = order.SupervisorOfHousing;
                    worksheet.Cells[row, col++].Value = order.SupervisorOfFiles;
                    worksheet.Cells[row, col++].Value = order.SupervisorOfOutpatientClinics;
                    worksheet.Cells[row, col++].Value = order.SupervisorOfSocialSecurity;
                    worksheet.Cells[row, col++].Value = order.SupervisorOfInventoryControl;
                    worksheet.Cells[row, col++].Value = order.SupervisorOfRevenueDevelopment;
                    worksheet.Cells[row, col++].Value = order.SupervisorOfSecurity;
                    worksheet.Cells[row, col++].Value = order.SupervisorOfMedicalConsultation;
                    worksheet.Cells[row, col++].Value = order.HumanResourcesManager;
                    worksheet.Cells[row, col++].Value = order.TransferType;
                    worksheet.Cells[row, col++].Value = order.SupervisorNotes;
                    worksheet.Cells[row, col++].Value = order.File1Url;
                    worksheet.Cells[row, col++].Value = order.File2Url;
                    worksheet.Cells[row, col++].Value = order.File3Url;
                    worksheet.Cells[row, col++].Value = order.File4Url;
                }

                worksheet.Cells.AutoFitColumns();
                return ServiceResult<byte[]>.Success(package.GetAsByteArray());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير الطلبات إلى Excel");
                return ServiceResult<byte[]>.Failure("حدث خطأ أثناء تصدير الطلبات");
            }
        }

        public async Task<ServiceResult<byte[]>> ExportFilteredResultsToExcelAsync(OrderFilterDto filter)
        {
            return await ExportOrdersToExcelAsync(filter);
        }

        public async Task<ServiceResult<int>> ImportOrdersFromExcelAsync(Stream excelStream)
        {
            try
            {
                int importedCount = 0;
                var errors = new List<string>();

                using var package = new ExcelPackage(excelStream);
                var worksheet = package.Workbook.Worksheets.FirstOrDefault();

                if (worksheet == null)
                {
                    return ServiceResult<int>.Failure("لم يتم العثور على ورقة عمل في الملف");
                }

                // Check if we have data
                if (worksheet.Dimension == null || worksheet.Dimension.Rows < 2)
                {
                    return ServiceResult<int>.Failure("الملف فارغ أو لا يحتوي على بيانات");
                }

                // Expected headers (same as export)
                var expectedHeaders = new string[]
                {
                    "رقم الطلب", "تاريخ الطلب", "نوع الطلب", "اسم الموظف", "المسمى الوظيفي",
                    "رقم الموظف", "السجل المدني", "الجنسية", "رقم الجوال", "نوع التوظيف",
                    "المؤهل", "القسم", "التفاصيل", "حالة الطلب", "تأكيد مدير القسم",
                    "تأكيد المدير المساعد", "تأكيد المنسق", "سبب الإلغاء", "تفاصيل المنسق",
                    "مشرف خدمات الموظفين", "مشرف تخطيط الموارد البشرية", "مشرف تقنية المعلومات",
                    "مشرف الحضور والانصراف", "مشرف السجلات الطبية", "مشرف الرواتب والمزايا",
                    "مشرف الشؤون القانونية والامتثال", "مشرف خدمات الموارد البشرية", "مشرف الإسكان",
                    "مشرف الملفات", "مشرف العيادات الخارجية", "مشرف الضمان الاجتماعي",
                    "مشرف مراقبة المخزون", "مشرف تطوير الإيرادات", "مشرف الأمن",
                    "مشرف الاستشارات الطبية", "مدير الموارد البشرية", "نوع النقل",
                    "ملاحظات المشرف", "رابط المرفق 1", "رابط المرفق 2", "رابط المرفق 3", "رابط المرفق 4"
                };

                // Process each row (skip header row)
                for (int row = 2; row <= worksheet.Dimension.Rows; row++)
                {
                    try
                    {
                        // Skip empty rows
                        if (string.IsNullOrWhiteSpace(worksheet.Cells[row, 4].Text)) // EmployeeName column
                            continue;

                        var order = new OrdersTable();
                        int col = 1;

                        // Skip ID column for import (auto-generated)
                        col++;

                        // Parse CreatedAt
                        if (DateTime.TryParse(worksheet.Cells[row, col++].Text, out DateTime createdAt))
                            order.CreatedAt = createdAt;
                        else
                            order.CreatedAt = DateTime.Now;

                        order.OrderType = worksheet.Cells[row, col++].Text?.Trim();
                        order.EmployeeName = worksheet.Cells[row, col++].Text?.Trim();
                        order.JobTitle = worksheet.Cells[row, col++].Text?.Trim();
                        order.EmployeeNumber = worksheet.Cells[row, col++].Text?.Trim();
                        order.CivilRecord = worksheet.Cells[row, col++].Text?.Trim();
                        order.Nationality = worksheet.Cells[row, col++].Text?.Trim();
                        order.MobileNumber = worksheet.Cells[row, col++].Text?.Trim();
                        order.EmploymentType = worksheet.Cells[row, col++].Text?.Trim();
                        order.Qualification = worksheet.Cells[row, col++].Text?.Trim();
                        order.Department = worksheet.Cells[row, col++].Text?.Trim();
                        order.Details = worksheet.Cells[row, col++].Text?.Trim();

                        // Parse OrderStatus
                        var statusText = worksheet.Cells[row, col++].Text?.Trim();
                        if (Enum.TryParse<OrderStatus>(statusText, out OrderStatus status))
                            order.OrderStatus = status;
                        else
                            order.OrderStatus = OrderStatus.DM; // Default status

                        order.ConfirmedByDepartmentManager = worksheet.Cells[row, col++].Text?.Trim();
                        order.ConfirmedByAssistantManager = worksheet.Cells[row, col++].Text?.Trim();
                        order.ConfirmedByCoordinator = worksheet.Cells[row, col++].Text?.Trim();
                        order.ReasonForCancellation = worksheet.Cells[row, col++].Text?.Trim();
                        order.CoordinatorDetails = worksheet.Cells[row, col++].Text?.Trim();
                        order.SupervisorOfEmployeeServices = worksheet.Cells[row, col++].Text?.Trim();
                        order.SupervisorOfHumanResourcesPlanning = worksheet.Cells[row, col++].Text?.Trim();
                        order.SupervisorOfInformationTechnology = worksheet.Cells[row, col++].Text?.Trim();
                        order.SupervisorOfAttendance = worksheet.Cells[row, col++].Text?.Trim();
                        order.SupervisorOfMedicalRecords = worksheet.Cells[row, col++].Text?.Trim();
                        order.SupervisorOfPayrollAndBenefits = worksheet.Cells[row, col++].Text?.Trim();
                        order.SupervisorOfLegalAndCompliance = worksheet.Cells[row, col++].Text?.Trim();
                        order.SupervisorOfHumanResourcesServices = worksheet.Cells[row, col++].Text?.Trim();
                        order.SupervisorOfHousing = worksheet.Cells[row, col++].Text?.Trim();
                        order.SupervisorOfFiles = worksheet.Cells[row, col++].Text?.Trim();
                        order.SupervisorOfOutpatientClinics = worksheet.Cells[row, col++].Text?.Trim();
                        order.SupervisorOfSocialSecurity = worksheet.Cells[row, col++].Text?.Trim();
                        order.SupervisorOfInventoryControl = worksheet.Cells[row, col++].Text?.Trim();
                        order.SupervisorOfRevenueDevelopment = worksheet.Cells[row, col++].Text?.Trim();
                        order.SupervisorOfSecurity = worksheet.Cells[row, col++].Text?.Trim();
                        order.SupervisorOfMedicalConsultation = worksheet.Cells[row, col++].Text?.Trim();
                        order.HumanResourcesManager = worksheet.Cells[row, col++].Text?.Trim();
                        order.TransferType = worksheet.Cells[row, col++].Text?.Trim();
                        order.SupervisorNotes = worksheet.Cells[row, col++].Text?.Trim();
                        order.File1Url = worksheet.Cells[row, col++].Text?.Trim();
                        order.File2Url = worksheet.Cells[row, col++].Text?.Trim();
                        order.File3Url = worksheet.Cells[row, col++].Text?.Trim();
                        order.File4Url = worksheet.Cells[row, col++].Text?.Trim();

                        // Validate required fields
                        if (string.IsNullOrWhiteSpace(order.EmployeeName))
                        {
                            errors.Add($"الصف {row}: اسم الموظف مطلوب");
                            continue;
                        }

                        if (string.IsNullOrWhiteSpace(order.CivilRecord))
                        {
                            errors.Add($"الصف {row}: السجل المدني مطلوب");
                            continue;
                        }

                        // Check for duplicate civil record
                        var existingOrder = await _unitOfWork.Orders.GetByCivilRecordAsync(order.CivilRecord);
                        if (existingOrder != null)
                        {
                            errors.Add($"الصف {row}: السجل المدني {order.CivilRecord} موجود مسبقاً");
                            continue;
                        }

                        await _unitOfWork.Orders.AddAsync(order);
                        importedCount++;
                    }
                    catch (Exception ex)
                    {
                        errors.Add($"الصف {row}: خطأ في معالجة البيانات - {ex.Message}");
                    }
                }

                if (importedCount > 0)
                {
                    await _unitOfWork.SaveChangesAsync();
                }

                var message = $"تم استيراد {importedCount} طلب بنجاح";
                if (errors.Any())
                {
                    message += $". عدد الأخطاء: {errors.Count}";
                }

                return ServiceResult<int>.Success(importedCount, message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استيراد الطلبات من Excel");
                return ServiceResult<int>.Failure("حدث خطأ أثناء استيراد الطلبات");
            }
        }

        public async Task<ServiceResult<byte[]>> ExportDetailedStatisticsToExcelAsync()
        {
            try
            {
                var orders = await _unitOfWork.Orders.GetAllAsync();
                using var package = new ExcelPackage();
                var worksheet = package.Workbook.Worksheets.Add("الإحصائيات المفصلة");

                int row = 1;

                StyleHeader(worksheet, row);
                worksheet.Cells[row++, 1].Value = "تقرير الإحصائيات المفصلة";
                worksheet.Cells[row++, 1].Value = $"تاريخ التقرير: {DateTime.Now:yyyy-MM-dd HH:mm}";
                row++;

                // Total Orders
                worksheet.Cells[row, 1].Value = "إجمالي الطلبات";
                worksheet.Cells[row++, 2].Value = orders.Count;
                row++;

                // Orders by Status
                StyleHeader(worksheet, row);
                worksheet.Cells[row++, 1].Value = "عدد الطلبات حسب الحالة";
                worksheet.Cells[row, 1].Value = "الحالة";
                worksheet.Cells[row, 2].Value = "العدد";
                worksheet.Cells[row, 3].Value = "%";
                var totalOrders = orders.Count;
                var statusGroups = orders.GroupBy(o => o.OrderStatus)
                    .Select(g => new { Status = g.Key, Count = g.Count() })
                    .OrderByDescending(x => x.Count)
                    .ToList();
                int statusStartRow = ++row;
                foreach (var group in statusGroups)
                {
                    worksheet.Cells[row, 1].Value = group.Status.ToDisplayString();
                    worksheet.Cells[row, 2].Value = group.Count;
                    worksheet.Cells[row, 3].Value = totalOrders > 0 ? (double)group.Count / totalOrders : 0;
                    row++;
                }
                row++;

                // Orders by Department
                StyleHeader(worksheet, row);
                worksheet.Cells[row++, 1].Value = "عدد الطلبات حسب القسم";
                worksheet.Cells[row, 1].Value = "القسم";
                worksheet.Cells[row, 2].Value = "العدد";
                worksheet.Cells[row, 3].Value = "%";
                var deptGroups = orders.GroupBy(o => o.Department ?? "غير محدد")
                    .Select(g => new { Department = g.Key, Count = g.Count() })
                    .OrderByDescending(x => x.Count)
                    .ToList();
                int deptStartRow = ++row;
                foreach (var group in deptGroups)
                {
                    worksheet.Cells[row, 1].Value = group.Department;
                    worksheet.Cells[row, 2].Value = group.Count;
                    worksheet.Cells[row, 3].Value = totalOrders > 0 ? (double)group.Count / totalOrders : 0;
                    row++;
                }
                row++;

                // Orders by Type
                StyleHeader(worksheet, row);
                worksheet.Cells[row++, 1].Value = "عدد الطلبات حسب نوع الطلب";
                worksheet.Cells[row, 1].Value = "نوع الطلب";
                worksheet.Cells[row, 2].Value = "العدد";
                worksheet.Cells[row, 3].Value = "%";
                var typeGroups = orders.GroupBy(o => o.OrderType ?? "غير محدد")
                    .Select(g => new { OrderType = g.Key, Count = g.Count() })
                    .OrderByDescending(x => x.Count)
                    .ToList();
                int typeStartRow = ++row;
                foreach (var group in typeGroups)
                {
                    worksheet.Cells[row, 1].Value = group.OrderType;
                    worksheet.Cells[row, 2].Value = group.Count;
                    worksheet.Cells[row, 3].Value = totalOrders > 0 ? (double)group.Count / totalOrders : 0;
                    row++;
                }
                row++;

                // Orders per Month (last 12 months)
                StyleHeader(worksheet, row);
                worksheet.Cells[row++, 1].Value = "عدد الطلبات لكل شهر (آخر 12 شهر)";
                worksheet.Cells[row, 1].Value = "الشهر";
                worksheet.Cells[row, 2].Value = "العدد";
                var now = DateTime.Now;
                var months = Enumerable.Range(0, 12)
                    .Select(i => now.AddMonths(-i))
                    .Select(d => new { Year = d.Year, Month = d.Month })
                    .OrderBy(x => x.Year).ThenBy(x => x.Month)
                    .ToList();
                int monthStartRow = ++row;
                foreach (var m in months)
                {
                    var count = orders.Count(o => o.CreatedAt.Year == m.Year && o.CreatedAt.Month == m.Month);
                    worksheet.Cells[row, 1].Value = $"{m.Year}-{m.Month:D2}";
                    worksheet.Cells[row, 2].Value = count;
                    row++;
                }

                worksheet.Cells[1, 1, row, 3].AutoFitColumns();
                return ServiceResult<byte[]>.Success(package.GetAsByteArray());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير الإحصائيات المفصلة");
                return ServiceResult<byte[]>.Failure("حدث خطأ أثناء تصدير الإحصائيات");
            }
        }

        private void StyleHeader(ExcelWorksheet worksheet, int row)
        {
            worksheet.Cells[row, 1, row, 3].Merge = true;
            worksheet.Cells[row, 1].Style.Font.Bold = true;
            worksheet.Cells[row, 1].Style.Font.Size = 12;
            worksheet.Cells[row, 1].Style.HorizontalAlignment = OfficeOpenXml.Style.ExcelHorizontalAlignment.Center;
            worksheet.Cells[row, 1].Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
            worksheet.Cells[row, 1].Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
        }

        public async Task<ServiceResult> DeleteOldOrdersAsync(DeleteOldOrdersDto deleteRequest, string userName)
        {
            try
            {
                DateTime cutoffDate;
                List<OrdersTable> ordersToDelete;

                if (deleteRequest.UseCustomDateRange)
                {
                    if (!deleteRequest.StartDate.HasValue || !deleteRequest.EndDate.HasValue)
                    {
                        return ServiceResult.Failure("يجب تحديد تاريخ البداية والنهاية");
                    }

                    ordersToDelete = await _unitOfWork.Orders.GetOrdersByDateRangeAsync(
                        deleteRequest.StartDate.Value, deleteRequest.EndDate.Value);
                }
                else
                {
                    cutoffDate = DateTime.Now.AddMonths(-(int)deleteRequest.PeriodInMonths);
                    ordersToDelete = await _unitOfWork.Orders.GetOrdersOlderThanAsync(cutoffDate);
                }

                foreach (var order in ordersToDelete)
                {
                    var fileUrls = new List<string>();
                    if (!string.IsNullOrEmpty(order.File1Url)) fileUrls.Add(order.File1Url);
                    if (!string.IsNullOrEmpty(order.File2Url)) fileUrls.Add(order.File2Url);
                    if (!string.IsNullOrEmpty(order.File3Url)) fileUrls.Add(order.File3Url);
                    if (!string.IsNullOrEmpty(order.File4Url)) fileUrls.Add(order.File4Url);
                    if (fileUrls.Count > 0)
                    {
                        var fileDeleteResult = await _fileService.DeleteFilesAsync(fileUrls);
                        if (!fileDeleteResult.IsSuccess)
                        {
                            _logger.LogWarning("فشل حذف بعض الملفات للطلب {OrderId}: {Message}", order.Id, fileDeleteResult.Message);
                        }
                    }
                    await _unitOfWork.Orders.DeleteAsync(order.Id);
                }

                await _unitOfWork.SaveChangesAsync();

                _logger.LogInformation("تم حذف الطلبات القديمة بواسطة {UserName}", userName);
                return ServiceResult.Success("تم حذف الطلبات القديمة بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الطلبات القديمة بواسطة {UserName}", userName);
                return ServiceResult.Failure("حدث خطأ أثناء حذف الطلبات القديمة");
            }
        }

        public async Task<ServiceResult> DeleteSpecificOrderAsync(int orderId, string userName)
        {
            try
            {
                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }
                var fileUrls = new List<string>();
                if (!string.IsNullOrEmpty(order.File1Url)) fileUrls.Add(order.File1Url);
                if (!string.IsNullOrEmpty(order.File2Url)) fileUrls.Add(order.File2Url);
                if (!string.IsNullOrEmpty(order.File3Url)) fileUrls.Add(order.File3Url);
                if (!string.IsNullOrEmpty(order.File4Url)) fileUrls.Add(order.File4Url);
                if (fileUrls.Count > 0)
                {
                    var fileDeleteResult = await _fileService.DeleteFilesAsync(fileUrls);
                    if (!fileDeleteResult.IsSuccess)
                    {
                        _logger.LogWarning("فشل حذف بعض الملفات للطلب {OrderId}: {Message}", orderId, fileDeleteResult.Message);
                    }
                }
                await _unitOfWork.Orders.DeleteAsync(orderId);
                await _unitOfWork.SaveChangesAsync();
                
                _logger.LogInformation("تم حذف الطلب {OrderId} بواسطة {UserName}", orderId, userName);
                return ServiceResult.Success("تم حذف الطلب بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الطلب {OrderId} بواسطة {UserName}", orderId, userName);
                return ServiceResult.Failure("حدث خطأ أثناء حذف الطلب");
            }
        }

        public async Task<ServiceResult> DeleteOrderAttachmentsAsync(DeleteOldOrdersDto deleteRequest, string userName)
        {
            try
            {
                DateTime cutoffDate;
                List<OrdersTable> ordersToProcess;

                if (deleteRequest.UseCustomDateRange)
                {
                    if (!deleteRequest.StartDate.HasValue || !deleteRequest.EndDate.HasValue)
                    {
                        return ServiceResult.Failure("يجب تحديد تاريخ البداية والنهاية");
                    }

                    ordersToProcess = await _unitOfWork.Orders.GetOrdersByDateRangeAsync(
                        deleteRequest.StartDate.Value, deleteRequest.EndDate.Value);
                }
                else
                {
                    cutoffDate = DateTime.Now.AddMonths(-(int)deleteRequest.PeriodInMonths);
                    ordersToProcess = await _unitOfWork.Orders.GetOrdersOlderThanAsync(cutoffDate);
                }

                if (!ordersToProcess.Any())
                {
                    return ServiceResult.Success("لا توجد طلبات تحتوي على مرفقات في الفترة المحددة");
                }

                int deletedAttachmentsCount = 0;
                var filesToDelete = new List<string>();

                foreach (var order in ordersToProcess)
                {
                    // Collect file URLs to delete
                    if (!string.IsNullOrEmpty(order.File1Url))
                    {
                        filesToDelete.Add(order.File1Url);
                        order.File1Url = null;
                        deletedAttachmentsCount++;
                    }
                    if (!string.IsNullOrEmpty(order.File2Url))
                    {
                        filesToDelete.Add(order.File2Url);
                        order.File2Url = null;
                        deletedAttachmentsCount++;
                    }
                    if (!string.IsNullOrEmpty(order.File3Url))
                    {
                        filesToDelete.Add(order.File3Url);
                        order.File3Url = null;
                        deletedAttachmentsCount++;
                    }
                    if (!string.IsNullOrEmpty(order.File4Url))
                    {
                        filesToDelete.Add(order.File4Url);
                        order.File4Url = null;
                        deletedAttachmentsCount++;
                    }

                    // Update the order to remove file URLs
                    await _unitOfWork.Orders.UpdateAsync(order);
                }

                // Save database changes
                await _unitOfWork.SaveChangesAsync();

                // Delete physical files
                if (filesToDelete.Any())
                {
                    try
                    {
                        var fileDeleteResult = await _fileService.DeleteFilesAsync(filesToDelete);
                        if (!fileDeleteResult.IsSuccess)
                        {
                            _logger.LogWarning("فشل حذف بعض الملفات الفيزيائية: {Message}", fileDeleteResult.Message);
                        }
                    }
                    catch (Exception fileEx)
                    {
                        _logger.LogWarning(fileEx, "خطأ في حذف الملفات الفيزيائية، تم حذف المراجع من قاعدة البيانات فقط");
                    }
                }

                _logger.LogInformation("تم حذف {Count} مرفق من {OrderCount} طلب بواسطة {UserName}",
                    deletedAttachmentsCount, ordersToProcess.Count, userName);

                return ServiceResult.Success($"تم حذف {deletedAttachmentsCount} مرفق من {ordersToProcess.Count} طلب بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف مرفقات الطلبات");
                return ServiceResult.Failure("حدث خطأ أثناء حذف مرفقات الطلبات");
            }
        }
    }
}
