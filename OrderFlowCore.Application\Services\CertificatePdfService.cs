using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Services
{
    public class CertificatePdfService : ICertificatePdfService
    {
        private readonly IEnvironmentService _env;
        private readonly string _templatePath;

        public CertificatePdfService(IEnvironmentService env)
        {
            _env = env;
            _templatePath = Path.Combine(Directory.GetCurrentDirectory(), "Resources", "Templates", "Certificate.docx");
        }

        public async Task<ServiceResult<byte[]>> GenerateCertificatePdfAsync(Certificate certificate)
        {
            try
            {
                if (certificate == null)
                    return ServiceResult<byte[]>.Failure("Certificate data is required");

                if (!File.Exists(_templatePath))
                    return ServiceResult<byte[]>.Failure("Certificate template not found");

                using var templateStream = new MemoryStream(await File.ReadAllBytesAsync(_templatePath));
                using var wordDoc = WordprocessingDocument.Open(templateStream, true);

                FillBookmarks(wordDoc, certificate);
                wordDoc.Save();
                templateStream.Position = 0;

                var outputPath = Path.Combine(GetPrintedDirectory(), $"certificate_{certificate.Id}.pdf");
                return await ConvertToPdfAsync(templateStream, outputPath);
            }
            catch (Exception ex)
            {
                return ServiceResult<byte[]>.Failure($"Error generating certificate PDF: {ex.Message}");
            }
        }

        private void FillBookmarks(WordprocessingDocument wordDoc, Certificate cert)
        {
            var values = new Dictionary<string, string>
            {
                ["full_name"] = cert.FullName,
                ["nationality"] = cert.Nationality,
                ["residency"] = cert.ResidencyNumber,
                ["job_title"] = cert.JobTitle,
                ["contract_start"] = cert.ContractStartDate.ToString("yyyy/MM/dd"),
                ["contract_end"] = cert.ContractEndDate.ToString("yyyy/MM/dd"),
                ["is_working"] = cert.IsStillWorking ? "لا يزال على رأس العمل" : "غير على رأس العمل",
                ["recipient"] = cert.CertificateRecipient,
                ["department"] = cert.Department,
                ["issue_date"] = (cert.IssueDate ?? DateTime.Now).ToString("yyyy/MM/dd"),
                ["dm"] = cert.ConfirmedByDirectManager,
                ["coord"] = cert.ConfirmedByCoordinator,
                ["hr"] = cert.HumanResourcesManager
            };

            var bookmarks = wordDoc.MainDocumentPart.RootElement.Descendants<BookmarkStart>().ToList();
            foreach (var bm in bookmarks)
            {
                if (!values.TryGetValue(bm.Name, out var text) || string.IsNullOrWhiteSpace(text))
                    continue;

                var run = new Run(new Text(text));
                bm.Parent.InsertAfter(run, bm);
            }
        }

        private async Task<ServiceResult<byte[]>> ConvertToPdfAsync(MemoryStream wordStream, string filePath)
        {
            return await Task.Run(() =>
            {
                using var spireDoc = new Spire.Doc.Document();
                spireDoc.LoadFromStream(wordStream, Spire.Doc.FileFormat.Docx);
                spireDoc.SaveToFile(filePath, Spire.Doc.FileFormat.PDF);
                return ServiceResult<byte[]>.Success(File.ReadAllBytes(filePath));
            });
        }

        private string GetPrintedDirectory()
        {
            var dir = Path.Combine(_env.WebRootPath, "certificate_printed");
            Directory.CreateDirectory(dir);
            return dir;
        }
    }
}




