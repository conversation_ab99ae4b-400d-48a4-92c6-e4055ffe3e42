using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Common;

namespace OrderFlowCore.Application.Interfaces.Services
{
    public interface IAutomaticDeletionSettingsService
    {
        Task<ServiceResult<List<AutomaticDeletionSettingsDto>>> GetAllSettingsAsync();
        Task<ServiceResult<AutomaticDeletionSettingsDto>> GetSettingByIdAsync(int id);
        Task<ServiceResult<AutomaticDeletionSettingsDto>> CreateSettingAsync(AutomaticDeletionSettingsCreateDto createDto);
        Task<ServiceResult<AutomaticDeletionSettingsDto>> UpdateSettingAsync(AutomaticDeletionSettingsUpdateDto updateDto);
        Task<ServiceResult<bool>> DeleteSettingAsync(int id);
        Task<ServiceResult<List<AutomaticDeletionSettingsDto>>> GetActiveSettingsAsync();
        Task<ServiceResult<bool>> ExecuteAutomaticDeletionAsync();
    }
}
