using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Core.Models;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Application.DTOs;
using System;

namespace OrderFlowCore.Infrastructure.Data
{
    public class OrderRepository : IOrderRepository
    {
        private readonly ApplicationDbContext _context;

        public OrderRepository(ApplicationDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        #region Basic CRUD Operations

        public async Task<int> AddAsync(OrdersTable order)
        {
            if (order == null)
                throw new ArgumentNullException(nameof(order));

            await _context.OrdersTables.AddAsync(order);
            return order.Id;
        }

        public async Task<int> UpdateAsync(OrdersTable order)
        {
            if (order == null)
                throw new ArgumentNullException(nameof(order));

            _context.OrdersTables.Update(order);
            return order.Id;
        }

        public async Task<OrdersTable> GetByIdAsync(int id)
        {
            return await _context.OrdersTables.FindAsync(id);
        }

        public async Task<List<OrdersTable>> GetAllAsync()
        {
            return await _context.OrdersTables.AsNoTracking().ToListAsync();
        }

        public async Task<OrdersTable?> GetOrderByIdAsync(int orderId)
        {
            return await _context.OrdersTables.FindAsync(orderId);
        }

        public async Task<OrdersTable?> GetByCivilRecordAsync(string civilRecord)
        {
            return await _context.OrdersTables
                .FirstOrDefaultAsync(o => o.CivilRecord == civilRecord);
        }

        #endregion

        #region Status-based Queries

        public async Task<List<OrdersTable>> GetDirectMangerPendingOrdersAsync(string roleType)
        {
            return await _context.OrdersTables
                .Where(o => (o.OrderStatus == OrderStatus.DM ||
                           o.OrderStatus == OrderStatus.ReturnedByAssistantManager ||
                           o.OrderStatus == OrderStatus.ReturnedByCoordinator) && o.Department == roleType)
                .AsNoTracking()
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<OrdersTable>> GetAssistantManagerPendingOrdersAsync(AssistantManagerType assistantManagerId)
        {
            var status = assistantManagerId.ToOrderStatus();

            return await _context.OrdersTables
                .Where(o => o.OrderStatus == status)
                .AsNoTracking()
                .OrderByDescending(o => o.CreatedAt)
                .ThenByDescending(o => o.Id)
                .ToListAsync();
        }
        public async Task<List<OrdersTable>> GetHRCoordinatorPendingOrdersAsync()
        {
            var allowedStatuses = new[]
            {
                OrderStatus.B,
                OrderStatus.ReturnedByManager,
                OrderStatus.ReturnedBySupervisor,
                OrderStatus.ActionRequired
            };

            return await _context.OrdersTables
                .Where(o => allowedStatuses.Contains(o.OrderStatus))
                .AsNoTracking()
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<OrdersTable>> GetSupervisorsPendingOrdersAsync()
        {
            var allowedStatuses = new[]
            {
                OrderStatus.C,
                OrderStatus.ActionRequiredBySupervisor,
                OrderStatus.ReturnedBySupervisor
            };

            return await _context.OrdersTables
                .Where(o => allowedStatuses.Contains(o.OrderStatus))
                .AsNoTracking()
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<OrdersTable>> GetHRMangerPendingOrdersAsync()
        {
            var allowedStatuses = new[]
            {
                OrderStatus.D
            };

            return await _context.OrdersTables
                .Where(o => allowedStatuses.Contains(o.OrderStatus))
                .AsNoTracking()
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<OrdersTable>> GetOrdersByStatusesAsync(OrderStatus[] statuses)
        {
            if (statuses == null || !statuses.Any())
                return new List<OrdersTable>();

            return await _context.OrdersTables
                .Where(o => statuses.Contains(o.OrderStatus))
                .AsNoTracking()
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<OrdersTable>> GetOrdersByStatusAsync(OrderStatus status)
        {
            return await _context.OrdersTables
                .Where(o => o.OrderStatus == status)
                .AsNoTracking()
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<OrdersTable>> GetOrdersByDateRangeAsync(DateTime startDate, DateTime endDate)
        {
            return await _context.OrdersTables
                .Where(o => o.CreatedAt >= startDate && o.CreatedAt <= endDate)
                .AsNoTracking()
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task<List<OrdersTable>> GetOrdersOlderThanAsync(DateTime cutoffDate)
        {
            return await _context.OrdersTables
                .Where(o => o.CreatedAt < cutoffDate)
                .AsNoTracking()
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        public async Task DeleteAsync(int orderId)
        {
            var order = await _context.OrdersTables.FindAsync(orderId);
            if (order != null)
            {
                _context.OrdersTables.Remove(order);
            }
        }

        public async Task<(List<OrdersTable> Orders, int TotalCount)> GetFilteredOrdersAsync(
            OrderStatus? status = null,
            string? department = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            string? civilRecord = null,
            int page = 1,
            int pageSize = 20)
        {
            var query = BuildFilteredQuery(status, department, fromDate, toDate, civilRecord);

            // Get total count before paging (single database call)
            var totalCount = await query.CountAsync();

            // Apply ordering and paging
            var orders = await query
                .OrderByDescending(o => o.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (orders, totalCount);
        }

        public async Task<List<OrdersTable>> GetFilteredOrdersForExportAsync(
            OrderStatus? status = null,
            string? department = null,
            DateTime? fromDate = null,
            DateTime? toDate = null,
            string? civilRecord = null)
        {
            var query = BuildFilteredQuery(status, department, fromDate, toDate, civilRecord);

            // Return all filtered results ordered by creation date (for export)
            return await query
                .OrderByDescending(o => o.CreatedAt)
                .ToListAsync();
        }

        private IQueryable<OrdersTable> BuildFilteredQuery(
            OrderStatus? status,
            string? department,
            DateTime? fromDate,
            DateTime? toDate,
            string? civilRecord)
        {
            var query = _context.OrdersTables.AsNoTracking();

            // Apply most selective filters first for better query optimization

            // Date range filter (usually most selective)
            if (fromDate.HasValue && toDate.HasValue)
            {
                query = query.Where(o => o.CreatedAt >= fromDate.Value && o.CreatedAt <= toDate.Value);
            }
            else if (fromDate.HasValue)
            {
                query = query.Where(o => o.CreatedAt >= fromDate.Value);
            }
            else if (toDate.HasValue)
            {
                query = query.Where(o => o.CreatedAt <= toDate.Value);
            }

            // Status filter (indexed field)
            if (status.HasValue)
            {
                query = query.Where(o => o.OrderStatus == status.Value);
            }

            // Department filter
            if (!string.IsNullOrEmpty(department) && department != "All")
            {
                query = query.Where(o => o.Department == department);
            }

            // Civil record filter (potentially expensive LIKE operation, apply last)
            if (!string.IsNullOrEmpty(civilRecord))
            {
                query = query.Where(o => o.CivilRecord.Contains(civilRecord));
            }

            return query;
        }

        #endregion
    }
}