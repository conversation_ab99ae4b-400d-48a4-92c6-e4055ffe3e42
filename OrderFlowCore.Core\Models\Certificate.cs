using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Core.Models;

[Table("certificates")]
public class Certificate
{
    [Key]
    public int Id { get; set; }

    // Employee info
    [Required, StringLength(200)]
    public string FullName { get; set; }
    [StringLength(100)]
    public string Nationality { get; set; }
    [StringLength(100)]
    public string ResidencyNumber { get; set; }

    // Contract details
    [StringLength(150)]
    public string JobTitle { get; set; }
    public DateTime ContractStartDate { get; set; }
    public DateTime ContractEndDate { get; set; }

    // Current status
    public bool IsStillWorking { get; set; }

    // Certificate purpose
    [StringLength(200)]
    public string CertificateRecipient { get; set; }
    [StringLength(150)]
    public string Department { get; set; }

    // Certificate issue date
    public DateTime? IssueDate { get; set; }
    public DateTime CreatedAt { get; set; }
    public CertificateStatus CertificateStatus { get; set; }

    [StringLength(255)]
    public string ConfirmedByDirectManager { get; set; }
    [StringLength(255)]
    public string ConfirmedByCoordinator { get; set; }
    [StringLength(500)]
    public string CoordinatorDetails { get; set; }
    [StringLength(255)]
    public string HumanResourcesManager { get; set; }
    [StringLength(500)]
    public string ReasonForCancellation { get; set; }
}



