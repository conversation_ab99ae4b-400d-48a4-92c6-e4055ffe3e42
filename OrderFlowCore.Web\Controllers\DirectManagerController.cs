using Microsoft.AspNetCore.Mvc;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Web.ViewModels;
using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.Extensions.Logging;
using System.IO.Compression;
using System.IO;
using OrderFlowCore.Web.Attributes;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Web.Extentions;

namespace OrderFlowCore.Web.Controllers
{
    [AuthorizeRole(UserRole.DirectManager)]
    public class DirectManagerController : Controller
    {
        private readonly IDirectManagerOrderService _directManagerOrderService;
        private readonly IOrderManagementService _orderManagementService;
        private readonly ILogger<DirectManagerController> _logger;

        public DirectManagerController(IDirectManagerOrderService directManagerOrderService, IOrderManagementService orderManagementService, ILogger<DirectManagerController> logger)
        {
            _directManagerOrderService = directManagerOrderService;
            _orderManagementService = orderManagementService;
            _logger = logger;
        }

        [HttpGet]
        [HttpGet]
        public async Task<IActionResult> Index()
        {
            var viewModel = new DirectManagerViewModel();

            var roleType = User.GetUserRoleType();
            var ordersResult = await _directManagerOrderService.GetPendingOrdersForDirectMangerAsync(roleType);
            if (ordersResult.IsSuccess)
            {
                viewModel.OrderNumbers = ordersResult.Data.Select(o => new SelectListItem
                {
                    Value = o.Id.ToString(),
                    Text = GetOrderDisplayText(o)
                }).ToList();
            }

            return View(viewModel);
        }

        private string GetOrderDisplayText(OrderSummaryDto o)
        {
            var icon = "";

            if (!string.IsNullOrEmpty(o.AdditionalInfo))
            {
                icon = o.AdditionalInfo switch
                {
                    "جديد" => "🟢 ", // أخضر
                    "مُعاد من المنسق" => "🟠 ", // برتقالي
                    _ => ""
                };
            }

            return $"{icon}طلب #{o.Id} - {o.EmployeeName}";
        }


        [HttpPost]
        public async Task<IActionResult> GetOrderDetails(int orderId)
        {
            var result = await _orderManagementService.GetOrderDetailsAsync(orderId);

            return Json(new { success = result.IsSuccess, message = result.Message, data = result.Data });

        }

        [HttpPost]
        public async Task<IActionResult> ConfirmOrder(int orderId)
        {
            var userName = User.Identity?.Name;
            var result = await _directManagerOrderService.ConfirmOrderByDirectManagerAsync(orderId, userName);

            return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpPost]
        public async Task<IActionResult> RejectOrder(int orderId, string reason)
        {
            if (string.IsNullOrWhiteSpace(reason))
            {
                TempData["ErrorMessage"] = "يجب إدخال سبب الإلغاء";
                return RedirectToAction(nameof(Index));
            }
            var userName = User.Identity?.Name;
            var result = await _directManagerOrderService.RejectOrderByDirectManagerAsync(orderId, reason, userName);

            return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpGet]
        public async Task<IActionResult> DownloadAttachments(int id)
        {
            var result = await _orderManagementService.DownloadAttachmentsZipAsync(id);

            if (result.IsSuccess)
            {
                string zipFileName = $"مرفقات_طلب_{id}.zip";
                return File(result.Data, "application/zip", zipFileName);
            }
            else
            {
                TempData["ErrorMessage"] = result.Message;
                return RedirectToAction(nameof(Index));
            }

        }
    }
}