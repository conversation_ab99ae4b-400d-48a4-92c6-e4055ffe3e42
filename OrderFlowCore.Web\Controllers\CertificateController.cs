using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Web.ViewModels;
using System.Threading.Tasks;

namespace OrderFlowCore.Web.Controllers
{
    public class CertificateController : Controller
    {
        private readonly ICertificateService _certificateService;
        private readonly ILogger<CertificateController> _logger;
        private readonly ICertificatePdfService _certificatePdfService;

        public CertificateController(ICertificateService certificateService, ICertificatePdfService certificatePdfService, ILogger<CertificateController> logger)
        {
            _certificateService = certificateService;
            _certificatePdfService = certificatePdfService;
            _logger = logger;
        }

        [HttpGet]
        public IActionResult New()
        {
            return View(new CertificateNewViewModel());
        }

        [HttpGet]
        public async Task<IActionResult> Print(int id)
        {
            var certResult = await _certificateService.GetByIdAsync(id);
            if (!certResult.IsSuccess || certResult.Data == null)
                return NotFound();

            var pdfResult = await _certificatePdfService.GenerateCertificatePdfAsync(certResult.Data);
            if (!pdfResult.IsSuccess || pdfResult.Data == null)
                return BadRequest(pdfResult.Message);

            return File(pdfResult.Data, "application/pdf", $"certificate_{id}.pdf");
        }
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> New(CertificateNewViewModel model)
        {
            if (!ModelState.IsValid)
                return View(model);

            var entity = CertificateNewViewModel.ToEntity(model);
            var result = await _certificateService.CreateAsync(entity);
            if (result.IsSuccess)
            {
                TempData["CertificateSuccess"] = true;
                TempData["CertificateId"] = result.Data;
                return RedirectToAction(nameof(New));
            }

            ModelState.AddModelError("", result.Message);
            return View(model);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ConfirmByDirectManager(int id)
        {
            var userName = User.Identity?.Name ?? "System";
            var result = await _certificateService.ConfirmByDirectManagerAsync(id, userName);
            return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ConfirmByCoordinator(int id, string details)
        {
            var userName = User.Identity?.Name ?? "System";
            var result = await _certificateService.ConfirmByCoordinatorAsync(id, userName, details);
            return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ConfirmByHRManager(int id)
        {
            var userName = User.Identity?.Name ?? "System";
            var result = await _certificateService.ConfirmByHRManagerAsync(id, userName);
            return Json(new { success = result.IsSuccess, message = result.Message });
        }
    }
}


