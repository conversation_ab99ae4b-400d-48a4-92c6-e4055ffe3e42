@using OrderFlowCore.Web.Extentions
@model OrderFlowCore.Web.ViewModels.SupervisorOrdersViewModel
@{
    ViewData["Title"] = "مشرف الطلبات";
}

<div class="container-fluid mt-4">
    @Html.AntiForgeryToken()

    <!-- Page Header -->
    <div class="card-header-custom mb-4">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h2><i class="fas fa-user-tie ml-2"></i> @User.GetUserRoleType()</h2>
                <p>معالجة الطلبات المحولة واتخاذ الإجراءات المناسبة</p>
            </div>
            <div class="col-md-4 text-left">
                <a href="@Url.Action("FollowUpRecords", "SupervisorOrders")" class="btn btn-secondary btn-lg">
                    <i class="fas fa-clipboard-list ml-1"></i> متابعة السجلات
                </a>
            </div>
        </div>
    </div>

    <!-- Order Selection Section -->
    <div class="card-custom mb-4">
        <div class="card-header-custom">
            <h5 class="mb-0"><i class="fas fa-search ml-2"></i> اختيار الطلب / Select Order</h5>
        </div>
        <div class="card-body">
            <div class="row align-items-center">
                <div class="col-md-7">
                    <label for="orderSelect" class="form-label">📋 رقم الطلب:</label>
                    @Html.DropDownListFor(m => m.SelectedOrderId, Model.OrderNumbers, "اختر الطلب من القائمة",
                             new { @class = "form-select form-select-lg", @id = "orderSelect" })
                </div>
                <div class="col-md-5">
                    <!-- منطقة عرض الحالة السريعة -->
                    <div id="quickStatusArea" class="d-none">
                        <div class="alert mb-0" id="statusAlert">
                            <strong>الحالة:</strong> <span id="statusText"></span>
                            <div id="statusDetails" class="small mt-1"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Processing Section (Hidden by default) -->
    <div id="processingSection" style="display: none;">
        <div class="row">
            <!-- Left Column: Order Processing Actions -->
            <div class="col-lg-4">
                <!-- Primary Actions Card -->
                <div class="card-custom mb-4">
                    <div class="card-header-custom bg-success text-white">
                        <h6 class="mb-0"><i class="fas fa-play-circle ml-2"></i> الإجراءات الرئيسية</h6>
                    </div>
                    <div class="card-body">
                        <button type="button" class="btn btn-success btn-lg w-100 mb-3" onclick="SupervisorOrders.submitOrder()">
                            <i class="fas fa-check-circle ml-1"></i> اعتماد الطلب
                        </button>
                        <button type="button" class="btn btn-info btn-lg w-100 mb-3" onclick="SupervisorOrders.markNeedsAction()">
                            <i class="fas fa-exclamation-triangle ml-1"></i> يتطلب إجراءات
                        </button>
                        <button type="button" class="btn btn-warning btn-lg w-100 mb-3" onclick="SupervisorOrders.returnOrder()">
                            <i class="fas fa-undo ml-1"></i> إعادة الطلب
                        </button>
                    </div>
                </div>

                <!-- Quick Actions Card -->
                <div class="card-custom mb-4">
                    <div class="card-header-custom bg-info text-white">
                        <h6 class="mb-0"><i class="fas fa-bolt ml-2"></i> إجراءات سريعة</h6>
                    </div>
                    <div class="card-body">
                        <button id="downloadAttachmentsBtn" class="btn btn-outline-secondary w-100" onclick="SupervisorOrders.downloadAttachments()">
                            <i class="fas fa-download ml-1"></i> تحميل المرفقات
                        </button>
                    </div>
                </div>
            </div>

            <!-- Right Column: Form Fields and Order Details -->
            <div class="col-lg-8">
                <!-- Processing Form Card -->
                <div class="card-custom mb-4">
                    <div class="card-header-custom bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-edit ml-2"></i> تفاصيل المعالجة</h5>
                    </div>
                    <div class="card-body">
                        @Html.HiddenFor(m => m.SelectedOrderId, new { @id = "hiddenOrderId" })

                        <div class="mb-3">
                            <label for="actionRequired" class="form-label fw-bold">الإجراءات المطلوبة</label>
                            <textarea class="form-control" id="actionRequired" name="actionRequired"
                                placeholder="حدد الإجراءات المطلوبة..." rows="3"></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="returnReason" class="form-label fw-bold">سبب الإلغاء/الإعادة</label>
                            <textarea class="form-control" id="returnReason" name="returnReason"
                                placeholder="اذكر سبب الإعادة..." rows="3"></textarea>
                        </div>
                    </div>
                </div>

                <!-- Messages Container -->
                <div id="messageContainer" class="mt-3"></div>
                <div id="specialActionMessageContainer" class="mt-3"></div>
                <div id="SupervisorActionRequireMessageContainer" class="mt-3"></div>
            </div>
        </div>
    </div>

    <!-- Loading -->
    <div id="loading" class="loading text-center" style="display: none;">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
        <p class="mt-2">جاري تحميل تفاصيل الطلب...</p>
    </div>

    <!-- Order Details Section -->
    <div id="orderDetailsSection" style="display: none;">
        <div class="card-custom mt-4">
            <div class="card-header-custom bg-light">
                <h5 class="mb-0"><i class="fas fa-file-alt ml-2"></i> تفاصيل الطلب</h5>
            </div>
            <div class="card-body">
                @await Html.PartialAsync("_OrderDetailsPartial")
            </div>
        </div>
    </div>
</div>

<!-- Confirm Submit Modal -->
<div class="modal fade" id="confirmSubmitModal" tabindex="-1" aria-labelledby="confirmSubmitModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmSubmitModalLabel">تأكيد التحويل</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-right">
                هل أنت متأكد من تحويل الطلب؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" id="confirmSubmitModalBtn">نعم، تحويل</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Action Required Modal -->
<div class="modal fade" id="confirmActionRequiredModal" tabindex="-1" aria-labelledby="confirmActionRequiredModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmActionRequiredModalLabel">تأكيد الإجراء المطلوب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-right">
                هل أنت متأكد من أن هذا الطلب يتطلب إجراءات إضافية؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-info" id="confirmActionRequiredModalBtn">نعم، يتطلب إجراءات</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Return Modal -->
<div class="modal fade" id="confirmReturnModal" tabindex="-1" aria-labelledby="confirmReturnModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmReturnModalLabel">تأكيد إعادة الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-right">
                هل أنت متأكد من إعادة الطلب؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" id="confirmReturnModalBtn">نعم، إعادة</button>
            </div>
        </div>
    </div>
</div>

@section Styles {
    <style>
        .processing-section {
            transition: all 0.3s ease-in-out;
        }

        .card-custom {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border: none;
            border-radius: 8px;
        }

        .card-header-custom {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px 8px 0 0;
            padding: 1rem;
        }

        .btn-lg {
            padding: 0.75rem 1.5rem;
            font-size: 1.1rem;
        }

        .form-select-lg {
            padding: 0.75rem 1rem;
            font-size: 1.1rem;
        }

        #processingSection {
            animation: fadeIn 0.5s ease-in-out;
        }

        @@keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        #quickStatusArea .alert {
            border-left-width: 4px;
            transition: all 0.3s ease;
        }

        #quickStatusArea .alert-info {
            border-left-color: #0dcaf0;
            background-color: rgba(13, 202, 240, 0.1);
        }

        #quickStatusArea .alert-warning {
            border-left-color: #ffc107;
            background-color: rgba(255, 193, 7, 0.1);
        }

        #quickStatusArea .alert-secondary {
            border-left-color: #6c757d;
            background-color: rgba(108, 117, 125, 0.1);
        }

        #statusDetails {
            color: #666;
            margin-top: 5px;
        }

        #orderSelect option {
            padding: 5px;
        }
    </style>
}

@section Scripts {
    <script src="~/js/orderDetailsModule.js"></script>
    <script src="~/js/supervisorOrders.js"></script>
}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
       $(document).ready(function() {
        $('#orderSelect').on('change', function() {
            const selectedText = $(this).find('option:selected').text();
            const selectedValue = $(this).val();

            if (!selectedValue || selectedText === 'اختر الطلب من القائمة') {
                $('#quickStatusArea').addClass('d-none');
                return;
            }

            let alertClass = '';
            let statusMessage = '';
            let detailsMessage = '';

            // تحديد نوع الحالة بناءً على النص
            if (selectedText.includes('🔵')) {
                alertClass = 'alert-info';
                statusMessage = '🔵 طلب جديد';
                detailsMessage = 'طلب جديد لم يتم معالجته بعد';
            }
            else if (selectedText.includes('🟡')) {
                alertClass = 'alert-warning';
                statusMessage = '🟡 يتطلب إجراءات';
                detailsMessage = 'هذا الطلب يتطلب إجراءات من المنسق أو المشرفين';
            }
            else {
                alertClass = 'alert-secondary';
                statusMessage = '📋 طلب للمعالجة';
                detailsMessage = 'طلب في انتظار المعالجة';
            }

            // تحديث العناصر
            $('#statusText').text(statusMessage);
            $('#statusDetails').html(`<i class="fas fa-info-circle me-1"></i>${detailsMessage}`);

            // تحديث الكلاسات
            $('#statusAlert')
                .removeClass('alert-info alert-warning alert-danger alert-secondary')
                .addClass(alertClass);

            // إظهار المنطقة
            $('#quickStatusArea').removeClass('d-none').hide().fadeIn(300);
        });

        // تشغيل التغيير عند تحميل الصفحة إذا كان هناك طلب محدد
        if ($('#orderSelect').val()) {
            $('#orderSelect').trigger('change');
        }
    });


</script>