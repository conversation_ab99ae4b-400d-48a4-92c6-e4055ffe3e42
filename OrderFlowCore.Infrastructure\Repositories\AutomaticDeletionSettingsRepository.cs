using Microsoft.EntityFrameworkCore;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Infrastructure.Data;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderFlowCore.Infrastructure.Repositories
{
    public class AutomaticDeletionSettingsRepository : IAutomaticDeletionSettingsRepository
    {
        private readonly ApplicationDbContext _context;

        public AutomaticDeletionSettingsRepository(ApplicationDbContext context)
        {
            _context = context;
        }

        public async Task<int> AddAsync(AutomaticDeletionSettings setting)
        {
            _context.Set<AutomaticDeletionSettings>().Add(setting);
            await _context.SaveChangesAsync();
            return setting.Id;
        }

        public async Task<int> UpdateAsync(AutomaticDeletionSettings setting)
        {
            _context.Set<AutomaticDeletionSettings>().Update(setting);
            return await _context.SaveChangesAsync();
        }

        public async Task<AutomaticDeletionSettings> GetByIdAsync(int id)
        {
            return await _context.Set<AutomaticDeletionSettings>().FindAsync(id);
        }

        public async Task<List<AutomaticDeletionSettings>> GetAllAsync()
        {
            return await _context.Set<AutomaticDeletionSettings>().ToListAsync();
        }

        public async Task DeleteAsync(AutomaticDeletionSettings setting)
        {
            _context.Set<AutomaticDeletionSettings>().Remove(setting);
            await _context.SaveChangesAsync();
        }

        public async Task<IEnumerable<AutomaticDeletionSettings>> GetActiveSettingsAsync()
        {
            return await _context.Set<AutomaticDeletionSettings>()
                .Where(s => s.IsActive)
                .OrderBy(s => s.PeriodInMonths)
                .ToListAsync();
        }
    }
}
