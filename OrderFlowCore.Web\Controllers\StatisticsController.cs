using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using StatisticsViewModel = OrderFlowCore.Application.DTOs.StatisticsViewModel;

namespace OrderFlowCore.Web.Controllers
{
    [Authorize]
    public class StatisticsController : Controller
    {
        private readonly IStatisticsService _statisticsService;
        private readonly ILogger<StatisticsController> _logger;

        public StatisticsController(
            IStatisticsService statisticsService,
            ILogger<StatisticsController> logger)
        {
            _statisticsService = statisticsService ?? throw new ArgumentNullException(nameof(statisticsService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<IActionResult> Index()
        {
            try
            {
                var viewModel = new StatisticsViewModel();
                var errorMessages = new List<string>();

                // تنفيذ استعلامات الإحصائيات تدريجياً لتجنب مشاكل DbContext

                // إحصائيات عامة
                var generalStatsResult = await _statisticsService.GetGeneralStatisticsAsync();
                ProcessStatisticsResult(generalStatsResult,
                    result => viewModel.GeneralStatistics = result,
                    errorMessages);

                // إحصائيات المراحل
                var stageStatsResult = await _statisticsService.GetStageStatisticsAsync();
                ProcessStatisticsResult(stageStatsResult,
                    result => viewModel.StageStatistics = result,
                    errorMessages);

                // إحصائيات الأقسام
                var departmentStatsResult = await _statisticsService.GetDepartmentStatisticsAsync();
                ProcessStatisticsResult(departmentStatsResult,
                    result => viewModel.DepartmentStatistics = result,
                    errorMessages);

                // إحصائيات المشرفين
                var supervisorStatsResult = await _statisticsService.GetSupervisorStatisticsAsync();
                ProcessStatisticsResult(supervisorStatsResult,
                    result => viewModel.SupervisorStatistics = result,
                    errorMessages);

                // إحصائيات مساعدي المدير
                var assistantManagerStatsResult = await _statisticsService.GetAssistantManagerStatisticsAsync();
                ProcessStatisticsResult(assistantManagerStatsResult,
                    result => viewModel.AssistantManagerStatistics = result,
                    errorMessages);

                // إحصائيات نوع التحويل
                var transferTypeStatsResult = await _statisticsService.GetTransferTypeStatisticsAsync();
                ProcessStatisticsResult(transferTypeStatsResult,
                    result => viewModel.TransferTypeStatistics = result,
                    errorMessages);

                // إحصائيات منسقي الموارد البشرية
                var hrCoordinatorStatsResult = await _statisticsService.GetHRCoordinatorStatisticsAsync();
                ProcessStatisticsResult(hrCoordinatorStatsResult,
                    result => viewModel.HRCoordinatorStatistics = result,
                    errorMessages);

                // إحصائيات مديري الموارد البشرية
                var hrManagerStatsResult = await _statisticsService.GetHRManagerStatisticsAsync();
                ProcessStatisticsResult(hrManagerStatsResult,
                    result => viewModel.HRManagerStatistics = result,
                    errorMessages);

                // إحصائيات الطلبات المنجزة حسب المشرفين - الجديد
                var completedSupervisorStatsResult = await _statisticsService.GetCompletedSupervisorStatisticsAsync();
                ProcessStatisticsResult(completedSupervisorStatsResult,
                    result => viewModel.CompletedSupervisorStatistics = result,
                    errorMessages);


                // 🔧 إضافة الدوال الجديدة - الطلبات المعلقة
                var pendingOrdersResult = await _statisticsService.GetPendingOrdersStatisticsAsync();
                ProcessStatisticsResult(pendingOrdersResult,
                    result =>
                    {
                        viewModel.PendingOrdersStatistics = result;
                        _logger.LogInformation($"✅ تم جلب {result.Count} إحصائية طلبات معلقة");
                    },
                    errorMessages);
                // 🆕 إضافة إحصائيات التأخير للمشرفين - الجدول الجديد
                var supervisorDelayStatsResult = await _statisticsService.GetSupervisorDelayStatisticsAsync();
                ProcessStatisticsResult(supervisorDelayStatsResult,
                    result =>
                    {
                        viewModel.SupervisorDelayStatistics = result;
                        _logger.LogInformation($"✅ تم جلب {result.Count} إحصائية تأخير للمشرفين");
                    },
            errorMessages);

                // الإحصائيات الشهرية
                var monthlyStatsResult = await _statisticsService.GetMonthlyStatisticsAsync();
                ProcessStatisticsResult(monthlyStatsResult,
                    result => viewModel.MonthlyStatistics = result,
                    errorMessages);

                // أنواع الطلبات
                var orderTypesResult = await _statisticsService.GetOrderTypesStatisticsAsync();
                ProcessStatisticsResult(orderTypesResult,
                    result => viewModel.OrderTypesStatistics = result,
                    errorMessages);
                // توزيع الطلبات حسب الحالة
                var orderStatusResult = await _statisticsService.GetOrderStatusStatisticsAsync();
                ProcessStatisticsResult(orderStatusResult,
                    result => viewModel.OrderStatusStatistics = result,
                    errorMessages);

                // إحصائيات الطلبات المعلقة الموحدة
                var unifiedPendingResult = await _statisticsService.GetUnifiedPendingStatisticsAsync();
                ProcessStatisticsResult(unifiedPendingResult,
                    result => {
                        viewModel.UnifiedPendingStats = result.OrderBy(x => x.Ranking).ToList();
                        _logger.LogInformation($"✅ تم جلب {result.Count} كيان بطلبات معلقة");
                    },
                    errorMessages);
                // تعيين رسائل الخطأ إن وجدت
                if (errorMessages.Any())
                {
                    TempData["ErrorMessage"] = string.Join("; ", errorMessages);
                    _logger.LogWarning("Statistics loaded with errors: {Errors}", string.Join("; ", errorMessages));
                }
                else
                {
                    _logger.LogInformation("Statistics loaded successfully");
                }

                // 🔧 تسجيل إحصائيات التحميل
                _logger.LogInformation($"📊 إحصائيات محملة:");
                _logger.LogInformation($"  المشرفين الأساسي: {viewModel.SupervisorStatistics?.Count ?? 0}");
                _logger.LogInformation($"  الطلبات المعلقة: {viewModel.PendingOrdersStatistics?.Count ?? 0}");
                _logger.LogInformation($"  تأخير المشرفين: {viewModel.SupervisorDelayStatistics?.Count ?? 0}");

                return View(viewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading statistics");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تحميل الإحصائيات";
                return View(new StatisticsViewModel());
            }
        }

        /// <summary>
        /// API endpoint for getting statistics data as JSON (useful for AJAX calls)
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetStatisticsData(string type)
        {
            try
            {
                return type?.ToLower() switch
                {
                    "general" => Json(await _statisticsService.GetGeneralStatisticsAsync()),
                    "stage" => Json(await _statisticsService.GetStageStatisticsAsync()),
                    "department" => Json(await _statisticsService.GetDepartmentStatisticsAsync()),
                    "supervisor" => Json(await _statisticsService.GetSupervisorStatisticsAsync()),
                    "assistantmanager" => Json(await _statisticsService.GetAssistantManagerStatisticsAsync()),
                    "transfertype" => Json(await _statisticsService.GetTransferTypeStatisticsAsync()),
                    "hrcoordinator" => Json(await _statisticsService.GetHRCoordinatorStatisticsAsync()),
                    "hrmanager" => Json(await _statisticsService.GetHRManagerStatisticsAsync()),
                    "completedsupervisor" => Json(await _statisticsService.GetCompletedSupervisorStatisticsAsync()),
                    "pending" => Json(await _statisticsService.GetPendingOrdersStatisticsAsync()),
                    "supervisordelay" => Json(await _statisticsService.GetSupervisorDelayStatisticsAsync()),
                    "delay" => Json(await _statisticsService.GetSupervisorDelayStatisticsAsync()), // اختصار
                    "unified" => Json(await _statisticsService.GetUnifiedPendingStatisticsAsync()), // 🆕 جديد
                    "monthly" => Json(await _statisticsService.GetMonthlyStatisticsAsync()), //  الإحصائيات الشهرية جديد
                    "ordertypes" => Json(await _statisticsService.GetOrderTypesStatisticsAsync()), // ✅ جديد - أنواع الطلبات
                    "orderstatus" => Json(await _statisticsService.GetOrderStatusStatisticsAsync()),// 🆕  توزيع الطلبات حسب الحالة
                    _ => BadRequest("Invalid statistics type")
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting statistics data for type: {Type}", type);
                return StatusCode(500, "حدث خطأ أثناء جلب البيانات");
            }
        }

        /// <summary>
        /// 🔧 نقطة وصول جديدة للتحليل الشامل للمشرفين
        /// </summary>
        [HttpGet("supervisor-analysis")]
        public async Task<IActionResult> GetSupervisorAnalysis()
        {
            try
            {
                // جلب البيانات الأساسية
                var basicResult = await _statisticsService.GetSupervisorStatisticsAsync();
                if (!basicResult.IsSuccess)
                {
                    return BadRequest($"فشل جلب البيانات الأساسية: {basicResult.Message}");
                }

                // جلب الطلبات المعلقة
                var pendingResult = await _statisticsService.GetPendingOrdersStatisticsAsync();
                if (!pendingResult.IsSuccess)
                {
                    _logger.LogWarning($"فشل جلب الطلبات المعلقة: {pendingResult.Message}");
                }

                var response = new
                {
                    BasicStatistics = basicResult.Data,
                    PendingOrders = pendingResult.IsSuccess ? pendingResult.Data : new List<PendingOrdersStatisticsDto>(),
                    Success = true,
                    Message = "تم جلب التحليل الشامل بنجاح",
                    DataSources = new
                    {
                        BasicData = basicResult.IsSuccess,
                        PendingData = pendingResult.IsSuccess
                    }
                };

                return Json(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في التحليل الشامل للمشرفين");
                return StatusCode(500, new
                {
                    Success = false,
                    Message = "حدث خطأ في الخادم",
                    Error = ex.Message
                });
            }
        }

        /// <summary>
        /// Get aggregated statistics for dashboard
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetAggregatedStatistics()
        {
            try
            {
                // Execute all statistics calls sequentially
                var generalStats = await _statisticsService.GetGeneralStatisticsAsync();
                var stageStats = await _statisticsService.GetStageStatisticsAsync();
                var departmentStats = await _statisticsService.GetDepartmentStatisticsAsync();

                var result = new
                {
                    General = generalStats.IsSuccess ? generalStats.Data : null,
                    Stages = stageStats.IsSuccess ? stageStats.Data : null,
                    Departments = departmentStats.IsSuccess ? departmentStats.Data : null,
                    Errors = new List<string>()
                        .Concat(generalStats.IsSuccess ? new string[0] : new[] { generalStats.Message })
                        .Concat(stageStats.IsSuccess ? new string[0] : new[] { stageStats.Message })
                        .Concat(departmentStats.IsSuccess ? new string[0] : new[] { departmentStats.Message })
                        .Where(x => !string.IsNullOrEmpty(x))
                        .ToList()
                };

                return Json(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting aggregated statistics");
                return StatusCode(500, new { Error = "حدث خطأ أثناء جلب البيانات المجمعة" });
            }
        }

        /// <summary>
        /// Refresh statistics endpoint for AJAX calls
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> RefreshStatistics()
        {
            try
            {
                // Just return success - the page will reload
                // You could implement cache clearing here if needed
                await Task.Delay(100); // Simulate some processing time

                return Json(new { success = true, message = "تم تحديث الإحصائيات بنجاح" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing statistics");
                return Json(new { success = false, message = "حدث خطأ أثناء تحديث الإحصائيات" });
            }
        }

        /// <summary>
        /// Helper method to process service results and handle errors consistently
        /// </summary>
        private static void ProcessStatisticsResult<T>(
            ServiceResult<T> result,
            Action<T> onSuccess,
            List<string> errorMessages)
        {
            if (result.IsSuccess && result.Data != null)
            {
                onSuccess(result.Data);
            }
            else
            {
                errorMessages.Add(result.Message ?? "حدث خطأ غير معروف");
            }
        }

        // =============== الطلبات المعلقة - نسخة مصححة ===============

        /// <summary>
        /// نسخة مصححة لتفاصيل الطلبات المعلقة (بدون الخصائص غير الموجودة)
        /// </summary>
        [HttpGet("pending-details/{supervisorName}")]
        public async Task<IActionResult> GetPendingOrderDetails(string supervisorName)
        {
            try
            {
                var result = await _statisticsService.GetSupervisorStatisticsAsync();

                if (!result.IsSuccess)
                    return BadRequest(result.Message);

                var supervisor = result.Data.FirstOrDefault(s =>
                    s.SupervisorName.Equals(supervisorName, StringComparison.OrdinalIgnoreCase));

                if (supervisor == null)
                    return NotFound($"لم يتم العثور على المشرف: {supervisorName}");

                return Ok(new
                {
                    SupervisorName = supervisor.SupervisorName,
                    Summary = new
                    {
                        PendingCount = supervisor.UnderExecution,
                        CompletionRate = supervisor.CompletionRate,
                        PerformanceLevel = supervisor.PerformanceLevel,
                        WorkloadStatus = supervisor.WorkloadStatus,
                        IsHighPerformer = supervisor.IsHighPerformer
                    },
                    BasicInfo = new
                    {
                        TotalOrders = supervisor.TotalOrders,
                        Completed = supervisor.Completed,
                        NeedsAction = supervisor.NeedsAction,
                        Returned = supervisor.Returned,
                        AverageCompletionTime = supervisor.AverageCompletionTime
                    },
                    TimeStatistics = new
                    {
                        ThisMonth = supervisor.ThisMonthOperations,
                        Last3Months = supervisor.Last3MonthsOperations,
                        TrendDirection = supervisor.TrendDirectionDisplay
                    },
                    Message = "تم تحديث التحليل بحذف 'السلوك النشط' لتحسين دقة النتائج."
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب تفاصيل المشرف: {SupervisorName}", supervisorName);
                return StatusCode(500, "حدث خطأ في الخادم");
            }
        }

        /// <summary>
        /// ملخص عام للطلبات المعلقة - نسخة مصححة
        /// </summary>
        [HttpGet("pending-summary")]
        public async Task<IActionResult> GetPendingSummary()
        {
            try
            {
                var result = await _statisticsService.GetSupervisorStatisticsAsync();

                if (!result.IsSuccess)
                    return BadRequest(result.Message);

                var allSupervisors = result.Data.ToList();
                var supervisorsWithPending = allSupervisors.Where(s => s.HasPendingWork).ToList();

                var summary = new
                {
                    TotalSupervisors = allSupervisors.Count,
                    SupervisorsWithPending = supervisorsWithPending.Count,
                    TotalPendingOrders = supervisorsWithPending.Sum(s => s.UnderExecution),

                    PerformanceCategories = new
                    {
                        Excellent = allSupervisors.Count(s => s.PerformanceLevel == "ممتاز"),
                        VeryGood = allSupervisors.Count(s => s.PerformanceLevel == "جيد جداً"),
                        Good = allSupervisors.Count(s => s.PerformanceLevel == "جيد"),
                        Average = allSupervisors.Count(s => s.PerformanceLevel == "متوسط"),
                        NeedsImprovement = allSupervisors.Count(s => s.PerformanceLevel == "يحتاج تحسين")
                    },

                    TopPerformers = allSupervisors
                        .Where(s => s.IsTopPerformer)
                        .Select(s => new
                        {
                            s.SupervisorName,
                            s.CompletionRate,
                            s.PerformanceLevel,
                            s.Ranking
                        }).ToList(),

                    HighWorkload = supervisorsWithPending
                        .OrderByDescending(s => s.UnderExecution)
                        .Take(5)
                        .Select(s => new
                        {
                            s.SupervisorName,
                            PendingOrders = s.UnderExecution,
                            s.CompletionRate,
                            s.WorkloadStatus
                        }).ToList(),

                    Message = "تم تحديث التحليل بحذف 'السلوك النشط' والتركيز على البيانات الأساسية الموثوقة."
                };

                return Ok(summary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب ملخص المشرفين");
                return StatusCode(500, "حدث خطأ في الخادم");
            }
        }

        /// <summary>
        /// تحليل أداء المشرفين - نسخة مصححة بدون الخصائص غير الموجودة
        /// </summary>
        [HttpGet("performance-analysis")]
        public async Task<IActionResult> GetPerformanceAnalysis()
        {
            try
            {
                var result = await _statisticsService.GetSupervisorStatisticsAsync();

                if (!result.IsSuccess)
                    return BadRequest(result.Message);

                var allSupervisors = result.Data.ToList();

                var analysis = new
                {
                    Summary = new
                    {
                        TotalSupervisors = allSupervisors.Count,
                        ActiveSupervisors = allSupervisors.Count(s => s.IsActive),
                        AverageCompletionRate = allSupervisors.Any() ? allSupervisors.Average(s => s.CompletionRate) : 0
                    },

                    PerformanceLevels = allSupervisors
                        .GroupBy(s => s.PerformanceLevel)
                        .Select(g => new { Level = g.Key, Count = g.Count() })
                        .OrderByDescending(x => x.Count)
                        .ToList(),

                    MonthlyActivity = new
                    {
                        ActiveThisMonth = allSupervisors.Count(s => s.IsActiveThisMonth),
                        TotalOperationsThisMonth = allSupervisors.Sum(s => s.ThisMonthOperations)
                    },

                    TopPerformers = allSupervisors
                        .Where(s => s.IsHighPerformer)
                        .OrderBy(s => s.Ranking)
                        .Take(10)
                        .Select(s => new
                        {
                            s.SupervisorName,
                            s.CompletionRate,
                            s.PerformanceLevel,
                            s.ThisMonthOperations,
                            s.TrendDirectionDisplay,
                            s.Ranking
                        }).ToList(),

                    NeedsImprovement = allSupervisors
                        .Where(s => s.CompletionRate < 60)
                        .OrderBy(s => s.CompletionRate)
                        .Select(s => new
                        {
                            s.SupervisorName,
                            s.CompletionRate,
                            s.UnderExecution,
                            s.WorkloadStatus,
                            s.SupervisorCategory
                        }).ToList()
                };

                return Ok(analysis);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحليل الأداء");
                return StatusCode(500, "حدث خطأ في الخادم");
            }
        }
        // 📊   البداية النسخة الثالثة:  - التحليل التاريخي فقط

        /// <summary>
        /// 🆕 نقطة وصول جديدة لتحليل التأخير التفصيلي
        /// </summary>
        [HttpGet("supervisor-delay-analysis")]
        public async Task<IActionResult> GetSupervisorDelayAnalysis()
        {
            try
            {
                var delayResult = await _statisticsService.GetSupervisorDelayStatisticsAsync();
                if (!delayResult.IsSuccess)
                {
                    return BadRequest($"فشل جلب بيانات التأخير: {delayResult.Message}");
                }

                var delayStats = delayResult.Data;
                var summary = new
                {
                    TotalSupervisors = delayStats.Count,
                    TotalCompletedOrders = delayStats.Sum(s => s.TotalCompletedOrders),
                    OverallAverageDelay = delayStats.Any() ? Math.Round(delayStats.Average(s => s.AverageDelayDays), 1) : 0,
                    TotalDelayedOrders = delayStats.Sum(s => s.DelayedOrders),
                    TotalCriticalDelays = delayStats.Sum(s => s.CriticalDelayOrders),

                    PerformanceLevels = new
                    {
                        Excellent = delayStats.Count(s => s.DelayLevelTextImproved == "ممتاز"),
                        Good = delayStats.Count(s => s.DelayLevelTextImproved == "جيد"),
                        Average = delayStats.Count(s => s.DelayLevelTextImproved == "متوسط"),
                        NeedsImprovement = delayStats.Count(s => s.DelayLevelTextImproved == "يحتاج تحسين")
                    },

                    TopPerformers = delayStats
                        .Where(s => s.IsTopPerformer)
                        .OrderBy(s => s.DelayRanking)
                        .Take(5)
                        .Select(s => new
                        {
                            s.SupervisorName,
                            s.AverageDelayDays,
                            s.OnTimePercentage,
                            s.TotalCompletedOrders,
                            s.DelayRanking,
                            s.PerformanceLevel
                        }).ToList(),

                    NeedsAttention = delayStats
                        .Where(s => s.NeedsImprovement)
                        .OrderByDescending(s => s.AverageDelayDays)
                        .Take(5)
                        .Select(s => new
                        {
                            s.SupervisorName,
                            s.AverageDelayDays,
                            s.DelayedOrders,
                            s.CriticalDelayOrders,
                            s.DelayLevelTextImproved
                        }).ToList()
                };

                return Json(new
                {
                    Success = true,
                    Message = "تم جلب تحليل التأخير بنجاح",
                    Data = delayStats,
                    Summary = summary
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحليل التأخير للمشرفين");
                return StatusCode(500, new
                {
                    Success = false,
                    Message = "حدث خطأ في الخادم",
                    Error = ex.Message
                });
            }
        }

        /// <summary>
        /// 🆕 تفاصيل التأخير لمشرف محدد
        /// </summary>
        [HttpGet("supervisor-delay-details/{supervisorName}")]
        public async Task<IActionResult> GetSupervisorDelayDetails(string supervisorName)
        {
            try
            {
                var result = await _statisticsService.GetSupervisorDelayStatisticsAsync();

                if (!result.IsSuccess)
                    return BadRequest(result.Message);

                var supervisor = result.Data.FirstOrDefault(s =>
                    s.SupervisorName.Equals(supervisorName, StringComparison.OrdinalIgnoreCase));

                if (supervisor == null)
                    return NotFound($"لم يتم العثور على المشرف: {supervisorName}");

                var response = new
                {
                    SupervisorName = supervisor.SupervisorName,
                    Summary = new
                    {
                        supervisor.TotalCompletedOrders,
                        supervisor.AverageDelayDays,
                        supervisor.OnTimePercentage,
                        supervisor.DelayPercentage,
                        supervisor.DelayRanking,
                        supervisor.PerformanceLevel
                    },
                    DelayBreakdown = new
                    {
                        supervisor.DelayedOrders,
                        supervisor.CriticalDelayOrders,
                        supervisor.MaxDelayDays,
                        supervisor.MinDelayDays
                    },
                    DelayedOrdersList = supervisor.DelayedOrdersList
                        .OrderByDescending(o => o.DelayDays)
                        .Take(10) // أول 10 طلبات متأخرة
                        .Select(o => new
                        {
                            o.OrderId,
                            o.EmployeeName,
                            o.DelayDays,
                            o.DelayLevel,
                            o.Department,
                            o.OrderType,
                            ArrivalDate = o.ArrivalDate.ToString("yyyy-MM-dd"),
                            ApprovalDate = o.ApprovalDate.ToString("yyyy-MM-dd")
                        }).ToList(),
                    Analysis = new
                    {
                        supervisor.HasCriticalDelays,
                        supervisor.NeedsImprovement,
                        supervisor.IsTopPerformer,
                        Recommendation = GetRecommendation(supervisor)
                    }
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب تفاصيل تأخير المشرف: {SupervisorName}", supervisorName);
                return StatusCode(500, "حدث خطأ في الخادم");
            }
        }

        /// <summary>
        /// 🆕 مقارنة الأداء بين المشرفين في التأخير
        /// </summary>
        [HttpGet("delay-comparison")]
        public async Task<IActionResult> GetDelayComparison()
        {
            try
            {
                var result = await _statisticsService.GetSupervisorDelayStatisticsAsync();

                if (!result.IsSuccess)
                    return BadRequest(result.Message);

                var supervisors = result.Data.Where(s => s.TotalCompletedOrders > 0).ToList();

                var comparison = new
                {
                    TotalSupervisors = supervisors.Count,

                    Ranking = supervisors
                        .OrderBy(s => s.DelayRanking)
                        .Select((s, index) => new
                        {
                            Position = index + 1,
                            s.SupervisorName,
                            s.AverageDelayDays,
                            s.OnTimePercentage,
                            s.TotalCompletedOrders,
                            s.DelayLevelTextImproved,
                            PerformanceChange = GetPerformanceChange(s) // يمكن تطويرها لاحقاً
                        }).ToList(),

                    Statistics = new
                    {
                        BestPerformer = supervisors.OrderBy(s => s.AverageDelayDays).First(),
                        WorstPerformer = supervisors.OrderByDescending(s => s.AverageDelayDays).First(),
                        AverageDelay = Math.Round(supervisors.Average(s => s.AverageDelayDays), 1),
                        MedianDelay = GetMedianDelay(supervisors),
                        TotalOnTimePercentage = supervisors.Any() ?
                            Math.Round(supervisors.Average(s => s.OnTimePercentage), 1) : 0
                    },

                    CategoryBreakdown = supervisors
                        .GroupBy(s => s.DelayLevelTextImproved)
                        .Select(g => new
                        {
                            Level = g.Key,
                            Count = g.Count(),
                            Percentage = Math.Round((double)g.Count() / supervisors.Count * 100, 1),
                            Supervisors = g.Select(s => s.SupervisorName).ToList()
                        }).ToList()
                };

                return Ok(comparison);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في مقارنة التأخير");
                return StatusCode(500, "حدث خطأ في الخادم");
            }
        }
        /// <summary>
        /// دالة مساعدة للحصول على التوصية للمشرف
        /// </summary>
        private string GetRecommendation(SupervisorDelayStatisticsDto supervisor)
        {
            if (supervisor.AverageDelayDays <= 1)
                return "أداء ممتاز - حافظ على هذا المستوى";
            if (supervisor.AverageDelayDays <= 2)
                return "أداء جيد - يمكن تحسينه قليلاً";
            if (supervisor.AverageDelayDays <= 5)
                return "يحتاج متابعة يومية لتقليل أوقات التأخير";
            return "يحتاج خطة تحسين عاجلة ومتابعة مكثفة";
        }

        /// <summary>
        /// دالة مساعدة لحساب متوسط التأخير
        /// </summary>
        private double GetMedianDelay(List<SupervisorDelayStatisticsDto> supervisors)
        {
            var sortedDelays = supervisors.Select(s => s.AverageDelayDays).OrderBy(d => d).ToList();
            int count = sortedDelays.Count;

            if (count == 0) return 0;
            if (count % 2 == 0)
                return (sortedDelays[count / 2 - 1] + sortedDelays[count / 2]) / 2;
            else
                return sortedDelays[count / 2];
        }

        /// <summary>
        /// دالة مساعدة لتحديد تغيير الأداء (للتطوير المستقبلي)
        /// </summary>
        private string GetPerformanceChange(SupervisorDelayStatisticsDto supervisor)
        {
            // يمكن تطوير هذه الدالة لاحقاً لمقارنة الأداء مع الفترات السابقة
            return "مستقر"; // قيمة افتراضية
        }
        /// <summary>
        /// 🆕 API للطلبات المعلقة الموحدة
        /// </summary>
        [HttpGet("unified-pending")]
        public async Task<IActionResult> GetUnifiedPendingStatistics()
        {
            try
            {
                var result = await _statisticsService.GetUnifiedPendingStatisticsAsync();

                if (!result.IsSuccess)
                    return BadRequest(result.Message);

                var summary = new
                {
                    TotalEntities = result.Data.Count,
                    Supervisors = result.Data.Count(e => e.EntityType == "Supervisor"),
                    Departments = result.Data.Count(e => e.EntityType == "Department"),
                    TotalPending = result.Data.Sum(e => e.TotalPendingOrders),
                    CriticalCount = result.Data.Count(e => e.AveragePendingDays > 5),
                    Data = result.Data.OrderBy(e => e.Ranking).ToList()
                };

                return Json(summary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الإحصائيات الموحدة");
                return StatusCode(500, "حدث خطأ في الخادم");
            }
        }
        // للإحصائيات الشهرية:
        [HttpGet("monthly")]
        public async Task<IActionResult> GetMonthlyStatistics()
        {
            try
            {
                var result = await _statisticsService.GetMonthlyStatisticsAsync();
                return Json(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الإحصائيات الشهرية");
                return StatusCode(500, "حدث خطأ في الخادم");
            }
        }
    }
}