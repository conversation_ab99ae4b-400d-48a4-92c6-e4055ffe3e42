// HR Coordinator JavaScript Module
const HRCoordinator = {
    // Global state
    currentOrderId: null,
    currentRestoreOrderId: null,
    modals: {},

    // Initialize the module based on current page
    init: function () {
        this.detectPageType();
        this.initializeModals();
        this.bindEvents();
        this.showTempDataMessages();
    },

    // Detect which page we're on and initialize accordingly
    detectPageType: function () {
        const path = window.location.pathname.toLowerCase();

        if (path.includes('/hrcoordinator/restore')) {
            this.initRestorePage();
        } else {
            this.initIndexPage();
        }
    },

    // Initialize Index page functionality
    initIndexPage: function () {
        const urlParams = new URLSearchParams(window.location.search);
        const orderId = urlParams.get('id');

        // Setup order selection handler
        const orderSelect = document.getElementById('orderSelect');
        if (orderSelect) {
            orderSelect.addEventListener('change', (e) => {
                const selectedValue = e.target.value;
                if (selectedValue) {
                    this.currentOrderId = parseInt(selectedValue);
                    this.showOrderProcessingSection();
                    this.loadOrderDetailsForProcessing(selectedValue);
                    this.loadAutoRoutingInfo(selectedValue);
                    this.ClearAllCheckboxes();
                } else {
                    this.hideOrderProcessingSection();
                    this.hideQuickStatus();
                }
            });

            // إذا كان هناك order محدد، فعّله
            if (orderId) {
                orderSelect.value = orderId;
                orderSelect.dispatchEvent(new Event('change'));
            }
        }
    },

   

   

    // Initialize Restore page functionality
    initRestorePage: function () {
        // Load initial restorable orders
        this.searchRestorableOrders();
    },

    // Initialize Bootstrap modals
    initializeModals: function () {
        const modalElements = [
            'confirmSubmitModal',
            'confirmAutoPathModal',
            'confirmDirectToManagerModal',
            'confirmReturnModal',
            'confirmRejectModal',
            'confirmRestoreModal',
            'confirmActionRequiredModal',
        ];

        modalElements.forEach(modalId => {
            const element = document.getElementById(modalId);
            if (element) {
                this.modals[modalId] = new bootstrap.Modal(element);
            }
        });
    },

    // Bind event handlers
    bindEvents: function () {
        // Order selection change handler (Index page)
        const orderSelect = document.getElementById('orderSelect');
        if (orderSelect) {
            orderSelect.addEventListener('change', (e) => {
                const orderId = e.target.value;
                if (orderId) {
                    this.currentOrderId = parseInt(orderId);
                    this.showOrderProcessingSection();
                    this.loadOrderDetailsForProcessing(orderId);
                    this.loadAutoRoutingInfo(orderId);
                    this.ClearAllCheckboxes();
                } else {
                    this.hideOrderProcessingSection();
                }
            });
        }
    },
     
    // ==================== INDEX PAGE FUNCTIONS ====================

    // Show order processing section
    showOrderProcessingSection: function () {
        const processingSection = document.getElementById('orderProcessingSection');
        if (processingSection) {
            processingSection.style.display = 'block';
        }
    },

    // Hide order processing section
    hideOrderProcessingSection: function () {
        const processingSection = document.getElementById('orderProcessingSection');
        if (processingSection) {
            processingSection.style.display = 'none';
        }
    },

    // Load order details for processing
    loadOrderDetailsForProcessing: function (orderId) {
        if (!orderId) {
            return;
        }

        // Show loading
        const loading = document.getElementById('loading');
        if (loading) {
            loading.style.display = 'block';
        }

        // Update the order processing title
        const title = document.getElementById('orderProcessingTitle');
        if (title) {
            title.textContent = `معالجة الطلب #${orderId}`;
        }

        // Load order details with callback to handle supervisor checkboxes
        OrderDetailsModule.loadOrderDetails(orderId, '/HRCoordinator/GetOrderDetails');
        
        // Add a small delay to ensure order details are loaded, then handle supervisor checkboxes
        setTimeout(() => {
            this.handleSupervisorCheckboxes();
        }, 500);
    },

    // Load auto-routing info
    loadAutoRoutingInfo(orderId) {
        const autoRouteBtn = document.getElementById('autoRouteBtn');
        if (!autoRouteBtn) return;

        fetch(`/HRCoordinator/GetAutoRouting/${orderId}`, {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        })
            .then(res => res.json())
            .then(res => {
                if (!res.success) {
                    autoRouteBtn.disabled = true;
                    return OrderDetailsModule.showMessage(res.message, 'error');
                }

                autoRouteBtn.disabled = !res.data.isAvailable;
                autoRouteBtn.onclick = res.data.isAvailable ? () => this.autoRouteOrder() : null;

                document.getElementById('autoRoutingMessage').innerHTML = res.data.message || '';
            })
            .catch(err => {
                console.error('Error fetching auto-routing info:', err);
                OrderDetailsModule.showMessage('حدث خطأ أثناء البحث عن الطلبات', 'error');
            });
    },

    ClearAllCheckboxes: function () {
        const checkboxes = document.querySelectorAll('input[name="SelectedSupervisors"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
            checkbox.disabled = false;
            const label = checkbox.closest('.form-check');
            if (label) {
                label.classList.remove('supervisor-confirmed', 'supervisor-requireAction', 'supervisor-returned', 'supervisor-underConfirmation');
            }
        });
        // Hide the returned by supervisor alert if visible
        const supervisorAlert = document.getElementById('supervisorAlert');
        if (supervisorAlert) {
            supervisorAlert.style.display = 'none';
            supervisorAlert.innerHTML = '';
        }
    },

    // ==================== RESTORE PAGE FUNCTIONS ====================

    // Search for restorable orders
    searchRestorableOrders: function () {
        const searchTerm = document.getElementById('txtRestoreSearch')?.value || '';
        const filter = document.getElementById('ddlRestoreFilter')?.value || 'all';

        const formData = new FormData();
        formData.append('searchTerm', searchTerm);
        formData.append('filter', filter);

        $.ajax({
            url: '/HRCoordinator/GetRestorableOrders',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'RequestVerificationToken': this.getAntiForgeryToken()
            },
            success: (data) => {
                if (data.success) {
                    this.populateRestorableOrders(data.data);
                    OrderDetailsModule.showMessage(data.message, 'success');
                } else {
                    this.populateRestorableOrders([]);
                    OrderDetailsModule.showMessage(data.message, 'error');
                }
            },
            error: (xhr, status, error) => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء البحث عن الطلبات', 'error');
                console.error('Error:', error);
            }
        });
    },

    // Populate restorable orders dropdown
    populateRestorableOrders: function (orders) {
        const select = document.getElementById('ddlRestorableOrders');
        if (select) {
            select.innerHTML = '<option value="">-- اختر الطلب للاستعادة --</option>';

            orders.forEach(order => {
                const option = document.createElement('option');
                option.value = order.value;
                option.textContent = order.text;
                select.appendChild(option);
            });
        }
    },

    // Load restore order details
    loadRestoreOrderDetails: function (orderId) {
        if (!orderId) {
            this.hideRestoreDetails();
            return;
        }

        this.currentRestoreOrderId = orderId;

        $.ajax({
            url: '/HRCoordinator/GetRestoreOrderDetails',
            type: 'POST',
            data: { orderId: orderId },
            headers: {
                'RequestVerificationToken': this.getAntiForgeryToken()
            },
            success: (data) => {
                if (data.success) {
                    this.displayRestoreDetails(data.data);
                    this.showRestoreDetails();
                } else {
                    OrderDetailsModule.showMessage(data.message, 'error');
                }
            },
            error: (xhr, status, error) => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء تحميل تفاصيل الطلب', 'error');
                console.error('Error:', error);
            }
        });
    },

    // Display restore details
    displayRestoreDetails: function (data) {
        // Display basic restore info
        const currentStatus = document.getElementById('lblRestoreCurrentStatus');
        const transferDate = document.getElementById('lblRestoreTransferDate');
        const assignedSupervisors = document.getElementById('lblRestoreAssignedSupervisors');

        if (currentStatus) currentStatus.textContent = data.currentStatus || 'غير محدد';
        if (transferDate) transferDate.textContent = data.transferDate || 'غير محدد';
        if (assignedSupervisors) assignedSupervisors.textContent = data.assignedSupervisors || 'غير محدد';

        // Set the order ID for the form
        const restoreOrderId = document.getElementById('restoreOrderId');
        if (restoreOrderId) {
            restoreOrderId.value = this.currentRestoreOrderId;
        }
    },

    // Show restore details section
    showRestoreDetails: function () {
        const detailsPanel = document.getElementById('RestoreDetailsPanel');
        const formSection = document.getElementById('RestoreFormSection');

        if (detailsPanel) detailsPanel.style.display = 'block';
        if (formSection) formSection.style.display = 'block';
    },

    // Hide restore details section
    hideRestoreDetails: function () {
        const detailsPanel = document.getElementById('RestoreDetailsPanel');
        const formSection = document.getElementById('RestoreFormSection');

        if (detailsPanel) detailsPanel.style.display = 'none';
        if (formSection) formSection.style.display = 'none';
        this.currentRestoreOrderId = null;
    },

    // Confirm restore order
    confirmRestore: function () {
        const notes = document.getElementById('restoreNotes')?.value || '';
        if (!this.currentRestoreOrderId) {
            OrderDetailsModule.showMessage('يرجى اختيار طلب للاستعادة', 'error');
            return;
        }

        // Show confirmation modal
        const modal = this.modals.confirmRestoreModal;
        if (modal) {
            modal.show();

            // Handle confirmation
            const confirmBtn = document.getElementById('confirmRestoreModalBtn');
            if (confirmBtn) {
                const handleConfirm = () => {
                    modal.hide();
                    confirmBtn.removeEventListener('click', handleConfirm);
                    this.submitRestore();
                };
                confirmBtn.addEventListener('click', handleConfirm);
            }
        }
    },

    // Submit restore order
    submitRestore: function () {
        const formData = new FormData();
        formData.append('orderId', this.currentRestoreOrderId);
        formData.append('restoreNotes', document.getElementById('restoreNotes')?.value || '');
        formData.append('__RequestVerificationToken', this.getAntiForgeryToken());

        $.ajax({
            url: '/HRCoordinator/RestoreOrder',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: (data) => {
                if (data.success) {
                    OrderDetailsModule.showMessage(data.message, 'success');
                    this.clearRestoreForm();
                    // after 1 s refresh the list
                    setTimeout(() => {
                        this.searchRestorableOrders(); // Refresh the list
                    }, 2000);

                } else {
                    OrderDetailsModule.showMessage(data.message, 'error');
                }
            },
            error: (xhr, status, error) => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء استعادة الطلب', 'error');
                console.error('Error:', error);
            }
        });
    },

    // Clear restore form
    clearRestoreForm: function () {
        const notes = document.getElementById('restoreNotes');
        const orderId = document.getElementById('restoreOrderId');

        if (notes) notes.value = '';
        if (orderId) orderId.value = '';
        this.hideRestoreDetails();
    },

    // ==================== SHARED FUNCTIONS ====================

    // Get anti-forgery token
    getAntiForgeryToken: function () {
        const token = document.querySelector('input[name="__RequestVerificationToken"]');
        return token ? token.value : '';
    },

    // Show temp data messages
    showTempDataMessages: function () {
        // This will be handled by the inline scripts in the views
        // that call OrderDetailsModule.showMessage()
    },

    // ==================== PROCESSING FUNCTIONS ====================

    // Select all supervisors
    selectAllSupervisors: function () {
        const checkboxes = document.querySelectorAll('input[name="SelectedSupervisors"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
    },

    // Unselect all supervisors
    unselectAllSupervisors: function () {
        const checkboxes = document.querySelectorAll('input[name="SelectedSupervisors"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
    },

    // Get selected supervisors
    getSelectedSupervisors: function () {
        const checkboxes = document.querySelectorAll('input[name="SelectedSupervisors"]:checked');
        return Array.from(checkboxes).map(cb => cb.value);
    },

    // Submit order
    submitOrder: function () {
        const orderId = this.currentOrderId || document.querySelector('input[name="SelectedOrderId"]')?.value;
        const details = document.getElementById('details')?.value;
        const selectedSupervisors = this.getSelectedSupervisors();

        if (!orderId) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب', 'error');
            return;
        }

        if (!details || details.trim() === '') {
            OrderDetailsModule.showMessage('يرجى كتابة التفاصيل/الرقم', 'error');
            return;
        }

        if (selectedSupervisors.length === 0) {
            OrderDetailsModule.showMessage('يجب اختيار قسم على الأقل للاعتماد', 'error');
            return;
        }

        // Show confirmation modal
        const modal = this.modals.confirmSubmitModal;
        if (modal) {
            modal.show();

            const confirmBtn = document.getElementById('confirmSubmitModalBtn');
            if (confirmBtn) {
                const handleConfirm = () => {
                    modal.hide();
                    confirmBtn.removeEventListener('click', handleConfirm);
                    this.performSubmitOrder(orderId, details, selectedSupervisors);
                };
                confirmBtn.addEventListener('click', handleConfirm);
            }
        }
    },

    // Perform submit order
    performSubmitOrder: function (orderId, details, selectedSupervisors) {
        const formData = new FormData();
        formData.append('orderId', orderId);
        formData.append('details', details);
        selectedSupervisors.forEach(supervisor => {
            formData.append('selectedSupervisors', supervisor);
        });
        formData.append('__RequestVerificationToken', this.getAntiForgeryToken());

        $.ajax({
            url: '/HRCoordinator/SubmitOrder',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: (data) => {
                if (data.success) {
                    OrderDetailsModule.showMessage(data.message, 'success');
                    setTimeout(() => {
                        window.location.href = '/HRCoordinator';
                    }, 2000);
                } else {
                    OrderDetailsModule.showMessage(data.message, 'error');
                }
            },
            error: (xhr, status, error) => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء تحويل الطلب', 'error');
                console.error('Error:', error);
            }
        });
    },

    // Auto route order
    autoRouteOrder: function () {
        const orderId = this.currentOrderId || document.querySelector('input[name="SelectedOrderId"]')?.value;

        if (!orderId) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب', 'error');
            return;
        }

        // Show confirmation modal
        const modal = this.modals.confirmAutoPathModal;
        if (modal) {
            modal.show();

            const confirmBtn = document.getElementById('confirmAutoPathModalBtn');
            if (confirmBtn) {
                const handleConfirm = () => {
                    modal.hide();
                    confirmBtn.removeEventListener('click', handleConfirm);
                    this.performAutoRoute(orderId);
                };
                confirmBtn.addEventListener('click', handleConfirm);
            }
        }
    },

    // Perform auto route
    performAutoRoute: function (orderId) {
        const formData = new FormData();
        formData.append('orderId', orderId);
        formData.append('__RequestVerificationToken', this.getAntiForgeryToken());

        $.ajax({
            url: '/HRCoordinator/AutoRouteOrder',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: (data) => {
                if (data.success) {
                    OrderDetailsModule.showMessage(data.message, 'success');
                    setTimeout(() => {
                        window.location.href = '/HRCoordinator';
                    }, 2000);
                } else {
                    OrderDetailsModule.showMessage(data.message, 'error');
                }
            },
            error: (xhr, status, error) => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء التوجيه التلقائي', 'error');
                console.error('Error:', error);
            }
        });
    },

    // Apply predefined path
    applyPath: function (pathNumber) {
        var pathKey = 'مسار' + pathNumber;
        var supervisors = (window.pathsConfiguration && window.pathsConfiguration[pathKey]) || [];
        // Uncheck all
        $('input[name="SelectedSupervisors"]').prop('checked', false);
        // Check only those in the path
        supervisors.forEach(function (sup) {
            $('input[name="SelectedSupervisors"][value="' + sup + '"]').prop('checked', true);
        });
    },

    // Direct to manager
    directToManager: function () {
        const orderId = this.currentOrderId || document.querySelector('input[name="SelectedOrderId"]')?.value;
        const details = document.getElementById('details')?.value;

        if (!orderId) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب', 'error');
            return;
        }

        if (!details || details.trim() === '') {
            OrderDetailsModule.showMessage('يرجى كتابة التفاصيل/الرقم', 'error');
            return;
        }

        // Show confirmation modal
        const modal = this.modals.confirmDirectToManagerModal;
        if (modal) {
            modal.show();

            const confirmBtn = document.getElementById('confirmDirectToManagerModalBtn');
            if (confirmBtn) {
                const handleConfirm = () => {
                    modal.hide();
                    confirmBtn.removeEventListener('click', handleConfirm);
                    this.performDirectToManager(orderId, details);
                };
                confirmBtn.addEventListener('click', handleConfirm);
            }
        }
    },

    // Perform direct to manager
    performDirectToManager: function (orderId, details) {
        const formData = new FormData();
        formData.append('orderId', orderId);
        formData.append('details', details);
        formData.append('__RequestVerificationToken', this.getAntiForgeryToken());

        $.ajax({
            url: '/HRCoordinator/DirectToManager',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: (data) => {
                if (data.success) {
                    OrderDetailsModule.showMessage(data.message, 'success');
                    setTimeout(() => {
                        window.location.href = '/HRCoordinator';
                    }, 2000);
                } else {
                    OrderDetailsModule.showMessage(data.message, 'error');
                }
            },
            error: (xhr, status, error) => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء تحويل الطلب للمدير', 'error');
                console.error('Error:', error);
            }
        });
    },

    // Mark order needs action
    markNeedsAction: function () {
        const orderId = this.currentOrderId || document.querySelector('input[name="SelectedOrderId"]')?.value;
        const actionRequired = document.getElementById('actionRequired')?.value;

        if (!orderId) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب', 'error');
            return;
        }

        if (!actionRequired || actionRequired.trim() === '') {
            OrderDetailsModule.showMessage('يرجى كتابة الإجراءات المطلوبة', 'error');
            return;
        }

        // Show confirmation modal
        const modal = this.modals.confirmActionRequiredModal;
        if (modal) {
            modal.show();
            const confirmBtn = document.getElementById('confirmActionRequiredModalBtn');
            if (confirmBtn) {
                const handleConfirm = () => {
                    modal.hide();
                    confirmBtn.removeEventListener('click', handleConfirm);
                    this.performMarkNeedsAction(orderId, actionRequired);
                };
                confirmBtn.addEventListener('click', handleConfirm);
            }
        }
    },

    // Perform mark needs action
    performMarkNeedsAction: function (orderId, actionRequired) {
        if (!orderId || !actionRequired) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب وكتابة الإجراءات المطلوبة', 'error');
            return;
        }
        // Prepare form data
        const formData = new FormData();
        formData.append('orderId', orderId);
        formData.append('actionRequired', actionRequired);
        formData.append('__RequestVerificationToken', this.getAntiForgeryToken());

        $.ajax({
            url: '/HRCoordinator/MarkOrderNeedsAction',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: (data) => {
                if (data.success) {
                    OrderDetailsModule.showMessage(data.message, 'success');
                    setTimeout(() => {
                        window.location.href = '/HRCoordinator';
                    }, 2000);
                } else {
                    OrderDetailsModule.showMessage(data.message, 'error');
                }
            },
            error: (xhr, status, error) => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء تحديد الإجراءات المطلوبة', 'error');
                console.error('Error:', error);
            }
        });
    },

    // Return order
    returnOrder: function () {
        const orderId = this.currentOrderId || document.querySelector('input[name="SelectedOrderId"]')?.value;
        const returnReason = document.getElementById('returnReason')?.value;

        if (!orderId) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب', 'error');
            return;
        }

        if (!returnReason || returnReason.trim() === '') {
            OrderDetailsModule.showMessage('يرجى كتابة سبب الإعادة', 'error');
            return;
        }

        // Show confirmation modal
        const modal = this.modals.confirmReturnModal;
        if (modal) {
            modal.show();

            const confirmBtn = document.getElementById('confirmReturnModalBtn');
            if (confirmBtn) {
                const handleConfirm = () => {
                    modal.hide();
                    confirmBtn.removeEventListener('click', handleConfirm);
                    this.performReturnOrder(orderId, returnReason);
                };
                confirmBtn.addEventListener('click', handleConfirm);
            }
        }
    },

    // Perform return order
    performReturnOrder: function (orderId, returnReason) {
        const formData = new FormData();
        formData.append('orderId', orderId);
        formData.append('returnReason', returnReason);
        formData.append('__RequestVerificationToken', this.getAntiForgeryToken());

        $.ajax({
            url: '/HRCoordinator/ReturnOrder',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: (data) => {
                if (data.success) {
                    OrderDetailsModule.showMessage(data.message, 'success');
                    setTimeout(() => {
                        window.location.href = '/HRCoordinator';
                    }, 2000);
                } else {
                    OrderDetailsModule.showMessage(data.message, 'error');
                }
            },
            error: (xhr, status, error) => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء إعادة الطلب', 'error');
                console.error('Error:', error);
            }
        });
    },

    // Reject order
    rejectOrder: function () {
        const orderId = this.currentOrderId || document.querySelector('input[name="SelectedOrderId"]')?.value;
        const rejectReason = document.getElementById('returnReason')?.value;

        if (!orderId) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب', 'error');
            return;
        }

        if (!rejectReason || rejectReason.trim() === '') {
            OrderDetailsModule.showMessage('يرجى كتابة سبب الإلغاء', 'error');
            return;
        }

        // Show confirmation modal
        const modal = this.modals.confirmRejectModal;
        if (modal) {
            modal.show();

            const confirmBtn = document.getElementById('confirmRejectModalBtn');
            if (confirmBtn) {
                const handleConfirm = () => {
                    modal.hide();
                    confirmBtn.removeEventListener('click', handleConfirm);
                    this.performRejectOrder(orderId, rejectReason);
                };
                confirmBtn.addEventListener('click', handleConfirm);
            }
        }
    },

    // Perform reject order
    performRejectOrder: function (orderId, rejectReason) {
        const formData = new FormData();
        formData.append('orderId', orderId);
        formData.append('rejectReason', rejectReason);
        formData.append('__RequestVerificationToken', this.getAntiForgeryToken());

        $.ajax({
            url: '/HRCoordinator/RejectOrder',
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: (data) => {
                if (data.success) {
                    OrderDetailsModule.showMessage(data.message, 'success');
                    setTimeout(() => {
                        window.location.href = '/HRCoordinator';
                    }, 2000);
                } else {
                    OrderDetailsModule.showMessage(data.message, 'error');
                }
            },
            error: (xhr, status, error) => {
                OrderDetailsModule.showMessage('حدث خطأ أثناء إلغاء الطلب', 'error');
                console.error('Error:', error);
            }
        });
    },

    // Download attachments
    downloadAttachments: function () {
        const orderId = this.currentOrderId || document.querySelector('input[name="SelectedOrderId"]')?.value;

        if (!orderId) {
            OrderDetailsModule.showMessage('يرجى اختيار رقم الطلب', 'error');
            return;
        }
        this.downloadAttachmentsWithoutRefresh(orderId);
    },

    // Function to download attachments without page refresh
    downloadAttachmentsWithoutRefresh: function (orderId) {
        // Show loading state
        const $btn = $('#downloadAttachmentsBtn');
        const originalText = $btn.html();
        $btn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2" role="status"></span>جاري التحميل...');

        // Create a temporary link element for download
        const downloadUrl = `/HRCoordinator/DownloadAttachments/${orderId}`;

        // Use fetch to handle the download
        fetch(downloadUrl, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.blob();
            })
            .then(blob => {
                // Create download link
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `مرفقات_طلب_${orderId}.zip`;

                // Trigger download
                document.body.appendChild(a);
                a.click();

                // Cleanup
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                // Show success message
                OrderDetailsModule.showMessage('تم تحميل المرفقات بنجاح', 'success');
            })
            .catch(error => {
                console.error('Download error:', error);
                OrderDetailsModule.showMessage('حدث خطأ أثناء تحميل المرفقات', 'error');
            })
            .finally(() => {
                // Restore button state
                $btn.prop('disabled', false).html(originalText);
            });
    },

    // New function to handle supervisor checkbox enabling/disabling based on order status and comments
    handleSupervisorCheckboxes: function() {
        // Get order details from the global scope (set by OrderDetailsModule)
        fetch(`/HRCoordinator/GetOrderDetails`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: `orderId=${this.currentOrderId}`
        })
        .then(response => response.json())
        .then(result => {
            if (result.success && result.data) {
                const orderData = result.data;
                
                // Check if order is ReturnedBySupervisor
                if (orderData.orderStatus && orderData.orderStatus.includes('أُعيد بواسطة أحد المشرفين')) {
                    this.updateSupervisorCheckboxes(orderData);
                    this.showReturnedBySupervisorAlert();
                }
            }
        })
        .catch(error => {
            console.error('Error fetching order details for supervisor checkbox handling:', error);
        });
    },

    // Update supervisor checkboxes based on supervisor comments
    updateSupervisorCheckboxes: function(orderData) {
        const supervisorMapping = {
            'خدمات الموظفين': orderData.supervisorOfEmployeeServices,
            'إدارة تخطيط الموارد البشرية': orderData.supervisorOfHumanResourcesPlanning,
            'إدارة تقنية المعلومات': orderData.supervisorOfInformationTechnology,
            'مراقبة الدوام': orderData.supervisorOfAttendance,
            'السجلات الطبية': orderData.supervisorOfMedicalRecords,
            'إدارة الرواتب والاستحقاقات': orderData.supervisorOfPayrollAndBenefits,
            'إدارة القانونية والالتزام': orderData.supervisorOfLegalAndCompliance,
            'خدمات الموارد البشرية': orderData.supervisorOfHumanResourcesServices,
            'إدارة الإسكان': orderData.supervisorOfHousing,
            'قسم الملفات': orderData.supervisorOfFiles,
            'العيادات الخارجية': orderData.supervisorOfOutpatientClinics,
            'التأمينات الاجتماعية': orderData.supervisorOfSocialSecurity,
            'وحدة مراقبة المخزون': orderData.supervisorOfInventoryControl,
            'إدارة تنمية الإيرادات': orderData.supervisorOfRevenueDevelopment,
            'إدارة الأمن و السلامة': orderData.supervisorOfSecurity,
            'الطب الاتصالي': orderData.supervisorOfMedicalConsultation
        };

        // Check each supervisor checkbox
        Object.keys(supervisorMapping).forEach(supervisorName => {
            const supervisorComment = supervisorMapping[supervisorName];
            const checkbox = document.querySelector(`input[name="SelectedSupervisors"][value="${supervisorName}"]`);
            
            if (checkbox && supervisorComment) {
                // Check if supervisor is assigned
                const isAssigned = supervisorComment && supervisorComment.trim() !== '';
                
                if (isAssigned) {
                    // Check supervisor comment to determine if checkbox should be disabled
                    const supervisorCase = this.SupervisorCaseBasedOnComment(supervisorComment);
                    
                    // Set checkbox as checked (since supervisor is assigned)
                    checkbox.checked = true;
                    
                    // Disable/enable based on comment
                    checkbox.disabled = supervisorCase === 1 || supervisorCase === 2;

                    // Add visual styling
                    const label = checkbox.closest('.form-check');
                    if (label) {
                        if (supervisorCase === 1) {
                            label.classList.add('supervisor-confirmed');
                        }else if (supervisorCase === 2) {
                            label.classList.add('supervisor-requireAction');
                        }
                        else if (supervisorCase === 3) {
                            label.classList.add('supervisor-returned');
                        }
                        else if (supervisorCase === 4) {
                            label.classList.add('supervisor-underConfirmation');
                        }
                    }
                }
            }
        });
    },

    SupervisorCaseBasedOnComment: function(supervisorComment) {
        if (!supervisorComment) return 0;
        
        const comment = supervisorComment.toLowerCase().trim();
        

        if (comment.includes('اعتماد')) {
            return 1;
        }
        if ( comment.includes('طلب إجراء')) {
            return 2;
        }
       
        if (comment.includes('تمت الإعادة')) {
            return 3;
        }

        if (comment.includes('الطلب قيد التنفيذ')) {
            return 4;
        }

        return 0;
    },

    // Show alert for returned by supervisor orders
    showReturnedBySupervisorAlert: function() {
        const supervisorAlert = document.getElementById('supervisorAlert');
        if (supervisorAlert) {
            supervisorAlert.style.display = 'block';
            supervisorAlert.innerHTML = `
                <i class="fas fa-info-circle"></i>
                <strong>تنبيه:</strong> هذا الطلب تم إرجاعه من أحد المشرفين. 
                المشرفين الذين قاموا بالاعتماد أو طلب إجراءات لا يمكن إلغاء تحديدهم.
                المشرفين الذين قاموا بالإعادة أو الطلب قيد التنفيذ يمكن إزالتهم أو الإبقاء عليهم.
            `;
        }
    }

};




// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function () {
    HRCoordinator.init();
});
