using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Core.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Services
{
    public class AutomaticDeletionSettingsService : IAutomaticDeletionSettingsService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<AutomaticDeletionSettingsService> _logger;

        public AutomaticDeletionSettingsService(
            IUnitOfWork unitOfWork,
            ILogger<AutomaticDeletionSettingsService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<ServiceResult<List<AutomaticDeletionSettingsDto>>> GetAllSettingsAsync()
        {
            try
            {
                var settings = await _unitOfWork.AutomaticDeletionSettings.GetAllAsync();
                var settingsDto = settings.Select(MapToDto).ToList();
                return ServiceResult<List<AutomaticDeletionSettingsDto>>.Success(settingsDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب إعدادات الحذف التلقائي");
                return ServiceResult<List<AutomaticDeletionSettingsDto>>.Failure("حدث خطأ أثناء جلب الإعدادات");
            }
        }

        public async Task<ServiceResult<AutomaticDeletionSettingsDto>> GetSettingByIdAsync(int id)
        {
            try
            {
                var setting = await _unitOfWork.AutomaticDeletionSettings.GetByIdAsync(id);
                if (setting == null)
                {
                    return ServiceResult<AutomaticDeletionSettingsDto>.Failure("الإعداد غير موجود");
                }

                return ServiceResult<AutomaticDeletionSettingsDto>.Success(MapToDto(setting));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب إعداد الحذف التلقائي {Id}", id);
                return ServiceResult<AutomaticDeletionSettingsDto>.Failure("حدث خطأ أثناء جلب الإعداد");
            }
        }

        public async Task<ServiceResult<AutomaticDeletionSettingsDto>> CreateSettingAsync(AutomaticDeletionSettingsCreateDto createDto)
        {
            try
            {
                var setting = new AutomaticDeletionSettings
                {
                    SettingName = createDto.SettingName,
                    IsEnabled = createDto.IsEnabled,
                    PeriodInMonths = createDto.PeriodInMonths,
                    Description = createDto.Description,
                    CreatedBy = createDto.CreatedBy,
                    CreatedAt = DateTime.UtcNow,
                    IsActive = true
                };

                await _unitOfWork.AutomaticDeletionSettings.AddAsync(setting);

                return ServiceResult<AutomaticDeletionSettingsDto>.Success(MapToDto(setting));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء إعداد الحذف التلقائي");
                return ServiceResult<AutomaticDeletionSettingsDto>.Failure("حدث خطأ أثناء إنشاء الإعداد");
            }
        }

        public async Task<ServiceResult<AutomaticDeletionSettingsDto>> UpdateSettingAsync(AutomaticDeletionSettingsUpdateDto updateDto)
        {
            try
            {
                var setting = await _unitOfWork.AutomaticDeletionSettings.GetByIdAsync(updateDto.Id);
                if (setting == null)
                {
                    return ServiceResult<AutomaticDeletionSettingsDto>.Failure("الإعداد غير موجود");
                }

                setting.IsEnabled = updateDto.IsEnabled;
                setting.LastModifiedBy = updateDto.LastModifiedBy;
                setting.LastModifiedAt = DateTime.UtcNow;

                await _unitOfWork.AutomaticDeletionSettings.UpdateAsync(setting);

                return ServiceResult<AutomaticDeletionSettingsDto>.Success(MapToDto(setting));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث إعداد الحذف التلقائي {Id}", updateDto.Id);
                return ServiceResult<AutomaticDeletionSettingsDto>.Failure("حدث خطأ أثناء تحديث الإعداد");
            }
        }

        public async Task<ServiceResult<bool>> DeleteSettingAsync(int id)
        {
            try
            {
                var setting = await _unitOfWork.AutomaticDeletionSettings.GetByIdAsync(id);
                if (setting == null)
                {
                    return ServiceResult<bool>.Failure("الإعداد غير موجود");
                }

                await _unitOfWork.AutomaticDeletionSettings.DeleteAsync(setting);

                return ServiceResult<bool>.Success(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف إعداد الحذف التلقائي {Id}", id);
                return ServiceResult<bool>.Failure("حدث خطأ أثناء حذف الإعداد");
            }
        }

        public async Task<ServiceResult<List<AutomaticDeletionSettingsDto>>> GetActiveSettingsAsync()
        {
            try
            {
                var settings = await _unitOfWork.AutomaticDeletionSettings.GetActiveSettingsAsync();
                var settingsDto = settings.Select(MapToDto).ToList();
                return ServiceResult<List<AutomaticDeletionSettingsDto>>.Success(settingsDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في جلب الإعدادات النشطة للحذف التلقائي");
                return ServiceResult<List<AutomaticDeletionSettingsDto>>.Failure("حدث خطأ أثناء جلب الإعدادات النشطة");
            }
        }

        public async Task<ServiceResult<bool>> ExecuteAutomaticDeletionAsync()
        {
            try
            {
                var activeSettings = await _unitOfWork.AutomaticDeletionSettings.GetActiveSettingsAsync();
                var enabledSettings = activeSettings.Where(s => s.IsEnabled).ToList();

                if (!enabledSettings.Any())
                {
                    return ServiceResult<bool>.Success(true); // No settings enabled
                }

                int totalDeleted = 0;
                foreach (var setting in enabledSettings)
                {
                    var cutoffDate = DateTime.UtcNow.AddMonths(-setting.PeriodInMonths);
                    var ordersToDeleteAttachments = await _unitOfWork.Orders.GetOrdersOlderThanAsync(cutoffDate);

                    foreach (var order in ordersToDeleteAttachments)
                    {
                        // Clear attachment URLs
                        order.File1Url = null;
                        order.File2Url = null;
                        order.File3Url = null;
                        order.File4Url = null;

                        await _unitOfWork.Orders.UpdateAsync(order);
                        totalDeleted++;
                    }
                }

                if (totalDeleted > 0)
                {
                    await _unitOfWork.SaveChangesAsync();
                    _logger.LogInformation("تم حذف مرفقات {Count} طلب تلقائياً", totalDeleted);
                }

                return ServiceResult<bool>.Success(true);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تنفيذ الحذف التلقائي للمرفقات");
                return ServiceResult<bool>.Failure("حدث خطأ أثناء تنفيذ الحذف التلقائي");
            }
        }

        private static AutomaticDeletionSettingsDto MapToDto(AutomaticDeletionSettings setting)
        {
            return new AutomaticDeletionSettingsDto
            {
                Id = setting.Id,
                SettingName = setting.SettingName,
                IsEnabled = setting.IsEnabled,
                PeriodInMonths = setting.PeriodInMonths,
                Description = setting.Description,
                CreatedAt = setting.CreatedAt,
                LastModifiedAt = setting.LastModifiedAt,
                LastModifiedBy = setting.LastModifiedBy,
                CreatedBy = setting.CreatedBy,
                IsActive = setting.IsActive
            };
        }
    }
}
