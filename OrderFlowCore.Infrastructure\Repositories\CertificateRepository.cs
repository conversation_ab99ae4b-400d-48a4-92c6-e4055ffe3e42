using Microsoft.EntityFrameworkCore;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderFlowCore.Infrastructure.Data
{
    public class CertificateRepository : ICertificateRepository
    {
        private readonly ApplicationDbContext _context;

        public CertificateRepository(ApplicationDbContext context)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
        }

        public async Task<int> AddAsync(Certificate certificate)
        {
            await _context.Certificates.AddAsync(certificate);
            return certificate.Id;
        }

        public async Task<int> UpdateAsync(Certificate certificate)
        {
            _context.Certificates.Update(certificate);
            return certificate.Id;
        }

        public async Task<Certificate> GetByIdAsync(int id)
        {
            return await _context.Certificates.FindAsync(id);
        }

        public async Task<List<Certificate>> GetAllAsync()
        {
            return await _context.Certificates.AsNoTracking().ToListAsync();
        }

        public async Task<List<Certificate>> GetByStatusAsync(CertificateStatus status)
        {
            return await _context.Certificates
                .Where(c => c.CertificateStatus == status)
                .AsNoTracking()
                .OrderByDescending(c => c.CreatedAt)
                .ToListAsync();
        }
    }
}



