using System.Threading.Tasks;

namespace OrderFlowCore.Application.Interfaces.Repositories
{
    public interface IUnitOfWork
    {
        IUserRepository Users { get; }
        IOrderRepository Orders { get; }
        IOrdersTypeRepository OrdersTypes { get; }
        IEmploymentTypeRepository EmploymentTypes { get; }
        IJobTypeRepository JobTypes { get; }
        IQualificationRepository Qualifications { get; }
        INationalityRepository Nationalities { get; }
        IDepartmentRepository Departments { get; }
        IEmployeeRepository Employees { get; }
        IPredefinedPathRepository PredefinedPaths { get; }
        IPathsTableRepository PathsTables { get; }
        IAutoRoutingRepository AutoRoutings { get; }
        IDirectRoutingRepository DirectRoutings { get; }
        ISupervisorsFollowUpRepository SupervisorsFollowUps { get; }
        IAutomaticDeletionSettingsRepository AutomaticDeletionSettings { get; }
        Task BeginTransactionAsync();
        Task CommitTransactionAsync();
        Task RollbackTransactionAsync();
        Task<int> SaveChangesAsync();
    }
}