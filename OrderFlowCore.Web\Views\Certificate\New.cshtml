@model OrderFlowCore.Web.ViewModels.CertificateNewViewModel
@{
    ViewData["Title"] = "شهادة لمن يهمه الأمر";
    Layout = "_Layout";
}

<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="card shadow-sm">
                    <div class="card-body p-4">
                        <h3 class="mb-4 text-center">شهادة لمن يهمه الأمر</h3>

                        @if (TempData["CertificateSuccess"] != null)
                        {
                            <div class="alert alert-success">تم إنشاء الشهادة بنجاح</div>
                        }

                        <form asp-action="New" method="post">
                            <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label">اسم الموظف</label>
                                    <input asp-for="FullName" class="form-control" />
                                    <span asp-validation-for="FullName" class="text-danger"></span>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">الجنسية</label>
                                    <input asp-for="Nationality" class="form-control" />
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">رقم الإقامة</label>
                                    <input asp-for="ResidencyNumber" class="form-control" />
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">المسمى الوظيفي</label>
                                    <input asp-for="JobTitle" class="form-control" />
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">تاريخ بداية العقد</label>
                                    <input asp-for="ContractStartDate" type="date" class="form-control" />
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">تاريخ نهاية العقد</label>
                                    <input asp-for="ContractEndDate" type="date" class="form-control" />
                                </div>
                                <div class="col-12">
                                    <div class="form-check">
                                        <input asp-for="IsStillWorking" class="form-check-input" />
                                        <label class="form-check-label" for="IsStillWorking">لا يزال على رأس العمل</label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">جهة الشهادة</label>
                                    <input asp-for="CertificateRecipient" class="form-control" />
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">القسم</label>
                                    <input asp-for="Department" class="form-control" />
                                </div>
                            </div>

                            <div class="mt-4 text-center">
                                <button type="submit" class="btn btn-primary px-5">إرسال الشهادة</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>




