using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using OrderFlowCore.Application.Interfaces.Data;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Core.Models;
using OrderFlowCore.Infrastructure.Data;
using OrderFlowCore.Infrastructure.Repositories;

namespace OrderFlowCore.Infrastructure
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddInfrastructure(this IServiceCollection services, IConfiguration configuration)
        {
            // Register DbContext
            services.AddDbContext<ApplicationDbContext>(options =>
                options.UseSqlServer(configuration.GetConnectionString("DefaultConnection")));

            // Register repositories
            services.AddScoped<IDatabaseSeeder, DatabaseSeeder>();
            services.AddScoped<IUserRepository, UserRepository>();
            services.AddScoped<IDepartmentRepository, DepartmentRepository>();
            services.AddScoped<INationalityRepository, NationalityRepository>();
            services.AddScoped<IQualificationRepository, QualificationRepository>();
            services.AddScoped<IEmploymentTypeRepository, EmploymentTypeRepository>();
            services.AddScoped<IJobTypeRepository, JobTypeRepository>();
            services.AddScoped<IOrdersTypeRepository, OrdersTypeRepository>();
            services.AddScoped<IOrderRepository, OrderRepository>();
            services.AddScoped<IEmployeeRepository, EmployeeRepository>();
            services.AddScoped<IPredefinedPathRepository, PredefinedPathRepository>();
            services.AddScoped<IPathsTableRepository, PathsTableRepository>();
            services.AddScoped<IAutoRoutingRepository, AutoRoutingRepository>();
            services.AddScoped<IDirectRoutingRepository, DirectRoutingRepository>();
            services.AddScoped<ISupervisorsFollowUpRepository, SupervisorsFollowUpRepository>();
            services.AddScoped<IAutomaticDeletionSettingsRepository, AutomaticDeletionSettingsRepository>();
            services.AddScoped<ICertificateRepository, CertificateRepository>();
            services.AddScoped<IUnitOfWork, UnitOfWork>();

            return services;
        }
    }
} 