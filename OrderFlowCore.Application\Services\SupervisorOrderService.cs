using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Helper;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Services
{
    public class SupervisorOrderService : ISupervisorOrderService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ISupervisorService _supervisorService;
        private readonly ILogger<SupervisorOrderService> _logger;

        public SupervisorOrderService(
            IUnitOfWork unitOfWork,
            ISupervisorService supervisorService,
            ILogger<SupervisorOrderService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _supervisorService = supervisorService ?? throw new ArgumentNullException(nameof(supervisorService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        // في SupervisorOrderService.cs - تحديث GetSupervisorOrdersAsync
        public async Task<ServiceResult<List<OrderSummaryDto>>> GetSupervisorOrdersAsync(string supervisorRole)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(supervisorRole))
                {
                    return ServiceResult<List<OrderSummaryDto>>.Success(new List<OrderSummaryDto>());
                }

                var allOrders = await _unitOfWork.Orders.GetSupervisorsPendingOrdersAsync();
                var filtered = new List<OrdersTable>();

                foreach (var order in allOrders)
                {
                    var supervisorStatuses = _supervisorService.GetAssignedSupervisors(order);
                    if (supervisorStatuses.Contains(supervisorRole))
                    {
                        var status = _supervisorService.GetSupervisorStatus(order, supervisorRole);
                        if (!string.IsNullOrEmpty(status) &&
                            (status.Contains("الطلب قيد التنفيذ") || status.Contains("طلب إجراء")))
                        {
                            filtered.Add(order);
                        }
                    }
                }

                var orderSummaries = filtered
                    .OrderByDescending(o => o.CreatedAt)
                    .Select(order => {
                        var status = _supervisorService.GetSupervisorStatus(order, supervisorRole);
                        var dto = new OrderSummaryDto
                        {
                            Id = order.Id,
                            EmployeeName = order.EmployeeName,
                            OrderType = order.OrderType,
                            Department = order.Department,
                            CreatedAt = order.CreatedAt,
                            OrderStatus = order.OrderStatus
                        };

                        // إضافة معلومات إضافية للحالة
                        if (status.Contains("طلب إجراء"))
                        {
                            // استخراج اسم المشرف من السجل
                            var match = System.Text.RegularExpressions.Regex.Match(status, @"من المشرف:\s*([^|]+)");
                            if (match.Success)
                            {
                                dto.AdditionalInfo = $"طلب إجراء - {match.Groups[1].Value.Trim()}";
                            }
                            else
                            {
                                dto.AdditionalInfo = "طلب إجراء";
                            }
                        }
                        else if (status.Contains("الطلب قيد التنفيذ"))
                        {
                            dto.AdditionalInfo = "جديد";
                        }

                        return dto;
                    }).ToList();

                return ServiceResult<List<OrderSummaryDto>>.Success(orderSummaries);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting supervisor orders for role {SupervisorRole}", supervisorRole);
                return ServiceResult<List<OrderSummaryDto>>.Failure($"حدث خطأ أثناء جلب الطلبات: {ex.Message}");
            }
        }

        public async Task<ServiceResult> ConfirmOrderBySupervisorAsync(int orderId, string supervisorRole, string userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (string.IsNullOrWhiteSpace(supervisorRole))
                {
                    return ServiceResult.Failure("دور المشرف مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Get current supervisor status
                var currentStatus = _supervisorService.GetSupervisorStatus(order, supervisorRole);

                // Update supervisor status to confirmed with assignment date
                var assignmentDate = ExtractAssignmentDate(currentStatus);
                var confirmedStatus = OrderHelper.ConfirmedByWithAssignment(userName, assignmentDate);
                _supervisorService.UpdateSupervisorStatuses(order, new List<string> { supervisorRole }, confirmedStatus);
                order.RemoveSupervisorRequiringAction(supervisorRole);
                order.CompleteSupervisorAction(supervisorRole);
                order.ReasonForCancellation = null;

                if(order.OrderStatus == OrderStatus.ActionRequiredBySupervisor && string.IsNullOrEmpty(order.SupervisorsListThatRequireAction))
                {
                    order.OrderStatus = OrderStatus.C;
                }

                // Check if all required supervisors have confirmed
                var allConfirmed = CheckAllSupervisorsConfirmed(order);
                if (allConfirmed)
                {
                    order.HumanResourcesManager = OrderHelper.AssignedToHRManager();
                    order.OrderStatus = OrderStatus.D;
                }

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                var message = allConfirmed ? "تم اعتماد الطلب بنجاح من جميع المشرفين" : "تم اعتماد الطلب من المشرف بنجاح";
                return ServiceResult.Success(message);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error confirming order {OrderId} by supervisor {SupervisorRole}", orderId, supervisorRole);
                return ServiceResult.Failure($"حدث خطأ أثناء اعتماد الطلب: {ex.Message}");
            }
        }

        public async Task<ServiceResult> NeedsActionBySupervisorAsync(int orderId, string actionRequired, string supervisorRole, string userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (string.IsNullOrWhiteSpace(actionRequired))
                {
                    return ServiceResult.Failure("تفاصيل الإجراء المطلوب مطلوبة");
                }

                if (string.IsNullOrWhiteSpace(supervisorRole))
                {
                    return ServiceResult.Failure("دور المشرف مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Get current supervisor status
                var currentStatus = _supervisorService.GetSupervisorStatus(order, supervisorRole);

                // Update supervisor status to needs action with assignment date
                var assignmentDate = ExtractAssignmentDate(currentStatus);
                var actionStatus = OrderHelper.OrderNeedActionBySupervisor(userName, assignmentDate);
                _supervisorService.UpdateSupervisorStatuses(order, new List<string> { supervisorRole }, actionStatus);
                order.AddSupervisorRequiringAction(supervisorRole);

                if(order.OrderStatus == OrderStatus.C)
                {
                    order.OrderStatus = OrderStatus.ActionRequiredBySupervisor;
                }

                // concatenate with existing notes if any
                var newNote = "مشرف " + supervisorRole + ": " + actionRequired;
                if (!string.IsNullOrWhiteSpace(order.SupervisorNotes))
                {
                    order.SupervisorNotes += " | " + newNote;
                }
                else
                {
                    order.SupervisorNotes = newNote;
                }

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم تسجيل طلب الإجراء بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting action required for order {OrderId} by supervisor {SupervisorRole}", orderId, supervisorRole);
                return ServiceResult.Failure($"حدث خطأ أثناء تسجيل طلب الإجراء: {ex.Message}");
            }
        }

        public async Task<ServiceResult> ReturnOrderBySupervisorAsync(int orderId, string returnReason, string supervisorRole, string userName)
        {
            try
            {
                if (orderId <= 0)
                {
                    return ServiceResult.Failure("رقم الطلب غير صحيح");
                }

                if (string.IsNullOrWhiteSpace(returnReason))
                {
                    return ServiceResult.Failure("سبب الرفض مطلوب");
                }

                if (string.IsNullOrWhiteSpace(supervisorRole))
                {
                    return ServiceResult.Failure("دور المشرف مطلوب");
                }

                var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
                if (order == null)
                {
                    return ServiceResult.Failure("لم يتم العثور على الطلب");
                }

                // Get current supervisor status
                var currentStatus = _supervisorService.GetSupervisorStatus(order, supervisorRole);

                // Update supervisor status to Returned with assignment date
                var assignmentDate = ExtractAssignmentDate(currentStatus);
                var returnedStatus = OrderHelper.ReturnedByWithAssignment(userName, assignmentDate);
                _supervisorService.UpdateSupervisorStatuses(order, new List<string> { supervisorRole }, returnedStatus);

                // Update order status and reason
                order.OrderStatus = OrderStatus.ReturnedBySupervisor;
                var returnNote = "مشرف " + supervisorRole + ": " + returnReason;
                order.ReasonForCancellation = returnNote;

                await _unitOfWork.Orders.UpdateAsync(order);
                await _unitOfWork.SaveChangesAsync();

                return ServiceResult.Success("تم رفض الطلب بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error rejecting order {OrderId} by supervisor {SupervisorRole}", orderId, supervisorRole);
                return ServiceResult.Failure($"حدث خطأ أثناء رفض الطلب: {ex.Message}");
            }
        }

        #region Private Helper Methods

        private bool CheckAllSupervisorsConfirmed(OrdersTable order)
        {
            // Get all assigned supervisors
            var assignedSupervisors = _supervisorService.GetAssignedSupervisors(order);

            // Check if all assigned supervisors have confirmed (status contains "اعتماد")
            var confirmedSupervisors = assignedSupervisors.Count(supervisorRole =>
            {
                var status = _supervisorService.GetSupervisorStatus(order, supervisorRole);
                return !string.IsNullOrEmpty(status) && status.Contains("اعتماد");
            });

            return confirmedSupervisors >= assignedSupervisors.Count;
        }

        private string ExtractAssignmentDate(string status)
        {
            if (string.IsNullOrEmpty(status))
                return DateTime.Now.ToString("yyyy-MM-dd");

            var parts = status.Split(" | ");
            if (parts.Length >= 3)
            {
                // Format: "assignmentDate | action | outDate"
                return parts[0];
            }
            else if (parts.Length >= 2)
            {
                // Format: "date | action" (old format)
                return parts[0];
            }
            
            return DateTime.Now.ToString("yyyy-MM-dd");
        }

        #endregion
    }
}
