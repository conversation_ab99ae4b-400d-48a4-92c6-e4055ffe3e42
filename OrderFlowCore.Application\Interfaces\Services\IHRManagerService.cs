using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Interfaces.Services;

public interface IHRManagerService
{
    // Main order processing methods
    Task<ServiceResult<List<OrderSummaryDto>>> GetHRManagerOrdersAsync();
    Task<ServiceResult> ApproveOrderAsync(int orderId, string userName);
    Task<ServiceResult> RejectOrderAsync(int orderId, string rejectReason, string userName);
    Task<ServiceResult> ReturnOrderAsync(int orderId, string returnReason, string userName);
    
    // Status change functionality
    Task<ServiceResult<List<OrderSummaryDto>>> GetOrdersForStatusChangeAsync(string searchTerm = "", string filter = "today");
    Task<ServiceResult<List<DropdownItemDto>>> GetAvailableStatusesAsync();
    Task<ServiceResult> ChangeOrderStatusAsync(int orderId, string newStatus, string notes, string userName);
    Task<ServiceResult> ChangeOrderStatusAndDepartmentAsync(int orderId, string newStatus, string newDepartment, string notes, string userName);
    
}
