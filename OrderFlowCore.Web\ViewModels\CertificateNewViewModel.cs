using System;
using System.ComponentModel.DataAnnotations;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Core.Models;

namespace OrderFlowCore.Web.ViewModels
{
    public class CertificateNewViewModel
    {
        [Required]
        public string FullName { get; set; }
        public string Nationality { get; set; }
        public string ResidencyNumber { get; set; }
        public string JobTitle { get; set; }
        [DataType(DataType.Date)]
        public DateTime ContractStartDate { get; set; }
        [DataType(DataType.Date)]
        public DateTime ContractEndDate { get; set; }
        public bool IsStillWorking { get; set; }
        public string CertificateRecipient { get; set; }
        public string Department { get; set; }

        public static Certificate ToEntity(CertificateNewViewModel vm)
        {
            return new Certificate
            {
                FullName = vm.FullName,
                Nationality = vm.Nationality,
                ResidencyNumber = vm.ResidencyNumber,
                JobTitle = vm.JobTitle,
                ContractStartDate = vm.ContractStartDate,
                ContractEndDate = vm.ContractEndDate,
                IsStillWorking = vm.IsStillWorking,
                CertificateRecipient = vm.CertificateRecipient,
                Department = vm.Department,
                CreatedAt = DateTime.Now,
                CertificateStatus = CertificateStatus.PendingDirectManager
            };
        }
    }
}




