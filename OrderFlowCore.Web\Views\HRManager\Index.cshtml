@model OrderFlowCore.Web.ViewModels.HRManagerProcessOrderViewModel
@{
    ViewData["Title"] = "مدير الموارد البشرية";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>مدير الموارد البشرية</h2>
                <div>
                    <a href="@Url.Action("ChangeStatus", "HRManager")" class="btn btn-success me-2">
                        <i class="fas fa-exchange-alt"></i> تغيير حالة الطلبات
                    </a>
                </div>
            </div>

            <!-- Order Selection Section -->
            <div class="card-custom mb-4">
                <div class="card-header-custom">
                    <h5 class="mb-0"><i class="fas fa-search ml-2"></i> اختيار الطلب / Select Order</h5>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-7">
                            <label for="orderSelect" class="form-label">📋 رقم الطلب:</label>
                            @Html.DropDownListFor(m => m.SelectedOrderId, Model.OrderNumbers, "اختر الطلب من القائمة",
                                     new { @class = "form-select form-select-lg", @id = "orderSelect" })
                        </div>
                        <div class="col-md-5">
                            <!-- منطقة عرض الحالة السريعة -->
                            <div id="quickStatusArea" class="d-none">
                                <div class="alert mb-0" id="statusAlert">
                                    <strong>الحالة:</strong> <span id="statusText"></span>
                                    <div id="statusDetails" class="small mt-1"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loading -->
            <div id="loading" class="loading text-center" style="display: none;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل تفاصيل الطلب...</p>
            </div>

            <!-- Order Processing Section (Hidden by default) -->
            <div id="orderProcessingSection" style="display: none;">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 id="orderProcessingTitle">معالجة الطلب</h2>
                </div>

                @using (Html.BeginForm())
                {
                    @Html.AntiForgeryToken()
                    @Html.HiddenFor(m => m.SelectedOrderId)

                    <!-- Main Processing Section -->
                    <div class="row">
                        <!-- Right Column: Actions -->
                        <div class="col-lg-4">
                            <!-- Primary Actions -->
                            <div class="card mb-3 border-success">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0"><i class="fas fa-play-circle"></i> الإجراءات الرئيسية</h6>
                                </div>
                                <div class="card-body">
                                    <button type="button" class="btn btn-success btn-lg w-100 mb-3" onclick="HRManager.approveOrder()">
                                        <i class="fas fa-check-circle"></i> اعتماد الطلب
                                    </button>
                                    <button type="button" class="btn btn-danger btn-lg w-100 mb-3" onclick="HRManager.rejectOrder()">
                                        <i class="fas fa-times-circle"></i> إلغاء الطلب
                                    </button>
                                    <button type="button" class="btn btn-warning btn-lg w-100 mb-3" onclick="HRManager.returnOrder()">
                                        <i class="fas fa-undo"></i> إعادة الطلب
                                    </button>
                                </div>
                            </div>

                            <!-- Quick Actions -->
                            <div class="card mb-3">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0"><i class="fas fa-bolt"></i> إجراءات سريعة</h6>
                                </div>
                                <div class="card-body">
                                    <button type="button" class="btn btn-outline-secondary w-100" onclick="HRManager.downloadAttachments()">
                                        <i class="fas fa-download"></i> تحميل مرفقات الطلب
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Left Column: Form Fields -->
                        <div class="col-lg-8">
                            <!-- Form Details Card -->
                            <div class="card mb-4">
                                <div class="card-header bg-primary text-white">
                                    <h5 class="mb-0"><i class="fas fa-edit"></i> تفاصيل المعالجة</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="rejectReason" class="form-label fw-bold">سبب الإلغاء/الإعادة</label>
                                        <textarea class="form-control" id="rejectReason" name="rejectReason" 
                                            placeholder="اذكر سبب الإلغاء أو الإعادة..." rows="3"></textarea>
                                    </div>
                                </div>
                            </div>

                            <!-- Message Container -->
                            <div id="messageContainer" class="mt-3"></div>
                        </div>
                    </div>
                }

                <!-- Order Details Summary -->
                <div class="card mt-4">
                    <div class="card-header bg-light">
                        <h5 class="mb-0"><i class="fas fa-file-alt"></i> ملخص الطلب</h5>
                    </div>
                    <div class="card-body">
                        @await Html.PartialAsync("_OrderDetailsPartial")
                    </div>
                </div>
            </div>

            <!-- Welcome Message -->
            <div class="alert alert-welcome text-center">
                <h4 class="alert-heading">مرحباً بك في لوحة تحكم مدير الموارد البشرية</h4>
                <p class="mb-0">اختر طلباً من القائمة أعلاه لبدء المعالجة</p>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Approve Modal -->
<div class="modal fade" id="confirmApproveModal" tabindex="-1" aria-labelledby="confirmApproveModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmApproveModalLabel">تأكيد اعتماد الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-right">
                هل أنت متأكد من اعتماد هذا الطلب؟
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" id="confirmApproveModalBtn">نعم، اعتماد</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Reject Modal -->
<div class="modal fade" id="confirmRejectModal" tabindex="-1" aria-labelledby="confirmRejectModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmRejectModalLabel">تأكيد إلغاء الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-right">
                هل أنت متأكد من إلغاء هذا الطلب؟
                <div class="mt-3">
                    <label for="rejectReasonModal" class="form-label">سبب الإلغاء:</label>
                    <textarea class="form-control" id="rejectReasonModal" readonly rows="2"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="confirmRejectModalBtn">نعم، إلغاء</button>
            </div>
        </div>
    </div>
</div>

<!-- Confirm Return Modal -->
<div class="modal fade" id="confirmReturnModal" tabindex="-1" aria-labelledby="confirmReturnModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmReturnModalLabel">تأكيد إعادة الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-right">
                هل أنت متأكد من إعادة هذا الطلب؟
                <div class="mt-3">
                    <label for="returnReasonModal" class="form-label">سبب الإعادة:</label>
                    <textarea class="form-control" id="returnReasonModal" readonly rows="2"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" id="confirmReturnModalBtn">نعم، إعادة</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/orderDetailsModule.js"></script>
    <script src="~/js/hrmanager.js"></script>
}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>

    $(document).ready(function () {
        $('#orderSelect').on('change', function () {
            const selectedText = $(this).find('option:selected').text();
            const selectedValue = $(this).val();

            if (!selectedValue || selectedText === 'اختر الطلب من القائمة') {
                $('#quickStatusArea').addClass('d-none');
                return;
            }

            let alertClass = '';
            let statusMessage = '';
            let detailsMessage = '';
            let iconClass = '';

            if (selectedText.includes('🔵')) {
                alertClass = 'alert-success';
                iconClass = 'fas fa-check-circle text-primary';
                statusMessage = '🔵 طلب جديد';
                detailsMessage = 'طلب جديد جاهز للمعالجة من المدير';
            }
            else if (selectedText.includes('🔴')) {
                alertClass = 'alert-info';
                iconClass = 'fas fa-bolt text-danger';
                statusMessage = '🔴 محول مباشرة';
                detailsMessage = 'طلب محول مباشرة من المنسق بدون المرور بالمشرفين';
            }
            else if (selectedText.includes('🟡')) {
                alertClass = 'alert-warning border-warning pulse-border animate__animated animate__pulse animate__infinite';
                iconClass = 'fas fa-exclamation-circle fa-spin text-warning';
                statusMessage = '🟡 مُعاد من المدير';
                detailsMessage = '🚨 طلب سبق معالجته وتمت إعادته من المدير - يتطلب المراجعة العاجلة';
            }
            else {
                alertClass = 'alert-secondary';
                iconClass = 'fas fa-file-alt';
                statusMessage = '📋 طلب للمعالجة';
                detailsMessage = 'طلب في انتظار قرار المدير';
            }

            // تحديث العناصر
            $('#statusText').html(statusMessage);
            $('#statusDetails').html(`<i class="${iconClass} me-1"></i>${detailsMessage}`);

            // تحديث الكلاسات
            $('#statusAlert')
                .removeClass('alert-success alert-info alert-warning alert-danger alert-secondary border-danger border-warning pulse-border animate__animated animate__pulse animate__infinite')
                .addClass(alertClass);

            // إظهار المنطقة مع تأثير
            $('#quickStatusArea').removeClass('d-none').hide().fadeIn(300);
        });

        // تشغيل عند التحميل
        if ($('#orderSelect').val()) {
            $('#orderSelect').trigger('change');
        }
    });
</script>


@section Styles {
    <style>
        /* رسالة الترحيب */
        .alert-welcome {
            background: linear-gradient(135deg, #42a5f5, #7e57c2);
            color: #fff;
            border: none;
            border-radius: 8px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.15);
        }

            .alert-welcome .alert-heading {
                font-weight: bold;
            }




        /* منطقة الحالة */
        #statusAlert {
            border-radius: 8px;
            padding: 1rem;
            font-size: 1.05rem;
            transition: all 0.3s ease-in-out;
        }

        /* جديد - أزرق */
        .alert-success {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            color: #0d47a1;
            border-left: 6px solid #2196f3;
        }

        /* محول مباشرة - أحمر */
        .alert-info {
            background: linear-gradient(135deg, #ffebee, #ffcdd2);
            color: #b71c1c;
            border-left: 6px solid #f44336;
        }

        /* مُعاد من المدير - أصفر */
        .alert-warning {
            background: linear-gradient(135deg, #fffde7, #fff59d);
            color: #f57f17;
            border-left: 6px solid #fbc02d;
            animation: pulse 1.5s infinite;
        }

        /* افتراضي */
        .alert-secondary {
            background: #f5f5f5;
            color: #616161;
            border-left: 6px solid #9e9e9e;
        }

        /* تأثير النبض للحالات الحرجة */
        @@keyframes pulse {
            0%

        {
            box-shadow: 0 0 0 0 rgba(251, 192, 45, 0.6);
        }

        70% {
            box-shadow: 0 0 0 10px rgba(251, 192, 45, 0);
        }

        100% {
            box-shadow: 0 0 0 0 rgba(251, 192, 45, 0);
        }

        }</style>
}
