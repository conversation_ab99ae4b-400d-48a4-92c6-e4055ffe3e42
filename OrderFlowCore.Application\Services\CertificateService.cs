using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.Helper;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Core.Models;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Services
{
    public class CertificateService : ICertificateService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly ILogger<CertificateService> _logger;

        public CertificateService(IUnitOfWork unitOfWork, ILogger<CertificateService> logger)
        {
            _unitOfWork = unitOfWork;
            _logger = logger;
        }

        public async Task<ServiceResult<int>> CreateAsync(Certificate certificate)
        {
            try
            {
                certificate.CreatedAt = DateTime.Now;
                certificate.CertificateStatus = CertificateStatus.PendingDirectManager;
                certificate.ConfirmedByDirectManager = OrderHelper.AssignedToDirectManager(null);
                await _unitOfWork.Certificates.AddAsync(certificate);
                await _unitOfWork.SaveChangesAsync();
                return ServiceResult<int>.Success(certificate.Id, "تم إنشاء الشهادة بنجاح");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating certificate");
                return ServiceResult<int>.Failure("حدث خطأ أثناء إنشاء الشهادة");
            }
        }

        public async Task<ServiceResult> ConfirmByDirectManagerAsync(int id, string userName)
        {
            try
            {
                var cert = await _unitOfWork.Certificates.GetByIdAsync(id);
                if (cert == null) return ServiceResult.Failure("لم يتم العثور على الشهادة");
                var assignmentDate = OrderHelper.ExtractInDate(cert.ConfirmedByDirectManager ?? "");
                cert.ConfirmedByDirectManager = OrderHelper.ConfirmedByWithAssignment(userName, assignmentDate);
                cert.ConfirmedByCoordinator = OrderHelper.AssignedToCoordinator();
                cert.CertificateStatus = CertificateStatus.PendingCoordinator;
                await _unitOfWork.Certificates.UpdateAsync(cert);
                await _unitOfWork.SaveChangesAsync();
                return ServiceResult.Success("تم اعتماد المدير المباشر");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error DM confirm certificate {Id}", id);
                return ServiceResult.Failure("حدث خطأ أثناء اعتماد المدير المباشر");
            }
        }

        public async Task<ServiceResult> ConfirmByCoordinatorAsync(int id, string userName, string details)
        {
            try
            {
                var cert = await _unitOfWork.Certificates.GetByIdAsync(id);
                if (cert == null) return ServiceResult.Failure("لم يتم العثور على الشهادة");
                var assignmentDate = OrderHelper.ExtractInDate(cert.ConfirmedByCoordinator ?? "");
                cert.ConfirmedByCoordinator = OrderHelper.ConfirmedByWithAssignment(userName, assignmentDate);
                cert.HumanResourcesManager = OrderHelper.AssignedToHRManager();
                cert.CoordinatorDetails = details;
                cert.CertificateStatus = CertificateStatus.PendingHRManager;
                await _unitOfWork.Certificates.UpdateAsync(cert);
                await _unitOfWork.SaveChangesAsync();
                return ServiceResult.Success("تم اعتماد المنسق");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error coordinator confirm certificate {Id}", id);
                return ServiceResult.Failure("حدث خطأ أثناء اعتماد المنسق");
            }
        }

        public async Task<ServiceResult> ConfirmByHRManagerAsync(int id, string userName)
        {
            try
            {
                var cert = await _unitOfWork.Certificates.GetByIdAsync(id);
                if (cert == null) return ServiceResult.Failure("لم يتم العثور على الشهادة");
                var assignmentDate = OrderHelper.ExtractInDate(cert.HumanResourcesManager ?? "");
                cert.HumanResourcesManager = OrderHelper.ConfirmedByWithAssignment(userName, assignmentDate);
                cert.CertificateStatus = CertificateStatus.Approved;
                cert.IssueDate = DateTime.Now;
                await _unitOfWork.Certificates.UpdateAsync(cert);
                await _unitOfWork.SaveChangesAsync();
                return ServiceResult.Success("تم اعتماد مدير الموارد البشرية");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error HR manager confirm certificate {Id}", id);
                return ServiceResult.Failure("حدث خطأ أثناء اعتماد مدير الموارد البشرية");
            }
        }

        public async Task<ServiceResult> CancelAsync(int id, string userName, string reason)
        {
            try
            {
                var cert = await _unitOfWork.Certificates.GetByIdAsync(id);
                if (cert == null) return ServiceResult.Failure("لم يتم العثور على الشهادة");
                cert.ReasonForCancellation = reason;
                cert.CertificateStatus = CertificateStatus.Cancelled;
                await _unitOfWork.Certificates.UpdateAsync(cert);
                await _unitOfWork.SaveChangesAsync();
                return ServiceResult.Success("تم إلغاء الشهادة");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cancel certificate {Id}", id);
                return ServiceResult.Failure("حدث خطأ أثناء إلغاء الشهادة");
            }
        }

        public async Task<ServiceResult<Certificate>> GetByIdAsync(int id)
        {
            var cert = await _unitOfWork.Certificates.GetByIdAsync(id);
            return cert == null
                ? ServiceResult<Certificate>.Failure("لم يتم العثور على الشهادة")
                : ServiceResult<Certificate>.Success(cert);
        }

        public async Task<ServiceResult<List<Certificate>>> GetPendingAsync(CertificateStatus status)
        {
            var list = await _unitOfWork.Certificates.GetByStatusAsync(status);
            return ServiceResult<List<Certificate>>.Success(list);
        }
    }
}




