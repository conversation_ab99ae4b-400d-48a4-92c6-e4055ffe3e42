@model OrderFlowCore.Web.ViewModels.DirectManagerViewModel
@{
    ViewData["Title"] = "مدير مباشر";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-12">
            <h2 class="text-center mb-4">مدير مباشر</h2>
            
            <!-- Order Selection Section -->
            <div class="order-select-container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <label for="orderSelect" class="form-label text-white">📋 رقم الطلب:</label>
                        <select id="orderSelect" class="form-select">
                            <option value="">اختر الطلب من القائمة</option>
                            @foreach (var item in Model.OrderNumbers)
                            {
                                <option value="@item.Value">@item.Text</option>
                            }
                        </select>
                    </div>
                    <div class="col-md-6">
                        <!-- منطقة عرض الحالة السريعة -->
                        <div id="quickStatusArea" class="d-none">
                            <div class="alert mb-0" id="statusAlert">
                                <strong>الحالة:</strong> <span id="statusText"></span>
                                <div id="statusDetails" class="small mt-1"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Quick Actions Section -->
            <div id="quickActions" class="quick-actions" style="display: none;">
                <h4 class="text-center mb-4">🚀 الإجراءات السريعة</h4>
                
                <!-- Row 1: Primary Actions -->
                <div class="row mb-3 p-3 bg-light rounded border align-items-center">
                    <div class="col-md-6 mb-2 mb-md-0">
                        <button id="confirmOrderBtn" class="btn submit-button w-100">
                            <span class="action-icon">✅</span>اعتماد الطلب
                        </button>
                    </div>
                    <div class="col-md-6">
                        <button id="downloadAttachmentsBtn" class="btn download-button w-100">
                            <span class="action-icon">📎</span>تحميل المرفقات
                        </button>
                    </div>
                </div>
                
                <!-- Row 2: Reject Order -->
                <div class="row mb-3 p-3 bg-light rounded border align-items-center">
                    <div class="col-md-8 mb-2 mb-md-0">
                        <input id="rejectReason" type="text" class="form-control auto-expand" 
                                  placeholder="سبب الإلغاء" />
                    </div>
                    <div class="col-md-4 d-flex align-items-center">
                        <button id="rejectOrderBtn" class="btn btn-danger w-100">
                            <span class="action-icon">❌</span>إلغاء الطلب
                        </button>
                    </div>
                </div>
            </div>
                
            <div id="messageContainer" class="mt-3"></div>

            <!-- Loading -->
            <div id="loading" class="loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-2">جاري تحميل تفاصيل الطلب...</p>
            </div>

            <!-- Order Details Section -->
            @await Html.PartialAsync("_OrderDetailsPartial")
        </div>
    </div>
</div>

<!-- Bootstrap Modals -->
<!-- Confirm Order Modal -->
<div class="modal fade" id="confirmOrderModal" tabindex="-1" aria-labelledby="confirmOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmOrderModalLabel">تأكيد الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من تأكيد هذا الطلب؟</p>
                <p class="text-muted">سيتم اعتماد الطلب وإرساله للمرحلة التالية.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" id="confirmOrderModalBtn">تأكيد الطلب</button>
            </div>
        </div>
    </div>
</div>

<!-- Reject Order Modal -->
<div class="modal fade" id="rejectOrderModal" tabindex="-1" aria-labelledby="rejectOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectOrderModalLabel">إلغاء الطلب</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من إلغاء هذا الطلب؟</p>
                <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
                <div class="mb-3">
                    <label for="rejectReasonModal" class="form-label">سبب الإلغاء:</label>
                    <input type="text" class="form-control" id="rejectReasonModal" readonly>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-danger" id="rejectOrderModalBtn">إلغاء الطلب</button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/orderDetailsModule.js"></script>
    <script src="~/js/directManager.js"></script>
} 
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>
    $(document).ready(function() {
        $('#orderSelect').on('change', function() {
            const selectedText = $(this).find('option:selected').text();
            const selectedValue = $(this).val();

            if (!selectedValue || selectedText === 'اختر الطلب من القائمة') {
                $('#quickStatusArea').addClass('d-none');
                return;
            }

            let alertClass = '';
            let statusMessage = '';
            let detailsMessage = '';
            let iconClass = '';

            if (selectedText.includes('🟢')) {
                alertClass = 'alert-info';
                iconClass = 'fas fa-plus-circle';
                statusMessage = '🟢 طلب جديد';
                detailsMessage = 'طلب جديد يحتاج موافقة المدير المباشر';
            }
            else if (selectedText.includes('🟠')) {
                alertClass = 'alert-warning';
                iconClass = 'fas fa-undo';
                statusMessage = '🟠 طلب مُعاد من المنسق';
                detailsMessage = 'طلب تم إرجاعه من منسق الموارد البشرية للمراجعة';
            }
            else {
                alertClass = 'alert-secondary';
                iconClass = 'fas fa-file-alt';
                statusMessage = '📋 طلب للمراجعة';
                detailsMessage = 'طلب في انتظار القرار';
            }

            $('#statusText').text(statusMessage);
            $('#statusDetails').html(`<i class="${iconClass} me-1"></i>${detailsMessage}`);

            $('#statusAlert')
                .removeClass('alert-info alert-warning alert-secondary')
                .addClass(alertClass);

            $('#quickStatusArea').removeClass('d-none').hide().fadeIn(300);
        });

        if ($('#orderSelect').val()) {
            $('#orderSelect').trigger('change');
        }
    });

</script>
