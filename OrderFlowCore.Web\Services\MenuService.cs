using OrderFlowCore.Core.Entities;
using OrderFlowCore.Web.Models;
using OrderFlowCore.Web.Extentions;
using System.Security.Claims;

namespace OrderFlowCore.Web.Services
{
    public interface IMenuService
    {
        List<MenuItem> GetMenuItemsForUser(ClaimsPrincipal user);
        List<MenuItem> GetSettingsItemsForUser(ClaimsPrincipal user);
    }

    public class MenuService : IMenuService
    {
        public List<MenuItem> GetMenuItemsForUser(ClaimsPrincipal user)
        {
            var userRole = user.GetUserRole();

            var menuItems = new List<MenuItem>();

            // Dashboard - Available to all authenticated users
            menuItems.Add(new MenuItem { Controller = "Dashboard", Icon = "fas fa-tachometer-alt", Title = "لوحه التحكم" });

            // Orders Management - Available to all roles
            menuItems.Add(new MenuItem { Controller = "OrdersManagement", Icon = "fas fa-list", Title = "عـرض الطلبــات" });

            // Role-specific menu items
            switch (userRole)
            {
                case UserRole.DirectManager:
                    menuItems.Add(new MenuItem { Controller = "DirectManager", Icon = "fas fa-user-tie", Title = "مدير القــســـم - إدارة الطلبات" });
                    menuItems.Add(new MenuItem { Controller = "CertificateApprovals", Action = "DirectManager", Icon = "fas fa-award", Title = "شهادات - مدير القسم" });
                    break;

                case UserRole.AssistantManager:
                    menuItems.Add(new MenuItem { Controller = "AssistantManager", Icon = "fas fa-clipboard-check", Title = "مســاعد المدير - إدارة الطلبات" });
                    break;

                case UserRole.Coordinator:
                    menuItems.Add(new MenuItem { Controller = "HRCoordinator", Icon = "fas fa-user-tie", Title = "منسق الموارد البشرية - إدارة الطلبات" });
                    menuItems.Add(new MenuItem { Controller = "CertificateApprovals", Action = "Coordinator", Icon = "fas fa-award", Title = "شهادات - المنسق" });
                    break;

                case UserRole.Supervisor:
                    menuItems.Add(new MenuItem { Controller = "SupervisorOrders", Icon = "fas fa-user-tie", Title = "الــمـــشرفــون - إدارة الطلبات" });
                    if (user.GetUserRoleType() == "قسم الملفات")
                    {
                        menuItems.Add(new MenuItem { Controller = "PrintOrder", Icon = "fas fa-print", Title = "طباعة الطلبات" });
                    }
                    break;

                case UserRole.Manager:
                    menuItems.Add(new MenuItem { Controller = "HRManager", Icon = "fas fa-user-tie", Title = "مدير الموارد البشرية - إدارة الطلبات" });
                    menuItems.Add(new MenuItem { Controller = "PrintOrder", Icon = "fas fa-print", Title = "طباعة الطلبات" });
                    menuItems.Add(new MenuItem { Controller = "PrintCertificate", Icon = "fas fa-print", Title = "طباعة الشهادات" });
                    menuItems.Add(new MenuItem { Controller = "CertificateApprovals", Action = "HRManager", Icon = "fas fa-award", Title = "شهادات - مدير الموارد البشرية" });
                    break;
                case UserRole.Admin:
                    menuItems.Add(new MenuItem { Controller = "PrintOrder", Icon = "fas fa-print", Title = "طباعة الطلبات" });
                    menuItems.Add(new MenuItem { Controller = "PrintCertificate", Icon = "fas fa-print", Title = "طباعة الشهادات" });
                    break;
            }

            return menuItems;
        }

        public List<MenuItem> GetSettingsItemsForUser(ClaimsPrincipal user)
        {
            var userRole = user.GetUserRole();

            var settingsItems = new List<MenuItem>();

            // Only Admin, Manager, and Coordinator can see settings
            if (userRole == UserRole.Admin)
            {
                settingsItems.AddRange(new List<MenuItem>
                {
                    new MenuItem { Controller = "Department", Icon = "fas fa-building", Title = "الأقسام" },
                    new MenuItem { Controller = "Nationality", Icon = "fas fa-flag", Title = "الجنسيات" },
                    new MenuItem { Controller = "Qualification", Icon = "fas fa-certificate", Title = "المؤهلات" },
                    new MenuItem { Controller = "EmploymentType", Icon = "fas fa-briefcase", Title = "أنواع التوظيف" },
                    new MenuItem { Controller = "JobType", Icon = "fas fa-user-tie", Title = "أنواع الوظائف" },
                    new MenuItem { Controller = "OrdersType", Icon = "fas fa-file-alt", Title = "أنواع الطلبات" },
                    new MenuItem { Controller = "Employee", Icon = "fas fa-users", Title = "إدارة بيانات الموظفين" },
                    new MenuItem { Controller = "Distribution", Icon = "fas fa-sitemap", Title = "إدارة التوزيع" },
                    new MenuItem { Controller = "Accounts", Icon = "fas fa-user-cog", Title = "إدارة الحسابات" },
                    new MenuItem { Controller = "PathManagement", Icon = "fas fa-map-signs", Title = "إدارة المسارات" },
                });
            }
            else if (userRole == UserRole.Manager)
            {
                settingsItems.AddRange(new List<MenuItem>
                {
                    new MenuItem { Controller = "Department", Icon = "fas fa-building", Title = "الأقسام" },
                    new MenuItem { Controller = "Nationality", Icon = "fas fa-flag", Title = "الجنسيات" },
                    new MenuItem { Controller = "Qualification", Icon = "fas fa-certificate", Title = "المؤهلات" },
                    new MenuItem { Controller = "EmploymentType", Icon = "fas fa-briefcase", Title = "أنواع التوظيف" },
                    new MenuItem { Controller = "JobType", Icon = "fas fa-user-tie", Title = "أنواع الوظائف" },
                    new MenuItem { Controller = "OrdersType", Icon = "fas fa-file-alt", Title = "أنواع الطلبات" },
                    new MenuItem { Controller = "Employee", Icon = "fas fa-users", Title = "إدارة بيانات الموظفين" },
                    new MenuItem { Controller = "Distribution", Icon = "fas fa-sitemap", Title = "إدارة التوزيع" },
                    new MenuItem { Controller = "PathManagement", Icon = "fas fa-map-signs", Title = "إدارة المسارات" },
                });
            }
            else if (userRole == UserRole.Coordinator)
            {
                // Coordinator sees only PathManagement
                settingsItems.Add(new MenuItem { Controller = "PathManagement", Icon = "fas fa-map-signs", Title = "إدارة المسارات" });
            }

            // All other roles see no settings
            return settingsItems;
        }
    }
}