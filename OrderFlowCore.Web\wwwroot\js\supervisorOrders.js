// supervisorOrders.js
// Handles follow-up records AJAX and UI logic for SupervisorOrders

class SupervisorOrdersManager {
    constructor() {
        this.currentOrderId = null;
        this.loadingOverlay = null;
        this.modals = {};
        this.config = {
            paths: {
                submitOrder: '/SupervisorOrders/SubmitOrder',
                markNeedsAction: '/SupervisorOrders/MarkOrderNeedsAction',
                returnOrder: '/SupervisorOrders/ReturnOrder',
                downloadAttachments: '/SupervisorOrders/DownloadAttachments',
                getOrderDetails: '/SupervisorOrders/GetOrderDetails',
                getSpecialAction: '/SupervisorOrders/GetSpecialAction',
                addFollowUpRecord: '/SupervisorOrders/AddFollowUpRecordAjax',
                editFollowUpRecord: '/SupervisorOrders/EditFollowUpRecordAjax',
                deleteFollowUpRecord: '/SupervisorOrders/DeleteFollowUpRecordAjax',
                importFollowUpRecords: '/SupervisorOrders/ImportFollowUpRecords'
            },
            messages: {
                selectOrder: 'يرجى اختيار رقم الطلب',
                writeActions: 'يرجى كتابة الإجراءات المطلوبة',
                writeReturnReason: 'يرجى كتابة سبب الإعادة',
                invalidInput: 'يرجى إدخال البيانات بشكل صحيح',
                serverError: 'حدث خطأ في الاتصال بالخادم',
                processing: 'جاري المعالجة...'
            },
            validation: {
                civil: { min: 2, max: 50 },
                owner: { min: 2, max: 100 },
                procedure: { min: 2, max: 200 }
            }
        };
    }

    // Initialization
    init() {
        this.detectPageType();
        this.bindEvents();
        this.initializeModals();
    }

    detectPageType() {
        const path = window.location.pathname.toLowerCase();
        const pageHandlers = {
            '/supervisororders/followuprecords': () => this.initFollowUpRecordsPage(),
            default: () => this.initIndexPage()
        };

        const handler = Object.entries(pageHandlers).find(([key]) =>
            key !== 'default' && path.includes(key)
        )?.[1] || pageHandlers.default;

        handler();
    }

    // Page-specific initialization
    // في initIndexPage method
    initIndexPage() {
        const orderSelect = document.getElementById('orderSelect');
        if (orderSelect) {
            orderSelect.addEventListener('change', (e) => {
                const orderId = parseInt(e.target.value);

                if (orderId) {
                    this.currentOrderId = orderId;

                    // عرض الحالة السريعة
                    const selectedText = orderSelect.options[orderSelect.selectedIndex].text;
                    this.displayOrderStatus(selectedText);

                    // باقي العمليات
                    this.showProcessingSection();
                    this.updateHiddenOrderId(orderId);
                    this.loadOrderDetails(orderId);
                    this.checkForSpecialAction(orderId);
                } else {
                    // إخفاء كل شيء
                    this.hideOrderStatus();
                    this.hideProcessingSection();
                }
            });

            // تفعيل إذا كان هناك طلب محدد
            if (orderSelect.value) {
                orderSelect.dispatchEvent(new Event('change'));
            }
        }

        this.setupProcessOrderPageHandlers();
    }

    // إضافة method جديدة لعرض الحالة
    displayOrderStatus(optionText) {
        const statusArea = document.getElementById('quickStatusArea');
        const statusAlert = document.getElementById('statusAlert');
        const statusText = document.getElementById('statusText');
        const statusDetails = document.getElementById('statusDetails');

        if (!statusArea || !statusAlert || !statusText) return;

        // تنظيف الكلاسات السابقة
        statusAlert.className = 'alert mb-0';
        statusDetails.innerHTML = '';

        // تحديد الحالة من النص
        if (optionText.includes('🆕') || optionText.includes('جديد')) {
            statusAlert.classList.add('alert-info');
            statusText.textContent = 'طلب جديد - قيد التنفيذ';
            statusDetails.innerHTML = '<i class="fas fa-info-circle"></i> الطلب لم يتم معالجته بعد في قسمك';
        }
        else if (optionText.includes('⚠️') || optionText.includes('طلب إجراء')) {
            statusAlert.classList.add('alert-warning');
            statusText.textContent = 'يتطلب إجراءات';

            // محاولة استخراج اسم المشرف
            const parts = optionText.split('-');
            if (parts.length > 1) {
                const actionPart = parts[1].trim();
                if (actionPart) {
                    statusDetails.innerHTML = `<i class="fas fa-exclamation-triangle"></i> ${actionPart}`;
                }
            }
        }
        else {
            statusAlert.classList.add('alert-secondary');
            statusText.textContent = 'طلب للمعالجة';
        }

        // إظهار المنطقة
        statusArea.style.display = 'block';
    }

    // إضافة method لإخفاء الحالة
    hideOrderStatus() {
        const statusArea = document.getElementById('quickStatusArea');
        if (statusArea) {
            statusArea.style.display = 'none';
        }
    }

    initProcessOrderPage() {
        // This method is no longer needed as ProcessOrder.cshtml is removed
        // Functionality moved to initIndexPage
    }

    initFollowUpRecordsPage() {
        new FollowUpRecordsManager(this).init();
    }

    // Helper methods

    updateOrderDisplay(orderId) {
        const orderNumberEl = document.getElementById('orderNumber');
        if (orderNumberEl) {
            orderNumberEl.textContent = orderId;
        }
    }

    showProcessingSection() {
        const processingSection = document.getElementById('processingSection');
        const orderDetailsSection = document.getElementById('orderDetailsSection');
        if (processingSection) processingSection.style.display = 'block';
        if (orderDetailsSection) orderDetailsSection.style.display = 'block';
    }

    hideProcessingSection() {
        const processingSection = document.getElementById('processingSection');
        const orderDetailsSection = document.getElementById('orderDetailsSection');
        if (processingSection) processingSection.style.display = 'none';
        if (orderDetailsSection) orderDetailsSection.style.display = 'none';
        this.currentOrderId = null;
    }

    updateHiddenOrderId(orderId) {
        const hiddenOrderId = document.getElementById('hiddenOrderId');
        if (hiddenOrderId) {
            hiddenOrderId.value = orderId;
        }
    }

    getAntiForgeryToken() {
        return document.querySelector('input[name="__RequestVerificationToken"]')?.value || '';
    }

    getCurrentOrderId() {
        return this.currentOrderId || document.querySelector('input[name="SelectedOrderId"]')?.value;
    }

    // Event binding
    bindEvents() {
        // Most event binding is now handled in initIndexPage()
        // This method is kept for any global event handlers if needed
    }



    // Modal management
    initializeModals() {
        const modalIds = [
            'confirmSubmitModal',
            'confirmReturnModal',
            'confirmRejectModal',
            'confirmActionRequiredModal'
        ];

        modalIds.forEach(modalId => {
            const element = document.getElementById(modalId);
            if (element) {
                this.modals[modalId] = new bootstrap.Modal(element);
            }
        });
    }

    showConfirmationModal(modalName, onConfirm) {
        const modal = this.modals[modalName];
        if (!modal) return;

        modal.show();
        const confirmBtnId = modalName.replace('Modal', 'ModalBtn');
        const confirmBtn = document.getElementById(confirmBtnId);

        if (confirmBtn) {
            const handleConfirm = () => {
                modal.hide();
                confirmBtn.removeEventListener('click', handleConfirm);
                onConfirm();
            };
            confirmBtn.addEventListener('click', handleConfirm);
        }
    }

    // Order actions
    async submitOrder() {
        const orderId = this.getCurrentOrderId();
        if (!orderId) {
            this.showMessage(this.config.messages.selectOrder, 'error');
            return;
        }

        this.showConfirmationModal('confirmSubmitModal', () =>
            this.performOrderAction(this.config.paths.submitOrder, { orderId })
        );
    }

    async markNeedsAction() {
        const orderId = this.getCurrentOrderId();
        const actionRequired = document.getElementById('actionRequired')?.value?.trim();

        if (!this.validateOrderAction(orderId, actionRequired, 'actions')) {
            return;
        }

        this.showConfirmationModal('confirmActionRequiredModal', () =>
            this.performOrderAction(this.config.paths.markNeedsAction, { orderId, actionRequired })
        );
    }

    async returnOrder() {
        const orderId = this.getCurrentOrderId();
        const returnReason = document.getElementById('returnReason')?.value?.trim();

        if (!this.validateOrderAction(orderId, returnReason, 'return')) {
            return;
        }

        this.showConfirmationModal('confirmReturnModal', () =>
            this.performOrderAction(this.config.paths.returnOrder, { orderId, returnReason })
        );
    }

    validateOrderAction(orderId, inputValue, actionType) {
        if (!orderId) {
            this.showMessage(this.config.messages.selectOrder, 'error');
            return false;
        }

        if (!inputValue) {
            const message = actionType === 'actions'
                ? this.config.messages.writeActions
                : this.config.messages.writeReturnReason;
            this.showMessage(message, 'error');
            return false;
        }

        return true;
    }

    async performOrderAction(url, data) {
        const formData = new FormData();
        Object.entries(data).forEach(([key, value]) => {
            formData.append(key, value);
        });
        formData.append('__RequestVerificationToken', this.getAntiForgeryToken());

        try {
            const response = await $.ajax({
                url,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false
            });

            if (response.success) {
                this.showMessage(response.message, 'success');
                setTimeout(() => window.location.href = '/SupervisorOrders', 2000);
            } else {
                this.showMessage(response.message, 'error');
            }
        } catch (error) {
            this.showMessage(this.config.messages.serverError, 'error');
            console.error('Error:', error);
        }
    }

    downloadAttachments() {
        const orderId = this.getCurrentOrderId();
        if (!orderId) {
            this.showMessage(this.config.messages.selectOrder, 'error');
            return;
        }
        this.downloadAttachmentsWithoutRefresh(orderId)
    }

    // Function to download attachments without page refresh
    downloadAttachmentsWithoutRefresh(orderId) {
    // Show loading state
    const $btn = $('#downloadAttachmentsBtn');
    const originalText = $btn.html();
    $btn.prop('disabled', true).html('<span class="spinner-border spinner-border-sm me-2" role="status"></span>جاري التحميل...');

    // Create a temporary link element for download
    const downloadUrl = `${this.config.paths.downloadAttachments}/${orderId}`;

    // Use fetch to handle the download
    fetch(downloadUrl, {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        return response.blob();
    })
    .then(blob => {
        // Create download link
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `مرفقات_طلب_${orderId}.zip`;

        // Trigger download
        document.body.appendChild(a);
        a.click();

        // Cleanup
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        // Show success message
        OrderDetailsModule.showMessage('تم تحميل المرفقات بنجاح', 'success');
    })
    .catch(error => {
        console.error('Download error:', error);
        OrderDetailsModule.showMessage('حدث خطأ أثناء تحميل المرفقات', 'error');
    })
    .finally(() => {
        // Restore button state
        $btn.prop('disabled', false).html(originalText);
    });
}

    navigateToProcess() {
        if (this.currentOrderId) {
            // Navigate to Index with order ID parameter
            window.location.href = `/SupervisorOrders?id=${this.currentOrderId}`;
        }
    }

    loadOrderDetails(orderId) {
        if (!orderId) {
            OrderDetailsModule.hideOrderDetails();
            return;
        }

        this.currentOrderId = orderId;
        OrderDetailsModule.loadOrderDetails(orderId, this.config.paths.getOrderDetails);
    }

    
    async checkForSpecialAction(orderId) {
        const specialActionContainer = document.getElementById('specialActionMessageContainer');
        if (!specialActionContainer) return;

        // clear old message
        specialActionContainer.innerHTML = '';

        const data = { orderId };
        const url = this.config.paths.getSpecialAction;

        const formData = new FormData();
        Object.entries(data).forEach(([key, value]) => {
            formData.append(key, value);
        });
        formData.append('__RequestVerificationToken', this.getAntiForgeryToken());

        try {
            const response = await $.ajax({
                url,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false
            });

            if (response.success) {
                const details = response.data;
                if (!details)  return;
                
                specialActionContainer.innerHTML = `
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <div class="d-flex align-items-center">
                            <div class="me-3">
                                <i class="fas fa-info-circle text-info" style="font-size: 1.5rem;"></i>
                            </div>
                            <div>
                                <h6 class="alert-heading mb-1"> تنبيه: هذا الطلب يحتاج <b>متابعة خاصة</b></h6>
                                <p class="mb-1"><strong>التفاصيل:</strong> ${details}</p>
                                <p class="mb-0"><small>نرجو مراجعة الإجراءات المطلوبة قبل المتابعة</small></p>
                            </div>
                        </div>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
            } else {
                //this.showMessage(response.message, 'error');
            }
        } catch (error) {
            this.showMessage(this.config.messages.serverError, 'error');
            console.error('Error:', error);
        }
    }


    // Process order page setup
    setupProcessOrderPageHandlers() {
        this.setupActionButtonsState();
        this.setupLoadingOverlayOnSubmit();
        this.setupModalEnterKey();

        window.addEventListener('pageshow', () => {
            this.hideLoadingOverlay();
            this.resetActionInputs();
        });
    }

    setupActionButtonsState() {
        const elements = {
            orderSelect: document.getElementById('orderSelect'),
            confirmOrderBtn: document.getElementById('confirmOrderBtn'),
            needsActionBtn: document.getElementById('needsActionBtn'),
            rejectOrderBtn: document.getElementById('rejectOrderBtn'),
            actionRequiredInput: document.getElementById('actionRequiredInput'),
            rejectReasonInput: document.getElementById('rejectReasonInput')
        };

        const setButtonsState = (enabled) => {
            ['confirmOrderBtn', 'needsActionBtn', 'rejectOrderBtn',
                'actionRequiredInput', 'rejectReasonInput'].forEach(key => {
                    if (elements[key]) elements[key].disabled = !enabled;
                });
        };

        if (elements.orderSelect) {
            const updateState = () => {
                const hasSelection = !!elements.orderSelect.value;
                setButtonsState(hasSelection);
                this.highlightSelectedOption(elements.orderSelect);
            };

            elements.orderSelect.addEventListener('change', updateState);
            updateState();
        }
    }

    highlightSelectedOption(selectElement) {
        Array.from(selectElement.options).forEach(option => {
            option.style.backgroundColor = '';
        });

        if (selectElement.selectedIndex > 0) {
            selectElement.options[selectElement.selectedIndex].style.backgroundColor = '#d1e7dd';
        }
    }

    setupLoadingOverlayOnSubmit() {
        const formIds = ['confirmOrderForm', 'needsActionForm', 'rejectOrderForm'];
        formIds.forEach(id => {
            const form = document.getElementById(id);
            if (form) {
                form.addEventListener('submit', () => this.showLoadingOverlay());
            }
        });
    }

    setupModalEnterKey() {
        const modalConfigs = [
            ['confirmOrderModal', 'confirmOrderModalBtn'],
            ['needsActionModal', 'needsActionModalBtn'],
            ['rejectOrderModal', 'rejectOrderModalBtn']
        ];

        modalConfigs.forEach(([modalId, confirmBtnId]) => {
            this.addModalEnterKeyHandler(modalId, confirmBtnId);
        });
    }

    addModalEnterKeyHandler(modalId, confirmBtnId) {
        const modal = document.getElementById(modalId);
        const confirmBtn = document.getElementById(confirmBtnId);

        if (!modal || !confirmBtn) return;

        modal.addEventListener('shown.bs.modal', () => modal.focus());
        modal.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                confirmBtn.click();
            }
        });
    }

    // Loading overlay
    showLoadingOverlay() {
        if (!this.loadingOverlay) {
            this.loadingOverlay = this.createLoadingOverlay();
            document.body.appendChild(this.loadingOverlay);
        }
    }

    createLoadingOverlay() {
        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.innerHTML = `
            <div class="d-flex justify-content-center align-items-center h-100">
                <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
                    <span class="visually-hidden">${this.config.messages.processing}</span>
                </div>
            </div>
        `;

        // Add CSS for overlay
        const style = document.createElement('style');
        style.textContent = `
            .loading-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background: rgba(0, 0, 0, 0.2);
                z-index: 2000;
            }
        `;
        document.head.appendChild(style);

        return overlay;
    }

    hideLoadingOverlay() {
        if (this.loadingOverlay) {
            this.loadingOverlay.remove();
            this.loadingOverlay = null;
        }
    }

    resetActionInputs() {
        const inputs = ['actionRequiredInput', 'rejectReasonInput'];
        inputs.forEach(id => {
            const input = document.getElementById(id);
            if (input) input.value = '';
        });
    }

    // Utility methods
    showMessage(message, type) {
        OrderDetailsModule.showMessage(message, type);
    }

    showToast(message, type) {
        SharedUtils.showToast(message, type);
    }
}

// Separate class for Follow-up Records management
class FollowUpRecordsManager {
    constructor(parent) {
        this.parent = parent;
        this.token = null;
        this.table = null;
        this.rows = null;
        this.config = {
            pageSize: 10,
            currentPage: 1,
            deleteRecord: { civil: null, row: null }
        };
    }

    init() {
        this.token = $("input[name='__RequestVerificationToken']").first().val();
        this.table = $('#followUpTable');
        this.setupEventHandlers();
        this.initializeTable();
    }

    setupEventHandlers() {
        // Form submissions
        $('#addFollowUpForm').on('submit', (e) => this.handleAddRecord(e));
        $('#editFollowUpForm').on('submit', (e) => this.handleEditRecord(e));

        // Button clicks
        $(document).on('click', '.edit-btn', (e) => this.openEditModal(e));
        $(document).on('click', '.delete-btn', (e) => this.openDeleteModal(e));
        $('#confirmDeleteBtn').on('click', () => this.confirmDelete());

        // Search and export/import
        $('#followUpSearch').on('input', () => this.handleSearch());
        $('#exportCsvBtn').on('click', () => this.exportToCsv());
        $('#importCsvInput').on('change', (e) => this.importFromCsv(e));
    }

    initializeTable() {
        this.filterRows();
    }

    // Validation
    validateRecord(civil, owner, procedure) {
        const { validation } = this.parent.config;

        if (!civil || civil.length < validation.civil.min || civil.length > validation.civil.max) {
            this.parent.showToast(`يرجى إدخال السجل المدني بشكل صحيح (${validation.civil.min}-${validation.civil.max} حرف).`, 'danger');
            return false;
        }

        if (!owner || owner.length < validation.owner.min || owner.length > validation.owner.max) {
            this.parent.showToast(`يرجى إدخال اسم صاحب الطلب بشكل صحيح (${validation.owner.min}-${validation.owner.max} حرف).`, 'danger');
            return false;
        }

        if (!procedure || procedure.length < validation.procedure.min || procedure.length > validation.procedure.max) {
            this.parent.showToast(`يرجى إدخال الإجراء المطلوب بشكل صحيح (${validation.procedure.min}-${validation.procedure.max} حرف).`, 'danger');
            return false;
        }

        return true;
    }

    // CRUD operations
    async handleAddRecord(e) {
        e.preventDefault();

        const data = {
            CivilRecord: $('#addCivilRegistry').val().trim(),
            OwnerName: $('#addOwnerName').val().trim(),
            SpecialProcedure: $('#addSpecialProcedure').val().trim()
        };

        if (!this.validateRecord(data.CivilRecord, data.OwnerName, data.SpecialProcedure)) {
            return;
        }

        try {
            const response = await this.makeAjaxRequest(
                window.addFollowUpRecordUrl || this.parent.config.paths.addFollowUpRecord,
                data
            );

            if (response.success) {
                this.addRowToTable(data);
                this.parent.showToast(response.message, 'success');
                $('#addFollowUpForm')[0].reset();
            } else {
                this.parent.showToast(response.message, 'danger');
            }
        } catch (error) {
            this.parent.showToast(this.parent.config.messages.serverError, 'danger');
        }
    }

    addRowToTable(data) {
        const row = $(
            `<tr data-civil="${data.CivilRecord}" data-owner="${data.OwnerName}" data-proc="${data.SpecialProcedure}">
                <td class="fw-bold">${data.CivilRecord}</td>
                <td>${data.OwnerName}</td>
                <td>${data.SpecialProcedure}</td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-primary edit-btn me-1">
                        <i class="fas fa-edit"></i> تعديل
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-danger delete-btn ms-1">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </td>
            </tr>`
        ).hide();
        this.table.find('tbody').append(row);
        row.fadeIn(300);
    }

    openEditModal(e) {
        const row = $(e.currentTarget).closest('tr');
        $('#editCivilRegistry').val(row.attr('data-civil'));
        $('#editOwnerName').val(row.data('owner'));
        $('#editSpecialProcedure').val(row.data('proc'));

        const modal = new bootstrap.Modal(document.getElementById('editModal'));
        modal.show();
    }

    async handleEditRecord(e) {
        e.preventDefault();

        const data = {
            CivilRecord: $('#editCivilRegistry').val().trim(),
            OwnerName: $('#editOwnerName').val().trim(),
            SpecialProcedure: $('#editSpecialProcedure').val().trim()
        };

        // Validate only owner name and procedure (civil record is readonly)
        if (!data.OwnerName || data.OwnerName.length < 2 || data.OwnerName.length > 50) {
            this.parent.showToast('يرجى إدخال اسم صاحب الطلب بشكل صحيح (2-50 حرف).', 'danger');
            return;
        }

        if (!data.SpecialProcedure || data.SpecialProcedure.length < 2 || data.SpecialProcedure.length > 100) {
            this.parent.showToast('يرجى إدخال الإجراء المطلوب بشكل صحيح (2-100 حرف).', 'danger');
            return;
        }

        try {
            const response = await this.makeAjaxRequest(
                window.editFollowUpRecordUrl || this.parent.config.paths.editFollowUpRecord,
                data
            );

            if (response.success) {
                this.updateRowInTable(data);
                this.parent.showToast(response.message, 'success');
                bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
            } else {
                this.parent.showToast(response.message, 'danger');
            }
        } catch (error) {
            this.parent.showToast(this.parent.config.messages.serverError, 'danger');
        }
    }

    updateRowInTable(data) {
        const row = $(`#followUpTable tr[data-civil='${data.CivilRecord}']`);
        row.find('td:eq(1)').text(data.OwnerName);
        row.find('td:eq(2)').text(data.SpecialProcedure);
        row.data('owner', data.OwnerName);
        row.data('proc', data.SpecialProcedure);
    }

    openDeleteModal(e) {
        const row = $(e.currentTarget).closest('tr');
        this.config.deleteRecord.civil = row.attr('data-civil');
        this.config.deleteRecord.row = row;

        const info = `
            <strong>السجل المدني:</strong> ${row.attr('data-civil')}<br>
            <strong>اسم صاحب الطلب:</strong> ${row.data('owner')}<br>
            <strong>الإجراء المطلوب:</strong> ${row.data('proc')}
        `;
        $('#deleteModalRecordInfo').html(info);

        const modal = new bootstrap.Modal(document.getElementById('deleteModal'));
        modal.show();
    }

    async confirmDelete() {
        const { civil, row } = this.config.deleteRecord;
        if (!civil || !row) return;

        try {
            const response = await this.makeAjaxRequest(
                window.deleteFollowUpRecordUrl || this.parent.config.paths.deleteFollowUpRecord,
                { CivilRecord: civil }
            );

            if (response.success) {
                row.remove();
                this.parent.showToast(response.message, 'success');
            } else {
                this.parent.showToast(response.message, 'danger');
            }
        } catch (error) {
            this.parent.showToast(this.parent.config.messages.serverError, 'danger');
        } finally {
            bootstrap.Modal.getInstance(document.getElementById('deleteModal')).hide();
            this.config.deleteRecord = { civil: null, row: null };
        }
    }

    // Table operations
    handleSearch() {
        this.config.currentPage = 1;
        this.filterRows();
    }

    filterRows() {
        const searchTerm = $('#followUpSearch').val().toLowerCase();

        this.rows = this.table.find('tbody tr').filter(function () {
            const text = $(this).text().toLowerCase();
            return text.includes(searchTerm);
        });

        this.paginateRows();
    }

    paginateRows() {
        const { pageSize, currentPage } = this.config;
        const totalRows = this.rows.length;
        const totalPages = Math.ceil(totalRows / pageSize) || 1;

        if (currentPage > totalPages) {
            this.config.currentPage = totalPages;
        }

        // Hide all rows then show current page
        this.table.find('tbody tr').hide();
        this.rows.slice((this.config.currentPage - 1) * pageSize, this.config.currentPage * pageSize).fadeIn(200);

        this.renderPagination(totalPages);
    }

    renderPagination(totalPages) {
        const paginationEl = $('#followUpPagination').empty();

        if (totalPages <= 1) return;

        for (let i = 1; i <= totalPages; i++) {
            const btn = $('<button type="button" class="btn btn-sm mx-1"></button>').text(i);
            btn.addClass(i === this.config.currentPage ? 'btn-primary' : 'btn-outline-primary');

            btn.click(() => {
                this.config.currentPage = i;
                this.paginateRows();
            });

            paginationEl.append(btn);
        }
    }

    // Export/Import
    exportToCsv() {
        let csv = 'CivilRecord,OwnerName,SpecialProcedure\n';

        this.table.find('tbody tr:visible').each(function () {
            const row = $(this);
            const data = [
                row.attr('data-civil'),
                row.data('owner'),
                row.data('proc')
            ].map(val => `"${val || ''}"`).join(',');

            csv += data + '\n';
        });

        const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');

        link.href = url;
        link.download = 'FollowUpRecords.csv';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        this.parent.showToast('تم تصدير السجلات بنجاح', 'success');
    }

    async importFromCsv(e) {
        const file = e.target.files[0];
        if (!file) return;

        const formData = new FormData();
        formData.append('csvFile', file);
        formData.append('__RequestVerificationToken', this.token);

        try {
            const response = await $.ajax({
                url: this.parent.config.paths.importFollowUpRecords,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false
            });

            if (response.success !== undefined) {
                this.parent.showToast(response.message, response.success ? 'success' : 'danger');
                if (response.success) {
                    setTimeout(() => window.location.reload(), 1500);
                }
            } else {
                window.location.reload();
            }
        } catch (error) {
            this.parent.showToast(this.parent.config.messages.serverError, 'danger');
        }
    }

    // Utility method for AJAX requests
    async makeAjaxRequest(url, data) {
        return $.ajax({
            url,
            type: 'POST',
            contentType: 'application/json',
            headers: { 'RequestVerificationToken': this.token },
            data: JSON.stringify(data)
        });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.SupervisorOrders = new SupervisorOrdersManager();
    window.SupervisorOrders.init();
});