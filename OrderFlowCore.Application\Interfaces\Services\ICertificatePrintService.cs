using OrderFlowCore.Application.Common;
using System.Collections.Generic;
using System.Threading.Tasks;
using OrderFlowCore.Application.DTOs;

namespace OrderFlowCore.Application.Interfaces.Services
{
    public interface ICertificatePrintService
    {
        Task<ServiceResult<List<PrintableCertificateDto>>> GetPrintableCertificatesAsync(string searchTerm = "", string filter = "today");
    }
}




