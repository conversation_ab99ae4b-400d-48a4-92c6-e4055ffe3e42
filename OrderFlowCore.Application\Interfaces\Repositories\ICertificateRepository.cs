using OrderFlowCore.Core.Entities;
using OrderFlowCore.Core.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Interfaces.Repositories
{
    public interface ICertificateRepository
    {
        Task<int> AddAsync(Certificate certificate);
        Task<int> UpdateAsync(Certificate certificate);
        Task<Certificate> GetByIdAsync(int id);
        Task<List<Certificate>> GetAllAsync();
        Task<List<Certificate>> GetByStatusAsync(CertificateStatus status);
    }
}



