@model List<OrderFlowCore.Core.Models.Certificate>
@{
    ViewData["Title"] = "شهادات بانتظار مدير القسم";
}
 
<section class="py-4">
    <div class="container">
        <h3 class="mb-4">قائمة الشهادات - مدير القسم</h3>
        @if (TempData["ErrorMessage"] != null)
        {
            <div class="alert alert-danger">@TempData["ErrorMessage"]</div>
        }
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>الاسم</th>
                    <th>القسم</th>
                    <th>الوظيفة</th>
                    <th>الجهة</th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
            @foreach (var c in Model)
            {
                <tr>
                    <td>@c.FullName</td>
                    <td>@c.Department</td>
                    <td>@c.JobTitle</td>
                    <td>@c.CertificateRecipient</td>
                    <td>
                        <form asp-action="ApproveByDirectManager" method="post" class="d-inline">
                            <input type="hidden" name="id" value="@c.Id" />
                            <button type="submit" class="btn btn-sm btn-primary">اعتماد</button>
                        </form>
                    </td>
                </tr>
            }
            </tbody>
        </table>
    </div>
    </section>




