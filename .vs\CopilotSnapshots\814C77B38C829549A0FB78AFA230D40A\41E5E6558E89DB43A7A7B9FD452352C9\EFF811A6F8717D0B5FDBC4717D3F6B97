﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.ViewModels;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using OrderFlowCore.Web.Attributes;
using OrderFlowCore.Core.Entities;

namespace OrderFlowCore.Web.Controllers
{
    [AuthorizeRole(UserRole.DirectManager, UserRole.AssistantManager, UserRole.Coordinator, UserRole.Supervisor, UserRole.Manager, UserRole.Admin)]
    public class PrintCertificateController : Controller
    {
        private readonly ICertificatePrintService _certificatePrintService;
        private readonly ICertificateService _certificateService;
        private readonly ICertificatePdfService _certificatePdfService;

        public PrintCertificateController(ICertificatePrintService certificatePrintService, ICertificateService certificateService, ICertificatePdfService certificatePdfService)
        {
            _certificatePrintService = certificatePrintService;
            _certificateService = certificateService;
            _certificatePdfService = certificatePdfService;
        }

        public async Task<IActionResult> Index(string searchTerm = "", string filter = "today")
        {
            var certs = await _certificatePrintService.GetPrintableCertificatesAsync(searchTerm, filter);
            var model = new CertificatePrintViewModel
            {
                SearchTerm = searchTerm,
                Filter = filter,
                Certificates = certs.Data ?? new List<SelectListItem>(),
            };

            TempData["SuccessMessage"] = certs.Message;
            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> GetCertificateDetails(int certificateId)
        {
            var result = await _certificateService.GetByIdAsync(certificateId);
            return Json(new { success = result.IsSuccess, message = result.Message, data = result.Data });
        }

        [HttpPost]
        public async Task<IActionResult> Print(int certificateId)
        {
            var certResult = await _certificateService.GetByIdAsync(certificateId);
            if (!certResult.IsSuccess || certResult.Data == null)
                return NotFound(certResult.Message);

            var pdf = await _certificatePdfService.GenerateCertificatePdfAsync(certResult.Data);
            if (!pdf.IsSuccess || pdf.Data == null)
                return NotFound(pdf.Message);

            var fileName = $"certificate_{certResult.Data.Id}_{certResult.Data.FullName}_{(certResult.Data.IssueDate ?? System.DateTime.Now):ddMMyyyy}.pdf";
            foreach (char c in System.IO.Path.GetInvalidFileNameChars())
            {
                fileName = fileName.Replace(c, '_');
            }
            return File(pdf.Data, "application/pdf", fileName);
        }
    }
}


