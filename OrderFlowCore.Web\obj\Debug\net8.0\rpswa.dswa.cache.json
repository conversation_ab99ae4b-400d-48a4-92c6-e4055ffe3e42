{"GlobalPropertiesHash": "Bfyky2FCFFxmA0I8C+MiI7GsdzAuWb6eQqbi3zQLxLQ=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["uU0z9WZa8vOUsyFgX0Jo0bdY6XYmqyHTfH4VhYntGok=", "GIM604s6ss8MhvUhpQsLUx7YFqwpuKQ7MnsTX3KPcsg=", "7begaR+n4h5AMg74tCO6xW/izD0+WLQDEjfoA3GY66s=", "ooMPq5WPh2bga6LO0mT1XeDedHEeYNuFajtKXiAL8u4=", "fzlqAk93uy75E/tt7VzsvN86t78PHatSYA9Hbz29uzM=", "zER0bCv6gwd976ocpE/yuidBMGLqaA/udiUs9haWmhU=", "/rtsmqr4fI4n1iLR14Z4mGvY4t8YIXqVw+wnH/v8YcM=", "yAl/8SjSKzJFKHycPZh6+E0uxPvX1yTLcxex1LLve+Y=", "geZeCtEDTI8qxjI/tvXwDw+yA9PqnusZlNiiBQWbbvk=", "CryoTpYEUMPwwG8IfiRAysnvnlYk+ck4v3+RnK556yk=", "dlU6lyPb172qpOmlqC3jf1MhZr8WXx5ejdUySMZxixg=", "B3jHPPDg+1J3VvOsRxgomia4BsAdgVi/mUTrkvuk65E=", "zw4I5Yz2XUFcZtLsdWF4wqfJiZ9LmfKTmMaqg6RVAsE=", "u42WZxwDlX9fW+GYrxmSnwPWBE1iCFt8ef8H++cY88E=", "8gJcLqDZBUIrEkK4+hmScmEXHa8oUCHCTEXhw9uG/eo=", "3YCZU9SxFk7OdKUR6TM6GFtWc/8lRJILM8UNfOHNEKc=", "9p+5zGOCEPWttrgY960mEP5qVbXO+5aABl5orFtqp2g=", "hhLIJwAD4CyP99RtAF2LKGTpYTStXm6n2HSpHODuAO8=", "0ulF1HtyGDY/ppEf/QQCwUXXxwdi2vfWxIAVYvQjo4o=", "x5u631qv+QDHvwUB4s5+/Wp7JPHneOXZwDNO83UkWrw=", "3sOYteXrTixtmudUjXtLy41cAZ68wEorbGdUcSr/dPw=", "ikDWce0O7cOixuJhhA6JqcZJL1mv8Csb4G8SLe21HpQ=", "YGaE99pdMca3e9dNPQ4O9gqIIUvP4zVoNDuKm/Yi8gA=", "2Celv6/KDJ/2Ep4VCeXl4IfTUhxIkW3rFmuG9vWclTY=", "mo9RtreUnjfFjbefAQJYGVZfqB0N0Ucr8rrBfQKQ9AQ=", "GoNM3EUu/xA9SGvWfQ2swfMR+/CoyQKSoLezhRh8uro=", "axIUfcTxqK/sgzLI8vwCS3TBby5H9j5QIRMhUj9JZoM=", "BuL9bltxUNpolJIwn7qsNc9wBb+t87mcHpEFM9SHQ7M=", "gQ3PDN+nE7fjS3vS4lSfBvyoMv8rovG5Q3QKdofNyvc=", "sHMVFZlRjekXjIB3aipIGmfN4lo3laXKejRwhxYw0+I=", "hKNVBUpIqd3k8Mt+Jz7S0i3+tcLr9jhzDXVLR3IHlbg=", "Zc54gc9/bTQs8B1IBPfx3pwMXi4jEZvpmyGWtRp4mXs=", "ymJYrZlkDm9Adhj3ZZiR3Ue6MXAMgBynrOEJFDDK2Ak=", "Vzzc6fJMH0rshaXHWejAO7XYw6US5epdreqgbxia9mQ=", "YvLa7riBSEDhlwArN385o/voFjN97EyHMGUGgbUipMY=", "1nIZVXvNeI2vO7jZLNvnI5HuUoUPbLxMpfJJL/COFiI=", "Zlu0TfxI23rHLhCxb4A/R1P0wRa27vZB4+PlVfCKm1w=", "1mSid6An/APrp1oaNLegIbQY9PPRdwCfcQa5jK8yuGM=", "7NQQkri4O5Bx5ENAGrMihS2ksSfflHv1fJFq8oEBnT0=", "eesPeY+n2u/LVzMKmKb4m60wNHeAfSaMyakuUa2WoiI=", "5CysbPoherj3UwrqIcwPtPJW6jjz9e398QDDw9OClqs=", "MQOKtdhYp0jV7pV1a8k2wj6JiRYMmvENErJ+9l/EPV4=", "XHKmjVoWzqFcWQ13Z1YZjs4ZQ7sd/pBKNBIppJfZsRM=", "U3DvicMAbSjeLMQGiNlO6yplOu3ErwB7cEegOmnq+go=", "fgWsT24pLdKb++5bNfu5lUALlMbPAK56QeHg/s4h+gg=", "MQgDDY1mfklzwaPLEPzSWOL/MGAr7we8T0j9Kmhfwsg=", "FU9l5xEgU0CBtOoV4b/FWAEBjZXXbm5WoWmUahgfqLE=", "IJyOF5yBvSd4i9PbF+cM+RzXayAvm4jb86/bWBDsj9s=", "GI7x7fG+bUtjmRWzpup3TFKegXTHOKnGuAglvyZqDTg=", "XLFvPEK18vDjLj/tjQFHOUd/E8QehrXPskMXtHbZo0o=", "9aV5LpkBI/1C3DqnAFRjU7/063UyAhdBaBWyCDD4HI4=", "t0fF79To006Pad8qYsum4HwU1j7Xp/hMHDeSSZ8//NE=", "G4w0Y2QqlQlXcqYK7+KYqy+HGSgjd3P3mwl4amQelE8=", "0HD/coElfLdYa385GGuwu4BDSTBOToZvFycyC7//tNE=", "s3mdFxG3HRrb3iR3TlrXeo/8bovlMD4+EFFJO4opoUc=", "tXcHaGj9CMNu8a6TwGyU/pH8qQDtKD3HAjB3q36vTUg=", "vMeiLeJS4pn+uiDtRETsai9t64PzV2kdduBJwBB3cps=", "9DRYC6YJHJ8ViCGpOOtza/KWJVOrBQ55UxMxpDJy8ow=", "/BYPFlAnoYhTX6LbcOahNvEfQn3dYj7lPL0SSNddOsg=", "Z/LKDCtqGcoDiNRg9Vl0k8rOVgqR4OYyCu6jLzqnZI4=", "uBXm3qmgR7eWBokh8B7+nRu642A76OjlFHs/iXYjF3A=", "slGutAzaIi7U0Ho1QmRNfYWSe1SXL73+lTcKs1TDuEw=", "p4Ae/04fXxzvNSOBJkcBcIxCCiDBjPA8Im0YX8vE+f8=", "iQRgDE26Odvf9J4MwjltBxRx2snrWfj1Cw8D8o2Djzg=", "GVi37odJHe+P+iugwo1jTo9keaCEcNXrbKm/R0Hh+Aw=", "KKpPPsx1eLMGsb8i3dfn8/X7TlCvgkVLW68lwRRrbGk=", "T4rthG53XfQS7iMxJFHMOdrrUNdbHKCU2EJJqLm/njM=", "rCBBp6Rql5hpjNFPfrvDI69I3P+dIFA40OnYxPmHF+g=", "ZYGlML9/w0FQ67bUmcs98jQzG8Yfm9VcjF3yumqGHto=", "PV4Mwmngjx3nZlJDWHchNIrl8n3DHy6R0Gh2Z40w1jY=", "LGWLBES8IcepDqvkcr1lO2pXCWvp5YyR8QeLgmR3LEU=", "Lfvt+zKvp9axqKQVxzCtFg6VQYxGmkiiN4UpfSFlijs=", "z/5q4JBn2bb8OHFrtcYdetAri0Q+e18wf11TyRH/1rA=", "yVyOuWCXHPUUI5dUiscj5+6cyA0W1wJBHmQ7rYd4CLM=", "DJbEh2H1RzMOPblEsYRxlc+nl8oCIMjWJez8IIT4RcM=", "Kzv0HtmNXXuRsTTAop3L5+6YjrjWqdOHUj3p6ecEHis=", "mfmBCFNiL6j656EbI6PL4g4V4s7mSb0gUrA7rVJBGGY=", "qi9uUJdUBTVHab9hxFtDtLxSnfGSfloej616AS3Qq7s=", "HOICQ5WLXQk54Spl1wMweeeGpDz7qhENPJZMSxxAlNs=", "vgbXYenJS0liwvPxrV9Oj/h840UQGw7w4GiccxoROMo=", "70LHXauEdW5kvt4uahdOB10G6zri0esX7B68qKe0MVA=", "f/JHicm/pBemWVlrcdYtEpAAHyxlR48C2e0GkdssP1k=", "4+k+wsiJwI1ADkoydA3TXOf/3QFizHA56pgdbPkLN5E=", "go6NINOr9YXDtds4/qGLZwy+suSZMzH1Dl7mODAMefQ=", "ZAZATh4M+ADRO3we+GqCUBUlaOljncmUxEvbcQ2xPuk=", "9HioSrnfvIfl1k3Lfy27bHhYqiOSrs8oX3+RgJT/gXI=", "vpmlyjn/+HRoN2CF5jBC6OwaDursImbthVPAP1OBYvw=", "kaVgIVH9HSMDOWW34wKY+2h5017LgYIl4kctnhaxjuA=", "8A6DcS0AuvADiFhgYM3pkUGtEv7r1UMUvAiGh/QT3m4=", "cRFjftPmugVVIvSFIOCTwRlBtMfT/vZinqn72XSKIV8=", "sTao4DBztnC9TII3jxUMCgRucweCW8U414qmgBXs6yo=", "elY03uf1cm9fMLTMLV5aKCTwhosASOAg+kdqHuUCHE0=", "e/J5h7j0C2nx9ieSPG1oE+T4ShYy0RVTeFcGnHgePlw=", "tzE5QkgW/XsAXhDybc9Jcm6qRrC83NaiSqsFKObxa3Y=", "9BtcmCQZsxrXIPi5NqM+aIUl2J1apzYOStIV4HRfrIA=", "Q0Ws8nFMzdZx7lJ0Hj0UQgT+EvLHh6gEynPEvfTEiYk=", "XiuhkSgmh5y1ZTj5yx8XK6YL6hXDalk+LtxGBy94dr4=", "/zmRhiwF3Ae1t93OqgoqUFcB/8qctawLiQ7Tlq4HErQ=", "iW5wjMPYr/Uc7jacP32ZJkNGb4umBbDAjoQKeeth4tA=", "o88zYoxrP9o34wbvDK7uc/PSwDF6ABKQe2FSd3Z6Lz8=", "OgOym10REGfkVzmL1Ktt7ilH+OlQB7ngFWkGYHMa++A=", "b66ByzPxu6k6HemkFBra1hdOBvTDvLKtFPFTdA1KV4Q=", "MNPHQ1Ewxw4T/RIFke6rz3Oo4+pdvSoZAue2LtAMZ5U=", "/YXkQaS4Y2RWGwy8fv7hZhEapZFKGTFpepYPcTIXAmY=", "sXiIlRIJUMCFqvWvNoySzEHlye1aDhWtiXdgUu7uRLU=", "sYsRg+mW1za0qgBxdoFs2M8vnO+QWQb/DtlFc8fGb50=", "dXurl6ANunUK1+eQ+CBsZES/0TYVznvKsfth7RIfgCE=", "oYgnanrDGwKHD/RGVFdW8FSTvU4hdZsKhsTSXoEJx/Y=", "s4+KtcuEHPGLN5kmqxLe7ZBN1Ip2KsdzXQBToqD4BMo=", "JXa69fjDz4XDE7GrP351rSbmRMI0l/JmgHA0PENqxUQ=", "IgGEfK4/Ih3Q4IRkEAMQF7J1H+BcWEW8IMNk4O6wlcs=", "m/FPOpvlXiyGcDJZ4KZirtj0OGAqhYvGSBxIBRjUnjI=", "9QaKH4ocRhLPrt8TZzY/I6c1fMqyrUVRL8Gzss7sX8k=", "xkC3KGBO+c9t6X6sQoygSMkp9Ef4JLcGYfRdnMgclBw=", "gSSE+yhO593RNW9NfS+X2bTZTJc1UQeXDbNbHw+rO64=", "NAcxGkRKtoqjVnb5Xy60K2cPeR6dVW3cYvMydDaHg+8=", "FOFxj9qxn7GD1N+Bp6lMBKUwA846hVCxIBhUSCv2R3k=", "MQSWQqJWgeoDYt/RCQ/zv8nbAEGf549ui4tlbkLRCbI=", "Z6ViJljiKSTA0wDUb6PxQORB9MiawzxBbeeoB9p70Po=", "hVT6tHklTfBnN27M3GfGaV0xi2g5wTWaln34NS0JjEQ=", "q6yotDPQavcVF/VXVFISKm1hnZmW9FSXgPXlDZNmOmg=", "nWNDJlp9A/fofcRWC/RtawWqqcU9myRWlCeS/hdVBy0=", "S7KRS9rwnZEuHDThgx5AAaDwxrfDO3l5V3wkvOMXAU4=", "xNGNEGWGvYiz5Ypb6BhMjL9AFCSFnZXmxoIHb+/YJoE=", "rnUyXKXsUvIlHortoLvqVIVlxYpNnYjLeDod2FWFnBA=", "tM3sloM8ttD6V7YfEbIOjbssiEbFyOw6mXwCPyWXf8k=", "bjscLQxyEADGrZYYZULy1PUIo8E0/MXV1r50kv7pm9c=", "By0bmw/h0GKUv3ehU5gGvnjRoXMjPcHFXMdQi5/vUrE=", "L12UhAyMIaNGoNGHkHcYj2Ci7kzSjP1N1ISUmipzElM=", "6R0qhrHbhXT/DHFXMZhOE1tXFsFRW2M5eeG4LcAwf58=", "dGH3tyNc8GmwWInXJJMyxyjv1A2BkStTym8b+OuIz/o=", "IaQ1Eo5V4Qrj8QKmYrQeKCu+rAzvdH0mYwspYwrje/Q=", "ePXl1cmNnyjj9/6dmQwcklvcmz4ehezEGll2pQ9pkLw=", "Xh8oMEvii6g4MATEqJGJzr1Ry9B3aufNbNoIPRy52vM=", "EZwMmGsdERg6gfltHRH7H/THQ2V9yifKi8gDHME/EGY=", "V5F4pHHTMAY1m19MIUFVyVERWun00UfuQyMAH9J1VdM=", "QQKHNVrGVRBJzoBvl5BX1Yyd7TMPgH2/854MpCDJN3Q=", "pozoYH/HFdNIR48O4vVJ5DaMbHpEldIPRGDzYsZ/9DQ=", "jwEvgOy5s+3uzVIDmRmLgZcoOTy9HA971D4ROP3xLks=", "RysxZ3XN3Dz7zJE4DSfbzE5Vj8dIEsit1H3ScQ9itag=", "PNnywcFJCEM+t6za0AMqeWMbXVJW30Ed98qqLrBVgCE=", "YP0EDuKxwiR49OJIMYTc3JxEje50OAJm1Mh2Ungk3oE=", "Hrcy+w8wGhyW+/KQfppQHBNVRmzmdrSB4S3p7p1UnrM=", "sx9DP3QLwzxHY6/+ZFc6cWTimicaVdjoxMXkr/NvCRk=", "oZH9CkxQ3b76P3wjvT3puQTGjQcEbKCjFqeQOvCbQpk=", "oobPI7ZI6TNx3x4VAHuQOGcVkxVHkstpnxM93rECwa8=", "82hO0oo9lVlDnb719Uphrn/YGa6sXzbb2tsNkCQU6SQ=", "I5khY+XBPX9LFfXHnzGzd3T3nLy0TnYnsZ/WHdNi30o=", "lCmpnS1/8IWuD7E4DK95vxlIr5a6hzNe+IaMFy+/bOw=", "/JvP8iYPWPBcH9flO19XRdm1BIbF26B5FERZJbN8Aro=", "0erXzSNjT8FGiRfxLZDHopv2Nf4kv7RJwKmHejG8dKY=", "1sQsvrQ0WPg1+RcsVtXM4uuVQFSMNYy7QW9eRQXwQwY=", "UAc91UsMqzheDU2j0ncyJp/tCyL2y27cxMSmkSe8mHc=", "6PhZS+FAhxfKt/lQelR5rs5jYAWzecDDI66hZVi8P5E=", "pjsmaQXigzi1/Dth20DuxGFP1XA9odzA3ZRAMvzPtkE=", "XTh/hL3tBanQqbQ65VUHkN0O/cpv4XmDS5IUmGxuz+g=", "z8LI71x5naXjLc+EpBSYKgvGz1ZEplwvUvT0ugCkZco=", "AQXE1e2qE4ziTAp9XTMGmONLTPYqsOhuSPPUMMc6Cp8=", "kex55pQsKt7otkFjaxUu+U6XlfjtAqgnUVEAuYwsCfM=", "GnGIZ+QOZHy+vrnDVqNDOTU1hrjNkPCsska8QqfprEM=", "ZxHo3Gg1lU1b+3q95VscVIywCtGbidEsjFsvfXC6BRM=", "BfBvlP9dsCWdfsOMeOm4MBbRY/2LR1cxqpKmJKTFDm4=", "pPWcpGEpGfIuOT9WP4xUpkA/KQ9Vi+cTdxEjXvQMYMs=", "2vmeddPuA68BPJatAb/9aTKWbgBbh9IbBUGlVHUVTno=", "E3f4FgfCIQMTFaxr0mQBf8F8W6xmZb9W1d3pUVxE/Yk=", "ZqAVOnJP1rSXztdIhh1jljjXXcwVt+j3E/9fG7r7ozQ=", "ogYJPJER4QzCs0Wm/gOGBuiOwgtIJ7OdKvrSW4ZpHTw=", "MimlUmPcbkl3psY0QWmXVm3QRLSIHr1WhsnrnFMnRTs=", "e0bcmgBpTz1id3benyQZm7pZzcVxCei7yMlNIKmePQk=", "yFwhew0Lxj31zQW24cyPwZxMM2D8mY/G4mTVOwyEEYY=", "2NHHA25nwvfrpehDn8jlEIg4ug2pnKCWIGTUSFLSHwc=", "68wiOrPljlvJRCuxaK+BQtMaNGBMI0YUhXLFNP5Z/YM=", "56DXQlJJ5iPC9Hn1YzoWx5CJ8UcStER7S0Sm/X/LvZY=", "wb3tzC0eiNH17fJJvv0K8TVXmPpqkFB2xMGuNt9EXa0=", "PqQ8lYp6QY9LHqvtalMpOJyKk3NHXxiBsiobob4MlDI="], "CachedAssets": {"o88zYoxrP9o34wbvDK7uc/PSwDF6ABKQe2FSd3Z6Lz8=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_1_bf69c15d-ff5e-41b2-bfb8-74eb832baa59_order_1_file1_20250627233043.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_1_bf69c15d-ff5e-41b2-bfb8-74eb832baa59_order_1_file1_20250627233043#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wdbk1wabyj", "Integrity": "pPcNFOHBw4Ef3gPClk6tgsGHZT+tnzHeuazerOEO3wM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_1_bf69c15d-ff5e-41b2-bfb8-74eb832baa59_order_1_file1_20250627233043.pdf", "FileLength": 174317, "LastWriteTime": "2025-06-27T20:30:43+00:00"}, "iW5wjMPYr/Uc7jacP32ZJkNGb4umBbDAjoQKeeth4tA=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_19_147258369_file1.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_19_147258369_file1#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7zwr2vwz9y", "Integrity": "s732BgZvJ2UYPDitj+K03Ujw5uS6xRJ39exeZgT/VZ8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_19_147258369_file1.pdf", "FileLength": 349349, "LastWriteTime": "2025-08-15T16:07:45+00:00"}, "/zmRhiwF3Ae1t93OqgoqUFcB/8qctawLiQ7Tlq4HErQ=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_18_147258369_file1.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_18_147258369_file1#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "phmm8uv3he", "Integrity": "XQvTBLxLf9pnDORFNII1LUpGmL4+ofMFtl5z6VwGHds=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_18_147258369_file1.pdf", "FileLength": 335779, "LastWriteTime": "2025-08-10T20:35:19+00:00"}, "XiuhkSgmh5y1ZTj5yx8XK6YL6hXDalk+LtxGBy94dr4=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_16_147258369_file2.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_16_147258369_file2#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5h93g0hjve", "Integrity": "R81dFN/nRm/l3oc+HBZ4XWNMmBTvOStB5QNLVcb5CzY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_16_147258369_file2.pdf", "FileLength": 1623500, "LastWriteTime": "2025-08-03T21:35:52+00:00"}, "fzlqAk93uy75E/tt7VzsvN86t78PHatSYA9Hbz29uzM=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\Breada2.jpg", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/Breada2#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f7qy3nga8b", "Integrity": "K9g9PlESiv0l4+2HEY5V+j/loHJxZYH6dH9BrcPOZp4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\Breada2.jpg", "FileLength": 142247, "LastWriteTime": "2025-05-14T15:45:14+00:00"}, "zER0bCv6gwd976ocpE/yuidBMGLqaA/udiUs9haWmhU=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\Breada3.jpg", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/Breada3#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3cvxl0parf", "Integrity": "HZ6PZANISBLVgsQdUMLQDL38HDroVuGIh2slbMNeG+k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\Breada3.jpg", "FileLength": 114703, "LastWriteTime": "2025-05-14T15:55:54+00:00"}, "/rtsmqr4fI4n1iLR14Z4mGvY4t8YIXqVw+wnH/v8YcM=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\Breada4.png", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/Breada4#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "avx9uya0f1", "Integrity": "SqYkqYyQHiqHWhROGoQY3riTbwLou2n3CWrxzS90dcE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\Breada4.png", "FileLength": 319753, "LastWriteTime": "2025-05-14T15:58:29+00:00"}, "yAl/8SjSKzJFKHycPZh6+E0uxPvX1yTLcxex1LLve+Y=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\CardImage.jpg", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/CardImage#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "br1zpi7ud7", "Integrity": "pyR35oUyROVrLrOWWIOr/pS02JeYXmHud2TxL8b4w5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\CardImage.jpg", "FileLength": 13633, "LastWriteTime": "2025-05-13T20:35:24+00:00"}, "geZeCtEDTI8qxjI/tvXwDw+yA9PqnusZlNiiBQWbbvk=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\draw2.webp", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/draw2#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lb4spfgfnj", "Integrity": "4Rl+mf1UpaYn967Lz2UuVFzqBJdYMRAJAUtOw41/SGA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\draw2.webp", "FileLength": 22372, "LastWriteTime": "2025-04-27T21:45:33+00:00"}, "Q0Ws8nFMzdZx7lJ0Hj0UQgT+EvLHh6gEynPEvfTEiYk=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_147258369_file2_20250801235838.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_147258369_file2_20250801235838#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "clsmlksgek", "Integrity": "wADZjdTvmOukGZw19az5AmpyYwbLMnLIKUy5g54a2Qk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_147258369_file2_20250801235838.pdf", "FileLength": 26192, "LastWriteTime": "2025-08-01T20:58:38+00:00"}, "dlU6lyPb172qpOmlqC3jf1MhZr8WXx5ejdUySMZxixg=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\star.png", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/star#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vbvgifvvkn", "Integrity": "ZV+qrTvBrNy2IVwDzfLhZalZ9U5VOCpF1ql7uEQAZzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\star.png", "FileLength": 152554, "LastWriteTime": "2025-05-14T17:40:25+00:00"}, "B3jHPPDg+1J3VvOsRxgomia4BsAdgVi/mUTrkvuk65E=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\.eslintrc.json", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/.eslintrc#[.{fingerprint}]?.json", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "uu9sszpxz5", "Integrity": "5vi79dz2bVp7aGHRPP89A633hSv0mO36l6DWib2vysQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\.eslintrc.json", "FileLength": 1228, "LastWriteTime": "2025-05-03T13:16:51+00:00"}, "9BtcmCQZsxrXIPi5NqM+aIUl2J1apzYOStIV4HRfrIA=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_147258369_file2_20250801235456.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_147258369_file2_20250801235456#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "clsmlksgek", "Integrity": "wADZjdTvmOukGZw19az5AmpyYwbLMnLIKUy5g54a2Qk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_147258369_file2_20250801235456.pdf", "FileLength": 26192, "LastWriteTime": "2025-08-01T20:54:56+00:00"}, "tzE5QkgW/XsAXhDybc9Jcm6qRrC83NaiSqsFKObxa3Y=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_147258369_file2_20250801235325.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_147258369_file2_20250801235325#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "clsmlksgek", "Integrity": "wADZjdTvmOukGZw19az5AmpyYwbLMnLIKUy5g54a2Qk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_147258369_file2_20250801235325.pdf", "FileLength": 26192, "LastWriteTime": "2025-08-01T20:53:25+00:00"}, "e/J5h7j0C2nx9ieSPG1oE+T4ShYy0RVTeFcGnHgePlw=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_147258369_file2_20250801232309.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_147258369_file2_20250801232309#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvt4ecbxj8", "Integrity": "vSGHLgsTUyPJ/44RUV0SRITlmHToPp+9Z8t0HxjLhyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_147258369_file2_20250801232309.pdf", "FileLength": 652098, "LastWriteTime": "2025-08-01T20:23:09+00:00"}, "elY03uf1cm9fMLTMLV5aKCTwhosASOAg+kdqHuUCHE0=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_147258369_file1_20250802001459.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_147258369_file1_20250802001459#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvt4ecbxj8", "Integrity": "vSGHLgsTUyPJ/44RUV0SRITlmHToPp+9Z8t0HxjLhyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_147258369_file1_20250802001459.pdf", "FileLength": 652098, "LastWriteTime": "2025-08-01T21:14:59+00:00"}, "sTao4DBztnC9TII3jxUMCgRucweCW8U414qmgBXs6yo=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_147258369_file1_20250801235838.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_147258369_file1_20250801235838#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvt4ecbxj8", "Integrity": "vSGHLgsTUyPJ/44RUV0SRITlmHToPp+9Z8t0HxjLhyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_147258369_file1_20250801235838.pdf", "FileLength": 652098, "LastWriteTime": "2025-08-01T20:58:38+00:00"}, "cRFjftPmugVVIvSFIOCTwRlBtMfT/vZinqn72XSKIV8=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_147258369_file1_20250801235456.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_147258369_file1_20250801235456#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvt4ecbxj8", "Integrity": "vSGHLgsTUyPJ/44RUV0SRITlmHToPp+9Z8t0HxjLhyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_147258369_file1_20250801235456.pdf", "FileLength": 652098, "LastWriteTime": "2025-08-01T20:54:56+00:00"}, "8A6DcS0AuvADiFhgYM3pkUGtEv7r1UMUvAiGh/QT3m4=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_147258369_file1_20250801235325.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_147258369_file1_20250801235325#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvt4ecbxj8", "Integrity": "vSGHLgsTUyPJ/44RUV0SRITlmHToPp+9Z8t0HxjLhyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_147258369_file1_20250801235325.pdf", "FileLength": 652098, "LastWriteTime": "2025-08-01T20:53:25+00:00"}, "kaVgIVH9HSMDOWW34wKY+2h5017LgYIl4kctnhaxjuA=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_147258369_file1_20250801230416.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_147258369_file1_20250801230416#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wpwkvdozv9", "Integrity": "XApdrZRO6zTw/32NF7zRhkCkHPVyZIVI1N/Q9PTL0ww=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_147258369_file1_20250801230416.pdf", "FileLength": 793786, "LastWriteTime": "2025-08-01T20:04:16+00:00"}, "vpmlyjn/+HRoN2CF5jBC6OwaDursImbthVPAP1OBYvw=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_1234567890_file1_20250710195413.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_1234567890_file1_20250710195413#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9psus3l2z1", "Integrity": "M7v8Td5tSoL2OG4C2tCU6r1OHuKiiInnmu0yL2yEO6w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_1234567890_file1_20250710195413.pdf", "FileLength": 326409, "LastWriteTime": "2025-07-10T16:54:13+00:00"}, "9HioSrnfvIfl1k3Lfy27bHhYqiOSrs8oX3+RgJT/gXI=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_1234567890_file1_20250704150713.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_1234567890_file1_20250704150713#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wdbk1wabyj", "Integrity": "pPcNFOHBw4Ef3gPClk6tgsGHZT+tnzHeuazerOEO3wM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_1234567890_file1_20250704150713.pdf", "FileLength": 174317, "LastWriteTime": "2025-07-04T12:07:13+00:00"}, "ZAZATh4M+ADRO3we+GqCUBUlaOljncmUxEvbcQ2xPuk=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\order_printed\\17.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "order_printed/17#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "gtg9o89r3l", "Integrity": "ZEUCsMqx7q1er333UjU7jvATXU1HSjD2ejSDrMo0dHA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\order_printed\\17.pdf", "FileLength": 335755, "LastWriteTime": "2025-08-03T21:08:03+00:00"}, "go6NINOr9YXDtds4/qGLZwy+suSZMzH1Dl7mODAMefQ=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\order_printed\\11.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "order_printed/11#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "phmm8uv3he", "Integrity": "XQvTBLxLf9pnDORFNII1LUpGmL4+ofMFtl5z6VwGHds=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\order_printed\\11.pdf", "FileLength": 335779, "LastWriteTime": "2025-08-10T20:22:13+00:00"}, "4+k+wsiJwI1ADkoydA3TXOf/3QFizHA56pgdbPkLN5E=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "f/JHicm/pBemWVlrcdYtEpAAHyxlR48C2e0GkdssP1k=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5pze98is44", "Integrity": "OZVI+w57FGwS9boYCZpH1ZSpcP7pYhLu4KtIUvPlZ4I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 137972, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "70LHXauEdW5kvt4uahdOB10G6zri0esX7B68qKe0MVA=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dd6z7egasc", "Integrity": "/xUj+3OJU5yExlq6GSYGSHk7tPXikynS7ogEvDej/m4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 89501, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "vgbXYenJS0liwvPxrV9Oj/h840UQGw7w4GiccxoROMo=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fwhahm2icz", "Integrity": "H+K7U5CnXl1h5ywQfKtSj8PCmoN9aaq30gDh27Xc0jk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 288580, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "HOICQ5WLXQk54Spl1wMweeeGpDz7qhENPJZMSxxAlNs=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "qi9uUJdUBTVHab9hxFtDtLxSnfGSfloej616AS3Qq7s=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7iojwaux1", "Integrity": "JwUksNJ6/R07ZiLRoXbGeNrtlFZMFDKX4hemPiHOmCA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 24601, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "mfmBCFNiL6j656EbI6PL4g4V4s7mSb0gUrA7rVJBGGY=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pzqfkb6aqo", "Integrity": "m0l81WDPiG7CcG7CDsTuZzvcGvyFmrQY5DLIxx3aRGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 51171, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "Kzv0HtmNXXuRsTTAop3L5+6YjrjWqdOHUj3p6ecEHis=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9oaff4kq20", "Integrity": "N11IyJpHTgDcSCb3AfX4VrBnpGQeem1NoNzzgcXVyCc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22177, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "DJbEh2H1RzMOPblEsYRxlc+nl8oCIMjWJez8IIT4RcM=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ay5nd8zt9x", "Integrity": "4jrcLBsi0Ugm8iLKdqDsAyaCDjkscYZdoGuNH/zqs4E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 52977, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "yVyOuWCXHPUUI5dUiscj5+6cyA0W1wJBHmQ7rYd4CLM=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "z/5q4JBn2bb8OHFrtcYdetAri0Q+e18wf11TyRH/1rA=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "Lfvt+zKvp9axqKQVxzCtFg6VQYxGmkiiN4UpfSFlijs=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/jquery-validation-unobtrusive/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "LGWLBES8IcepDqvkcr1lO2pXCWvp5YyR8QeLgmR3LEU=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "PV4Mwmngjx3nZlJDWHchNIrl8n3DHy6R0Gh2Z40w1jY=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u33ctipx7g", "Integrity": "ui/FQI+y0IUsY8Pbi80b8s3GeEL+PsvdaLTONobpn88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 217145, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "ZYGlML9/w0FQ67bUmcs98jQzG8Yfm9VcjF3yumqGHto=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zwph15dxgs", "Integrity": "c4Ll6eSIg6Eothk8pCWAF8aE923EvtU11pqjBy+NjNM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 59511, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "rCBBp6Rql5hpjNFPfrvDI69I3P+dIFA40OnYxPmHF+g=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6<PERSON><PERSON><PERSON><PERSON>bh", "Integrity": "Qkl5mZUZ64aYBaORRMP9jfD1kz8J6FwiV2M86JDJkdQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 289522, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "T4rthG53XfQS7iMxJFHMOdrrUNdbHKCU2EJJqLm/njM=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o4kw7cc6tf", "Integrity": "6IStRQerBchYSw6J2GWTOWGOnDRrWXmaG0r6nCwN5s4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 148168, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "KKpPPsx1eLMGsb8i3dfn8/X7TlCvgkVLW68lwRRrbGk=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wsezl0heh6", "Integrity": "sPqzWcSS9aRa2gpWTVNQzemajn8hrFjgXPj3j9QItQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222508, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "GVi37odJHe+P+iugwo1jTo9keaCEcNXrbKm/R0Hh+Aw=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "um2aeqy4ik", "Integrity": "Kj4irQWPwfSb5NFeos/h0IroI5/nIg0HtAjQ+w4v6TE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 72016, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "iQRgDE26Odvf9J4MwjltBxRx2snrWfj1Cw8D8o2Djzg=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2lgwfvgpvi", "Integrity": "CllC/sbLvyLE9cQljmFRlITfqdZRnBv2ysX5LJtl/dg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 288320, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "p4Ae/04fXxzvNSOBJkcBcIxCCiDBjPA8Im0YX8vE+f8=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "m39kt2b5c9", "Integrity": "EuDXUJYKnfZuO8dSLN0f5iVbVasz36AROuAU3NJ3JBo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 139019, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "slGutAzaIi7U0Ho1QmRNfYWSe1SXL73+lTcKs1TDuEw=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2nslu3uf3", "Integrity": "xIBBxDPvWhk8/JdaFEZoejadfaKFUfZFwRS1D4Jkuro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 327261, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "uBXm3qmgR7eWBokh8B7+nRu642A76OjlFHs/iXYjF3A=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "25iw1kog22", "Integrity": "KuvCVS19rfTjoLgMyDDCdOkRRlhNrY4psEM4uezts2M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 78468, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "Z/LKDCtqGcoDiNRg9Vl0k8rOVgqR4OYyCu6jLzqnZI4=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbynt5jhd9", "Integrity": "gO4uhxfGuK0ONjRlHuwfghGfEXT5azm1oHWnTEFGTfk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 425643, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "/BYPFlAnoYhTX6LbcOahNvEfQn3dYj7lPL0SSNddOsg=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l2av4jpuoj", "Integrity": "vQTf4d3WJi9vmWQNA4kJnjoedgEhMFXFDEMXqtHtgzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 208492, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "9DRYC6YJHJ8ViCGpOOtza/KWJVOrBQ55UxMxpDJy8ow=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fctod5rc9n", "Integrity": "j7uqK5VoTT4rUHMr911QEU5Sa94lR3uh9E28XBMlzrM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 661035, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "vMeiLeJS4pn+uiDtRETsai9t64PzV2kdduBJwBB3cps=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "9b9oa1qrmt", "Integrity": "22wR6QTidoeiRZXp6zkRQyMSUb/FB+Av11jqmZJF6uU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 162825, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "tXcHaGj9CMNu8a6TwGyU/pH8qQDtKD3HAjB3q36vTUg=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4gxs3k148c", "Integrity": "VFvmi/ZSwQFmjS6Pry9B8zXeZ/GA168TXLyykDhNMZE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 536461, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "s3mdFxG3HRrb3iR3TlrXeo/8bovlMD4+EFFJO4opoUc=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ve6x09088i", "Integrity": "SZ2mKaD4A+b+HIvttwl+TvLFnVy8o8/X40j+EKVwyvY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 203803, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "0HD/coElfLdYa385GGuwu4BDSTBOToZvFycyC7//tNE=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8inm30yfxf", "Integrity": "gBwg2tmA0Ci2u54gMF1jNCVku6vznarkLS6D76htNNQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 449111, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "G4w0Y2QqlQlXcqYK7+KYqy+HGSgjd3P3mwl4amQelE8=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bpk8xqwxhs", "Integrity": "z8OR40MowJ8GgK6P89Y+hiJK5+cclzFHzLhFQLL92bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 162720, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "t0fF79To006Pad8qYsum4HwU1j7Xp/hMHDeSSZ8//NE=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "73kdqttayv", "Integrity": "DRvWr0gangj5/5Q3DRn6ttzpcWDzl3OpHoAwAzNDR5Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 536547, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "9aV5LpkBI/1C3DqnAFRjU7/063UyAhdBaBWyCDD4HI4=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mpyigms19s", "Integrity": "xlexqj9/k3uobVwGfciZcj/eDdooaNgcf4OFLtLUygM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 204136, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "XLFvPEK18vDjLj/tjQFHOUd/E8QehrXPskMXtHbZo0o=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "56d2bn4wt9", "Integrity": "02ka4ymoE5yEecLUncLG3/SouTQMnTJOktX+96Pt/88=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 111710, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "GI7x7fG+bUtjmRWzpup3TFKegXTHOKnGuAglvyZqDTg=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "u3xrusw2ol", "Integrity": "Wi5ZuFSHLfx6dlEgjvW3BY9TC/1NqdBjj+XFifSSqN4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 53407, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "IJyOF5yBvSd4i9PbF+cM+RzXayAvm4jb86/bWBDsj9s=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ab1c3rmv7g", "Integrity": "puDgKwvlFAord9R8G8of9P2CVYIJUFSoIbjDLEsKEH0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 192214, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "FU9l5xEgU0CBtOoV4b/FWAEBjZXXbm5WoWmUahgfqLE=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b4skse8du6", "Integrity": "peAGH8Gu/ZL9VnbUGSMN69Ji5MxwbvOb53gDXU2cPaQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 71451, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "MQgDDY1mfklzwaPLEPzSWOL/MGAr7we8T0j9Kmhfwsg=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vy0bq9ydhf", "Integrity": "p1dop4slefZhL4zG2pa6+2HUrOY1UUArGJXmet8Md9c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 111875, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "fgWsT24pLdKb++5bNfu5lUALlMbPAK56QeHg/s4h+gg=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "16095smhkz", "Integrity": "5+ExmMkiaI3keYQRLhNibJ5ZXnNuWRbwrXOAZoTXMFg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 53479, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "U3DvicMAbSjeLMQGiNlO6yplOu3ErwB7cEegOmnq+go=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j75<PERSON><PERSON><PERSON>", "Integrity": "4WIqPof/vrXYO/jeJ4fDOQKUYWIwe64V3d+9/qNju20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 192271, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "XHKmjVoWzqFcWQ13Z1YZjs4ZQ7sd/pBKNBIppJfZsRM=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tey0rigmnh", "Integrity": "NbFZxZLmBVNLzb/7B0WdFfb6+8jXHGX6XY190uwgbec=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 71584, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "MQOKtdhYp0jV7pV1a8k2wj6JiRYMmvENErJ+9l/EPV4=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "wmug9u23qg", "Integrity": "GMDk5pA5dFkOimkBAWeEjYZ+7lgHPS0jYln6p/WJVYs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 41570, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "5CysbPoherj3UwrqIcwPtPJW6jjz9e398QDDw9OClqs=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "npxfuf8dg6", "Integrity": "a5KlgysZ4fQXw4rzIvXDHErFDPeHRSLccP7kX6HuvSQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 4718, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "eesPeY+n2u/LVzMKmKb4m60wNHeAfSaMyakuUa2WoiI=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cwzlr5n8x4", "Integrity": "/EdWHN6t5XYPplC88vixGfrBvfEii19kAssb+0YBVU8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 105151, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "7NQQkri4O5Bx5ENAGrMihS2ksSfflHv1fJFq8oEBnT0=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0wve5yxp74", "Integrity": "8NXw3kF49FkQVPMdjnGDqoXXRU0TwzsLfCGbK9U8gnk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 5827, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "1mSid6An/APrp1oaNLegIbQY9PPRdwCfcQa5jK8yuGM=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "okkk44j0xs", "Integrity": "2BbRsE/+czX1ufmDPGpnEieC9u6I3m5BKNDSX1ob3lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 35330, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "Zlu0TfxI23rHLhCxb4A/R1P0wRa27vZB4+PlVfCKm1w=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f8imaxxbri", "Integrity": "z0OApR88UEocYXTXHU7Ueycaiib9XbDUmel9Gx0gbx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 4646, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "1nIZVXvNeI2vO7jZLNvnI5HuUoUPbLxMpfJJL/COFiI=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jeal3x0ldm", "Integrity": "FZG0KxbNqITUi4QY7QvPFRS/TccntMfFWfSTdHN/pws=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 105138, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "YvLa7riBSEDhlwArN385o/voFjN97EyHMGUGgbUipMY=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "7na4sro3qu", "Integrity": "4zbWr0QNFhpUwGkn4WdGWXt80KnhRFv0qXkZyVnhajY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 5850, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "Vzzc6fJMH0rshaXHWejAO7XYw6US5epdreqgbxia9mQ=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o371a8zbv2", "Integrity": "NDSZjIiMPRIoO7/w7+jHef8retP4riQa8PMj4BVRGok=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 117516, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "ymJYrZlkDm9Adhj3ZZiR3Ue6MXAMgBynrOEJFDDK2Ak=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "n1oizzvkh6", "Integrity": "O6lb2kXarGgVw4/RDD42yYPhZIwREthThQFKGmD+3j0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51394, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "Zc54gc9/bTQs8B1IBPfx3pwMXi4jEZvpmyGWtRp4mXs=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2q4vfeazbq", "Integrity": "qvA39OMlEs53jaewqVFmE8DQQrio47bZtlTs+Wu6U8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 196539, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "hKNVBUpIqd3k8Mt+Jz7S0i3+tcLr9jhzDXVLR3IHlbg=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "q2ku51ktnl", "Integrity": "3vUJkZSpKL/zG7x6GNvDjs0TxYUo9zMt6dAc8hp9CVo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70612, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "sHMVFZlRjekXjIB3aipIGmfN4lo3laXKejRwhxYw0+I=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "5vj65cig9w", "Integrity": "72C/qDCGu+OwWeVA03bf9Ke0T8oIozCub0lfJkhzhvE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 117439, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "gQ3PDN+nE7fjS3vS4lSfBvyoMv8rovG5Q3QKdofNyvc=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "unj9p35syc", "Integrity": "ysBT/JYxH9gcMnwxT4+MB4sPxOx/JMg9wi77FA13T9A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51319, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "BuL9bltxUNpolJIwn7qsNc9wBb+t87mcHpEFM9SHQ7M=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "st1cbwfwo5", "Integrity": "QO8cMbVkLiktUX1cHeXSUSe5nXMXUgyL9cjwnMyxPqc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 196535, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "axIUfcTxqK/sgzLI8vwCS3TBby5H9j5QIRMhUj9JZoM=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "agp80tu62r", "Integrity": "JtktgiuQAd+AXerCnPMrHCDz1h5AtkH5tobvpuG7xZ4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70538, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "GoNM3EUu/xA9SGvWfQ2swfMR+/CoyQKSoLezhRh8uro=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\supervisorOrders.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/supervisorOrders#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "p8mnp6xcyp", "Integrity": "WXHDx1dWkCw3uXfDgzFom5hOT4fNfG5j79C/EEqtCmA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\supervisorOrders.js", "FileLength": 33941, "LastWriteTime": "2025-09-18T21:16:20.4081659+00:00"}, "mo9RtreUnjfFjbefAQJYGVZfqB0N0Ucr8rrBfQKQ9AQ=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\site.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-06-21T20:31:49+00:00"}, "2Celv6/KDJ/2Ep4VCeXl4IfTUhxIkW3rFmuG9vWclTY=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\shared-utils.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/shared-utils#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lri8h1ahbj", "Integrity": "AUbExRbpgTQeJ2NcFAONm7tfP2qfaDzTipMD9/g9Wbk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\shared-utils.js", "FileLength": 8396, "LastWriteTime": "2025-08-03T21:25:17+00:00"}, "YGaE99pdMca3e9dNPQ4O9gqIIUvP4zVoNDuKm/Yi8gA=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\pathManagement.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/pathManagement#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4auzfdj0yu", "Integrity": "DHdskasTgFAvd5mCSNulBaEaBDVHNbI7jJuMnB5WYrg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\pathManagement.js", "FileLength": 12313, "LastWriteTime": "2025-07-09T19:08:37+00:00"}, "ikDWce0O7cOixuJhhA6JqcZJL1mv8Csb4G8SLe21HpQ=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\orderStatusNotifications.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/orderStatusNotifications#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pqwxw<PERSON><PERSON><PERSON>", "Integrity": "AVBhvQu4XB4ENiw/gDJAq4m8jgk1RkYhBzHbJXurl3U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\orderStatusNotifications.js", "FileLength": 16661, "LastWriteTime": "2025-09-13T20:58:13+00:00"}, "3sOYteXrTixtmudUjXtLy41cAZ68wEorbGdUcSr/dPw=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\OrderDetals.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/OrderDetals#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v9fnrwrq4x", "Integrity": "7aeFf4QEoZykITPAxNSFGs21ByohvRSN13qswjmE3ac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\OrderDetals.js", "FileLength": 11664, "LastWriteTime": "2025-08-23T21:42:37+00:00"}, "x5u631qv+QDHvwUB4s5+/Wp7JPHneOXZwDNO83UkWrw=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\orderDetailsModule.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/orderDetailsModule#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dl4ieh5u6s", "Integrity": "wWlFKlZCNHhUpymAkKWqDFU4RpAkYJj+xRUT3SD43MM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\orderDetailsModule.js", "FileLength": 14678, "LastWriteTime": "2025-09-14T20:20:57+00:00"}, "0ulF1HtyGDY/ppEf/QQCwUXXxwdi2vfWxIAVYvQjo4o=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\Notification.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/Notification#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "zc2h8ylawz", "Integrity": "zTmGkRgSxKH9WdpAKmdC5CuP6CUXq2PiYgjfrjbGLq4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\Notification.js", "FileLength": 5915, "LastWriteTime": "2025-08-03T21:52:25+00:00"}, "hhLIJwAD4CyP99RtAF2LKGTpYTStXm6n2HSpHODuAO8=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\hrmanager.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/hrmanager#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "f2jq7dfyej", "Integrity": "Bw7xRe2d+tgnhFtZLIYHkPgcbPzsMmp6pC40LV+rIQg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\hrmanager.js", "FileLength": 25798, "LastWriteTime": "2025-09-18T22:34:59.9157755+00:00"}, "9p+5zGOCEPWttrgY960mEP5qVbXO+5aABl5orFtqp2g=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\hrCoordinator.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/hrCoordinator#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xn7t9o2y5z", "Integrity": "84qR3ZSREK/e6FOG2vPa8iBRMKqxR50q9GUcslF671E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\hrCoordinator.js", "FileLength": 38729, "LastWriteTime": "2025-09-18T19:44:09.1714383+00:00"}, "3YCZU9SxFk7OdKUR6TM6GFtWc/8lRJILM8UNfOHNEKc=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\directManager.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/directManager#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bx8trjx6z8", "Integrity": "b4nMzWM3WzJh3BFWLm9B5X3fCm+8dHkNT/ZPFGAwC1c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\directManager.js", "FileLength": 7828, "LastWriteTime": "2025-08-02T15:52:43+00:00"}, "8gJcLqDZBUIrEkK4+hmScmEXHa8oUCHCTEXhw9uG/eo=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\dashboard.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/dashboard#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "908fh1rlrm", "Integrity": "SRvuKIJ3255P7wDrXPs+y8P6VALq+YhkA8k1ehtfqEc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\dashboard.js", "FileLength": 4030, "LastWriteTime": "2025-08-03T22:05:15+00:00"}, "u42WZxwDlX9fW+GYrxmSnwPWBE1iCFt8ef8H++cY88E=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\dashboard-charts.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/dashboard-charts#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2dw3qkz4nn", "Integrity": "liBNr2PGnkBsEQ2rztX5tUvoJp9HUg/bWdIaV5PALT8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\dashboard-charts.js", "FileLength": 9104, "LastWriteTime": "2025-07-05T10:48:11+00:00"}, "zw4I5Yz2XUFcZtLsdWF4wqfJiZ9LmfKTmMaqg6RVAsE=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\js\\assistantManager.js", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "js/assistant<PERSON>anager#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "l99oqmn03m", "Integrity": "F9n09QfxnCe/qljtvEf3bfWcy7RPdXAggAv1UKYWpa8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\assistantManager.js", "FileLength": 8478, "LastWriteTime": "2025-08-02T22:05:22+00:00"}, "CryoTpYEUMPwwG8IfiRAysnvnlYk+ck4v3+RnK556yk=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\hero-svg-illustration.svg", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/hero-svg-illustration#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rhdii9jgqu", "Integrity": "MaXR7x3vz5T5vVgpYi7392hhJIv/lh9PzodJNCTt2K4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\hero-svg-illustration.svg", "FileLength": 6761, "LastWriteTime": "2025-06-27T14:04:00+00:00"}, "ooMPq5WPh2bga6LO0mT1XeDedHEeYNuFajtKXiAL8u4=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\img\\Breada1.png", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "img/Breada1#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r49qvtps7x", "Integrity": "u1NEt5uDu69/fXrFWrdVmBllySF4rOE3Ldb+p+eWiZk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\img\\Breada1.png", "FileLength": 126397, "LastWriteTime": "2025-06-27T13:58:30+00:00"}, "7begaR+n4h5AMg74tCO6xW/izD0+WLQDEjfoA3GY66s=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\favicon.ico", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "3djvn4e135", "Integrity": "OWWJaDDMsSyzwiAq56NrhqM61v7tYmOC+0cwr+SLErs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 4022, "LastWriteTime": "2025-06-27T14:07:54+00:00"}, "GIM604s6ss8MhvUhpQsLUx7YFqwpuKQ7MnsTX3KPcsg=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\css\\MainSite.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "css/MainSite#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "8nrgscigmb", "Integrity": "DKQxH8W+rZfiMPKzdFwlOsGSmpZRI2jBCnoBiC6U2W8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\MainSite.css", "FileLength": 41973, "LastWriteTime": "2025-08-15T16:10:37+00:00"}, "uU0z9WZa8vOUsyFgX0Jo0bdY6XYmqyHTfH4VhYntGok=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\css\\dashbord\\dashbord.css", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "css/dashbord/dashbord#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "e9wfd9qf13", "Integrity": "7l2N4aJ7x/+rlwJvHoyyLObrZ73steSaruyzaxVSWII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\dashbord\\dashbord.css", "FileLength": 36169, "LastWriteTime": "2025-08-03T21:53:35+00:00"}, "OgOym10REGfkVzmL1Ktt7ilH+OlQB7ngFWkGYHMa++A=": {"Identity": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\uploads\\order_45_1022427874_file1.pdf", "SourceId": "OrderFlowCore.Web", "SourceType": "Discovered", "ContentRoot": "E:\\Projects\\abozyad\\OrderFlowCore\\OrderFlowCore.Web\\wwwroot\\", "BasePath": "_content/OrderFlowCore.Web", "RelativePath": "uploads/order_45_1022427874_file1#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "51sk3n88mr", "Integrity": "6emshGH68nU+krAbKMC67P0PtmoRHIuvXkRG54DBOAo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\order_45_1022427874_file1.pdf", "FileLength": 69947, "LastWriteTime": "2025-09-19T11:25:41.5754351+00:00"}}, "CachedCopyCandidates": {}}