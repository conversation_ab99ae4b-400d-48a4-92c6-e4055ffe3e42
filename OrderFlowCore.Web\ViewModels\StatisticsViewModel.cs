using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Services;
using System.Collections.Generic;

namespace OrderFlowCore.Web.ViewModels
{
    public class StatisticsViewModel
    {
        public GeneralStatisticsDto GeneralStatistics { get; set; } = new GeneralStatisticsDto();
        public StageStatisticsDto StageStatistics { get; set; } = new StageStatisticsDto();
        public List<OrderDepartmentStatisticsDto> DepartmentStatistics { get; set; } = new List<OrderDepartmentStatisticsDto>();
        public List<SupervisorStatisticsDto> SupervisorStatistics { get; set; } = new List<SupervisorStatisticsDto>();
        public List<AssistantManagerStatisticsDto> AssistantManagerStatistics { get; set; } = new List<AssistantManagerStatisticsDto>();
        public List<TransferTypeStatisticsDto> TransferTypeStatistics { get; set; } = new List<TransferTypeStatisticsDto>();
        public List<HRCoordinatorReportDto> HRCoordinatorStatistics { get; set; } = new List<HRCoordinatorReportDto>();
        public List<HRManagerReportDto> HRManagerStatistics { get; set; } = new List<HRManagerReportDto>();
        public List<CompletedSupervisorReportDto> CompletedSupervisorStatistics { get; set; } = new List<CompletedSupervisorReportDto>();
        public List<SupervisorDelayStatisticsDto> SupervisorDelayStatistics { get; set; } = new List<SupervisorDelayStatisticsDto>();
        public List<PendingOrdersStatisticsDto> PendingOrdersStatistics { get; set; } = new List<PendingOrdersStatisticsDto>();
        public List<UnifiedPendingStatisticsDto> UnifiedPendingStats { get; set; } = new();
        public YearlyStatisticsSummaryDto MonthlyStatistics { get; set; } = new YearlyStatisticsSummaryDto();
        public List<TransferTypeStatisticsDto> OrderTypesStatistics { get; set; } = new List<TransferTypeStatisticsDto>();

    }
}