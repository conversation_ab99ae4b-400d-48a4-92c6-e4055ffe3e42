/**
 * Order Status Notifications Module
 * Handles the display of status notifications for order details
 * @version 2.0.0
 */
const OrderStatusNotifications = (function () {
    'use strict';

    // Constants
    const SELECTORS = {
        PROGRESS_CARD: '.progress-card',
        NOTIFICATION_SECTION: 'section[data-notification]',
        ALERT: '.alert[class*="alert-"]'
    };

    const ALERT_TYPES = {
        WARNING: 'alert-warning',
        INFO: 'alert-info',
        DANGER: 'alert-danger'
    };

    const COLORS = {
        WARNING: { bg: '#ffc10740', text: '#ffc107' },  // Bootstrap warning
        INFO: { bg: '#0dcaf040', text: '#0dcaf0' },     // Bootstrap info
        DANGER: { bg: '#dc354540', text: '#dc3545' }    // Bootstrap danger
    };

    const ICONS = {
        WARNING: 'fas fa-exclamation-triangle',
        RETURN: 'fas fa-undo',
        CANCEL: 'fas fa-times-circle'
    };

    // Status notification configurations
    const statusConfigs = {
        'يتطلب إجراءات': {
            type: 'warning',
            alertClass: ALERT_TYPES.WARNING,
            icon: ICONS.WARNING,
            title: 'إجراء مطلوب على الطلب',
            message: 'يرجى اتخاذ الإجراء المطلوب لمعالجة طلبك.',
            colors: COLORS.WARNING,
            showAttachmentMessage: true,
            requireAction: true
        },
        'يتطلب إجراءات من المشرف': {
            type: 'warning',
            alertClass: ALERT_TYPES.WARNING,
            icon: ICONS.WARNING,
            title: 'إجراء مطلوب من المشرف',
            message: 'يرجى اتخاذ الإجراء المطلوب من المشرف لمعالجة طلبك.',
            colors: COLORS.WARNING,
            showAttachmentMessage: true,
            supervisorsRequireAction: true
        },
        'أُعيد بواسطة أحد المنسقين': {
            type: 'info',
            alertClass: ALERT_TYPES.INFO,
            icon: ICONS.RETURN,
            title: 'تم إعادة الطلب من المنسق',
            message: 'تم إعادة الطلب من منسق الموارد البشرية للمراجعة.',
            colors: COLORS.INFO,
            showAttachmentMessage: false
        },
        'أُعيد بواسطة أحد المشرفين': {
            type: 'info',
            alertClass: ALERT_TYPES.INFO,
            icon: ICONS.RETURN,
            title: 'تم إعادة الطلب من المشرف',
            message: 'تم إعادة الطلب من المشرف للمراجعة.',
            colors: COLORS.INFO,
            showAttachmentMessage: false
        },
        'تمت الإعادة من مساعد المدير': {
            type: 'info',
            alertClass: ALERT_TYPES.INFO,
            icon: ICONS.RETURN,
            title: 'تم إعادة الطلب من مساعد المدير',
            message: 'تم إعادة الطلب من مساعد المدير للمراجعة.',
            colors: COLORS.INFO,
            showAttachmentMessage: false
        },
        'تمت الإعادة من المدير': {
            type: 'info',
            alertClass: ALERT_TYPES.INFO,
            icon: ICONS.RETURN,
            title: 'تم إعادة الطلب من المدير',
            message: 'تم إعادة الطلب من مدير الموارد البشرية للمراجعة.',
            colors: COLORS.INFO,
            showAttachmentMessage: false
        },
        'تم الإلغاء من مدير القسم': {
            type: 'danger',
            alertClass: ALERT_TYPES.DANGER,
            icon: ICONS.CANCEL,
            title: 'تم إلغاء الطلب من مدير القسم',
            message: 'تم إلغاء الطلب من قبل مدير القسم.',
            colors: COLORS.DANGER,
            showAttachmentMessage: false
        },
        'تم الإلغاء من مساعد المدير': {
            type: 'danger',
            alertClass: ALERT_TYPES.DANGER,
            icon: ICONS.CANCEL,
            title: 'تم إلغاء الطلب من مساعد المدير',
            message: 'تم إلغاء الطلب من قبل مساعد المدير.',
            colors: COLORS.DANGER,
            showAttachmentMessage: false
        },
        'تم الإلغاء من قبل المشرف': {
            type: 'danger',
            alertClass: ALERT_TYPES.DANGER,
            icon: ICONS.CANCEL,
            title: 'تم إلغاء الطلب من المشرف',
            message: 'تم إلغاء الطلب من قبل المشرف.',
            colors: COLORS.DANGER,
            showAttachmentMessage: false
        },
        'تم الإلغاء من قبل المنسق': {
            type: 'danger',
            alertClass: ALERT_TYPES.DANGER,
            icon: ICONS.CANCEL,
            title: 'تم إلغاء الطلب من المنسق',
            message: 'تم إلغاء الطلب من قبل منسق الموارد البشرية.',
            colors: COLORS.DANGER,
            showAttachmentMessage: false
        },
        'تم الإلغاء من قبل المدير': {
            type: 'danger',
            alertClass: ALERT_TYPES.DANGER,
            icon: ICONS.CANCEL,
            title: 'تم إلغاء الطلب من المدير',
            message: 'تم إلغاء الطلب من قبل مدير الموارد البشرية.',
            colors: COLORS.DANGER,
            showAttachmentMessage: false
        }
    };

    /**
     * Utility functions
     */
    const utils = {
        /**
         * Sanitize HTML content to prevent XSS
         * @param {string} str - String to sanitize
         * @returns {string} Sanitized string
         */
        sanitizeHTML(str) {
            if (!str) return '';
            const temp = document.createElement('div');
            temp.textContent = str;
            return temp.innerHTML;
        },

        /**
         * Check if element exists in DOM
         * @param {string} selector - CSS selector
         * @returns {Element|null} Found element or null
         */
        getElement(selector) {
            return document.querySelector(selector);
        },

        /**
         * Validate required parameters
         * @param {Object} params - Parameters to validate
         * @returns {boolean} True if valid
         */
        validateParams(params) {
            const { currentStatus } = params;
            if (!currentStatus || typeof currentStatus !== 'string') {
                console.warn('OrderStatusNotifications: Invalid currentStatus parameter');
                return false;
            }
            return true;
        }
    };

    /**
     * Create additional content sections based on action requirement
     * @param {Object} config - Status configuration
     * @param {string} reasonForCancellation - Cancellation reason
     * @param {string} coordinatorDetails - Coordinator details
     * @returns {string} Additional content HTML
     */
    function createAdditionalContent(config, reasonForCancellation, coordinatorDetails, supervisorsNotes, supervisorsListThatRequireAction) {
        let content = '';

        if (reasonForCancellation && reasonForCancellation.trim()) {
            content += `
                    <div class="alert alert-light border-0 text-dark mt-3 mb-0">
                        <strong>سبب الإلغاء/الإعادة:</strong><br />
                        <span>${utils.sanitizeHTML(reasonForCancellation)}</span>
                    </div>
                `;
            return content;
        }

        // Check if status requires action first
        if (config.requireAction || config.supervisorsRequireAction || supervisorsListThatRequireAction) {
            const message = config.requireAction ? coordinatorDetails : supervisorsNotes;
            const heading = config.requireAction ? 'تفاصيل المنسق:' : 'ملاحظات المشرف:';
            // Show action required section only
            if (message && message.trim()) {
                content += `
                    <div class="alert alert-light border-0 text-dark mt-3 mb-0">
                        <strong>${heading}</strong><br />
                        <span>${message}</span>
                    </div>
                `;
            }

            // Add attachment message for action required statuses
            if (config.showAttachmentMessage) {
                content += `
                    <div class="mt-4">
                        <span class="text-muted">يمكنكم تحديث المرفقات المطلوبة أدناه.</span>
                    </div>
                `;
            }
        }

        return content;
    }

    /**
     * Create notification HTML
     * @param {Object} config - Status configuration
     * @param {string} reasonForCancellation - Cancellation reason
     * @param {string} coordinatorDetails - Coordinator details
     * @returns {string} HTML string for the notification
     */
    function createNotificationHTML(config, reasonForCancellation, coordinatorDetails, supervisorsNotes) {
        const additionalContent = createAdditionalContent(config, reasonForCancellation, coordinatorDetails, supervisorsNotes, supervisorsListThatRequireAction);

        return `
            <section data-notification="true" class="notification-section">
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <div class="alert ${config.alertClass} shadow-lg border-0 rounded-4 p-4 text-center animate-fade-in">
                                <div class="mb-3">
                                    <span class="notification-icon d-inline-block bg-opacity-25 rounded-circle p-3 mb-2" 
                                          style="background-color: ${config.colors.bg};">
                                        <i class="${config.icon} fa-2x" style="color: ${config.colors.text};"></i>
                                    </span>
                                    <h2 class="notification-title fw-bold mb-2" style="color: ${config.colors.text};">
                                        ${config.title}
                                    </h2>
                                </div>
                                <p class="notification-message lead mb-2">${config.message}</p>
                                ${additionalContent}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        `;
    }

    /**
     * Remove existing notification
     * @returns {boolean} True if notification was removed
     */
    function removeExistingNotification() {
        const existingNotification = utils.getElement(SELECTORS.NOTIFICATION_SECTION);
        if (existingNotification) {
            existingNotification.remove();
            return true;
        }
        return false;
    }

    /**
     * Insert notification into DOM
     * @param {string} notificationHTML - HTML content to insert
     * @param {string} containerSelector - CSS selector for the container
     * @returns {boolean} True if successfully inserted
     */
    function insertNotification(notificationHTML, containerSelector = '#statusNotificationsContainer') {
        const container = utils.getElement(containerSelector);
        if (container) {
            container.innerHTML = notificationHTML;
            return true;
        } else {
            console.warn('OrderStatusNotifications: Container not found. Notification not inserted.');
            return false;
        }
    }

    /**
     * Initialize status notifications
     * @param {string} currentStatus - Current order status
     * @param {string} reasonForCancellation - Cancellation reason
     * @param {string} coordinatorDetails - Coordinator details
     * @param {string} containerSelector - Container selector (deprecated, kept for backward compatibility)
     */
    function init(currentStatus, reasonForCancellation = '', coordinatorDetails = '', supervisorsNotes = '', supervisorsListThatRequireAction = '', containerSelector = '#statusNotificationsContainer') {
        // Validate parameters
        if (!utils.validateParams({ currentStatus })) {
            return false;
        }

        const config = statusConfigs[currentStatus];

        if (!config) {
            console.info(`OrderStatusNotifications: No notification configuration found for status: ${currentStatus}`);
            return false;
        }

        try {
            const notificationHTML = createNotificationHTML(config, reasonForCancellation, coordinatorDetails, supervisorsNotes, supervisorsListThatRequireAction);
            const inserted = insertNotification(notificationHTML, containerSelector);

            if (inserted) {
                console.info(`OrderStatusNotifications: Notification displayed for status: ${currentStatus}`);
                return true;
            }
            return false;
        } catch (error) {
            console.error('OrderStatusNotifications: Error initializing notification:', error);
            return false;
        }
    }

    /**
     * Update notification for a specific status
     * @param {string} status - New status
     * @param {string} reasonForCancellation - Cancellation reason
     * @param {string} coordinatorDetails - Coordinator details
     */
    function updateNotification(status, reasonForCancellation = '', coordinatorDetails = '', supervisorsNotes = '', supervisorsListThatRequireAction = '', containerSelector = '#statusNotificationsContainer') {
        // Remove existing notification
        removeExistingNotification();

        // Add new notification
        return init(status, reasonForCancellation, coordinatorDetails, supervisorsNotes, supervisorsListThatRequireAction, containerSelector);
    }

    /**
     * Get status configuration
     * @param {string} status - Order status
     * @returns {Object|null} Status configuration or null if not found
     */
    function getStatusConfig(status) {
        return statusConfigs[status] || null;
    }

    /**
     * Check if status requires notification
     * @param {string} status - Order status
     * @returns {boolean} True if status requires notification
     */
    function requiresNotification(status) {
        return statusConfigs.hasOwnProperty(status);
    }

    /**
     * Get all available statuses
     * @returns {string[]} Array of available status keys
     */
    function getAvailableStatuses() {
        return Object.keys(statusConfigs);
    }

    /**
     * Clear all notifications
     * @returns {boolean} True if notifications were cleared
     */
    function clearNotifications() {
        return removeExistingNotification();
    }

    /**
     * Add custom status configuration
     * @param {string} status - Status key
     * @param {Object} config - Status configuration
     * @returns {boolean} True if added successfully
     */
    function addCustomStatus(status, config) {
        if (!status || !config) {
            console.warn('OrderStatusNotifications: Invalid status or config for custom status');
            return false;
        }

        // Validate required config properties
        const requiredProps = ['alertClass', 'icon', 'title', 'message', 'colors'];
        for (const prop of requiredProps) {
            if (!config.hasOwnProperty(prop)) {
                console.warn(`OrderStatusNotifications: Missing required property '${prop}' in custom status config`);
                return false;
            }
        }

        statusConfigs[status] = config;
        return true;
    }

    // Public API
    return {
        // Core functions
        init,
        updateNotification,

        // Utility functions
        getStatusConfig,
        requiresNotification,
        getAvailableStatuses,
        clearNotifications,
        addCustomStatus,

        // Version
        version: '2.0.0'
    };
})();
