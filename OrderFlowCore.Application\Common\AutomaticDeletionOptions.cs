namespace OrderFlowCore.Application.Common
{
    public class AutomaticDeletionOptions
    {
        public const string SectionName = "AutomaticDeletion";

        /// <summary>
        /// How often to run the automatic deletion process (in hours)
        /// Default: 24 hours
        /// </summary>
        public int ExecutionIntervalHours { get; set; } = 24;

        /// <summary>
        /// Whether the automatic deletion background service is enabled
        /// Default: true
        /// </summary>
        public bool IsEnabled { get; set; } = true;

        /// <summary>
        /// The time of day to run the automatic deletion (24-hour format)
        /// Default: 2 AM (02:00)
        /// </summary>
        public string ExecutionTime { get; set; } = "02:00";

        /// <summary>
        /// Maximum number of orders to process in a single execution
        /// Default: 1000 (0 = unlimited)
        /// </summary>
        public int MaxOrdersPerExecution { get; set; } = 1000;
    }
}
