using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Web.Attributes;
using OrderFlowCore.Web.ViewModels;
using System.Security.Claims;

namespace OrderFlowCore.Web.Controllers
{
    [AuthorizeRole(UserRole.Manager)]
    public class HRManagerController : Controller
    {
        private readonly IHRManagerService _hrManagerService;
        private readonly IOrderManagementService _orderManagementService;
        private readonly IDepartmentService _departmentService;
        private readonly ILogger<HRManagerController> _logger;

        public HRManagerController(
            IHRManagerService hrManagerService,
            IOrderManagementService orderManagementService,
            IDepartmentService departmentService,
            ILogger<HRManagerController> logger)
        {
            _hrManagerService = hrManagerService;
            _orderManagementService = orderManagementService;
            _departmentService = departmentService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Index()
        {
            var viewModel = new HRManagerProcessOrderViewModel();

            // Get orders for HR manager
            var ordersResult = await _hrManagerService.GetHRManagerOrdersAsync();

            if (!ordersResult.IsSuccess)
            {
                TempData["ErrorMessage"] = ordersResult.Message;
                return View(viewModel);
            }

            viewModel.OrderNumbers = [.. ordersResult.Data!.Select(o => new SelectListItem
            {
                Value = o.Id.ToString(),
                Text = GetOrderDisplayText(o)
            })];

            return View(viewModel);
        }


        private static string GetOrderDisplayText(OrderSummaryDto o)
        {
            var icon = "";

            if (!string.IsNullOrEmpty(o.AdditionalInfo))
            {
                icon = o.AdditionalInfo switch
                {
                    "جديد" => "🔵 ",            // أزرق
                    "محول مباشرة" => "🔴 ",     // أحمر (بدلاً من الأخضر)
                    "مُعاد من المدير" => "🟡 ", // أصفر (بدلاً من الأحمر)
                    _ => ""
                };
            }

            return string.IsNullOrEmpty(o.EmployeeName)
                ? $"{icon}{o.Id}"
                : $"{icon}{o.Id} | {o.EmployeeName}";
        }



        [HttpGet]
        public async Task<IActionResult> ChangeStatus()
        {
            var viewModel = new HRManagerChangeStatusViewModel();

            // Get orders for status change
            var statusChangeOrdersResult = await _hrManagerService.GetOrdersForStatusChangeAsync();

            if (!statusChangeOrdersResult.IsSuccess)
            {
                TempData["ErrorMessage"] = statusChangeOrdersResult.Message;
                return View(viewModel);
            }

            viewModel.StatusChangeOrderNumbers = [.. statusChangeOrdersResult.Data!.Select(o => new SelectListItem
            {
                Value = o.Id.ToString(),
                Text = $"{o.Id} | {o.EmployeeName} | {o.Department} - {o.OrderStatus.ToDisplayString()}"
            })];

            // Get available statuses
            var statusesResult = await _hrManagerService.GetAvailableStatusesAsync();
            viewModel.AvailableStatuses = [.. statusesResult.Data!.Select(s => new SelectListItem
            {
                Value = s.Value,
                Text = s.Text
            })];

            // Get available departments
            var departmentsResult = await _departmentService.GetAllDepartmentsAsync();
            if (departmentsResult.IsSuccess && departmentsResult.Data != null)
            {
                viewModel.AvailableDepartments = [.. departmentsResult.Data
                    .Where(d => d.IsActive)
                    .Select(d => new SelectListItem
                    {
                        Value = d.Name,
                        Text = d.Name
                    })];
            }

            return View(viewModel);
        }

        [HttpPost]
        public async Task<IActionResult> GetOrderDetails(int orderId)
        {
            var result = await _orderManagementService.GetOrderDetailsAsync(orderId);
            return Json(new { success = result.IsSuccess, message = result.Message, data = result.Data });
        }

        [HttpGet]
        public async Task<IActionResult> DownloadAttachments(int id)
        {
            var result = await _orderManagementService.DownloadAttachmentsZipAsync(id);

            if (result.IsSuccess)
            {
                return File(result.Data, "application/zip", $"مرفقات_طلب_{id}.zip");
            }
            else
            {
                return Json(new { success = false, message = result.Message });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ApproveOrder(int orderId)
        {
            if (orderId <= 0)
            {
                return Json(new { success = false, message = "يرجى اختيار رقم الطلب" });
            }

            var userName = User.Identity?.Name ?? "غير محدد";
            var result = await _hrManagerService.ApproveOrderAsync(orderId, userName);

            return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> RejectOrder(int orderId, string rejectReason)
        {
            if (orderId <= 0)
            {
                return Json(new { success = false, message = "يرجى اختيار رقم الطلب" });
            }

            if (string.IsNullOrWhiteSpace(rejectReason))
            {
                return Json(new { success = false, message = "يرجى إدخال سبب الإلغاء" });
            }

            var userName = User.Identity?.Name ?? "غير محدد";
            var result = await _hrManagerService.RejectOrderAsync(orderId, rejectReason, userName);

            return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ReturnOrder(int orderId, string returnReason)
        {
            if (orderId <= 0)
            {
                return Json(new { success = false, message = "يرجى اختيار رقم الطلب" });
            }

            if (string.IsNullOrWhiteSpace(returnReason))
            {
                return Json(new { success = false, message = "يرجى إدخال سبب الإعادة" });
            }

            var userName = User.Identity?.Name ?? "غير محدد";
            var result = await _hrManagerService.ReturnOrderAsync(orderId, returnReason, userName);

            return Json(new { success = result.IsSuccess, message = result.Message });
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ChangeOrderStatus(int orderId, string newStatus, string newDepartment, string notes)
        {
            if (orderId <= 0)
            {
                return Json(new { success = false, message = "يرجى اختيار رقم الطلب" });
            }

            if (string.IsNullOrWhiteSpace(newStatus) && string.IsNullOrWhiteSpace(newDepartment))
            {
                return Json(new { success = false, message = "يرجى اختيار الحالة الجديدة أو القسم الجديد" });
            }

            var userName = User.Identity?.Name ?? "غير محدد";
            var result = await _hrManagerService.ChangeOrderStatusAndDepartmentAsync(orderId, newStatus, newDepartment, notes, userName);

            return Json(new { success = result.IsSuccess, message = result.Message });
        }


        [HttpPost]
        public async Task<IActionResult> GetStatusChangeOrders(string searchTerm, string filter)
        {
            var result = await _hrManagerService.GetOrdersForStatusChangeAsync(searchTerm, filter);
            if (!result.IsSuccess)
            {
                return Json(new { success = false, message = result.Message });
            }

            var orders = result.Data.Select(o => new SelectListItem
            {
                Value = o.Id.ToString(),
                Text = $"{o.Id} | {o.EmployeeName} | {o.Department} - {o.OrderStatus.ToDisplayString()}"
            }).ToList();

            return Json(new { success = true, data = orders });

        }

        [HttpPost]
        public async Task<IActionResult> GetStatusChangeOrderDetails(int orderId)
        {
            var result = await _orderManagementService.GetOrderDetailsAsync(orderId);
            if (!result.IsSuccess)
            {
                return Json(new { success = false, message = result.Message });
            }

            return Json(new
            {
                success = true,
                currentStatus = result.Data.OrderStatus,
                currentDepartment = result.Data.Department,
                orderType = result.Data.OrderType
            });
        }

    }
}
