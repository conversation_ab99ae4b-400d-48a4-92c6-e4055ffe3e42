using System;
using System.ComponentModel.DataAnnotations;

namespace OrderFlowCore.Application.DTOs
{
    public class AutomaticDeletionSettingsDto
    {
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string SettingName { get; set; }

        [Required]
        public bool IsEnabled { get; set; }

        [Required]
        public int PeriodInMonths { get; set; }

        [StringLength(200)]
        public string Description { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime? LastModifiedAt { get; set; }

        [StringLength(255)]
        public string LastModifiedBy { get; set; }

        [StringLength(255)]
        public string CreatedBy { get; set; }

        public bool IsActive { get; set; }
    }

    public class AutomaticDeletionSettingsUpdateDto
    {
        [Required]
        public int Id { get; set; }

        [Required]
        public bool IsEnabled { get; set; }

        [StringLength(255)]
        public string LastModifiedBy { get; set; }
    }

    public class AutomaticDeletionSettingsCreateDto
    {
        [Required]
        [StringLength(100)]
        public string SettingName { get; set; }

        [Required]
        public bool IsEnabled { get; set; }

        [Required]
        public int PeriodInMonths { get; set; }

        [StringLength(200)]
        public string Description { get; set; }

        [StringLength(255)]
        public string CreatedBy { get; set; }
    }
}
