@{
    ViewData["Title"] = "طلب جديد";
    Layout = "_Layout";
}

<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="p-4">
                    <div class="text-center mb-5">
                        <span class="badge bg-primary bg-opacity-10 text-primary px-3 py-2 mb-3">اختيار نوع الطلب</span>
                        <h2 class="display-6 fw-bold">ماذا تريد أن تنشئ؟</h2>
                        <p class="text-muted">اختر بين تقديم طلب عادي أو إنشاء شهادة لمن يهمه الأمر</p>
                    </div>

                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body d-flex flex-column">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="icon-circle bg-primary bg-opacity-10 me-3">
                                            <i class="fas fa-file-signature text-primary"></i>
                                        </div>
                                        <h5 class="mb-0">طلب عادي / Normal Order</h5>
                                    </div>
                                    <p class="text-muted flex-grow-1">تقديم طلب جديد عبر النموذج المعتاد</p>
                                    <a asp-action="NewOrder" class="btn btn-primary mt-auto">فتح نموذج الطلب</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body d-flex flex-column">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="icon-circle bg-success bg-opacity-10 me-3">
                                            <i class="fas fa-award text-success"></i>
                                        </div>
                                        <h5 class="mb-0">شهادة لمن يهمه الأمر / Certificate</h5>
                                    </div>
                                    <p class="text-muted flex-grow-1">إنشاء شهادة لمن يهمه الأمر بإدخال بيانات الموظف وسبب الشهادة</p>
                                    <a asp-controller="Certificate" asp-action="New" class="btn btn-success mt-auto">فتح نموذج الشهادة</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<style>
    .icon-circle {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>



