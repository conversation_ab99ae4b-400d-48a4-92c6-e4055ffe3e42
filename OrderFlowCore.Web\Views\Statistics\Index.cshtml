@using OrderFlowCore.Application.DTOs
@using System.Linq

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الإحصائيات - نظام إخلاء الطرف</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --success-color: #43cea2;
            --info-color: #20a2ff;
            --danger-color: #ff6b6b;
            --dark-color: #2c3e50;
            --light-color: #f8f9fa;
            --indigo-color: #6610f2;
            --pink-color: #e83e8c;
            --red-color: #e74c3c;
            --orange-color: #fd7e14;
            --yellow-color: #ffc107;
            --green-color: #28a745;
            --teal-color: #20c997;
            --cyan-color: #17a2b8;
            --blue-color: #007bff;
            --navy-color: #1e3a8a;
            --maroon-color: #800020;
            --lime-color: #32cd32;
            --olive-color: #808000;
            --brown-color: #8b4513;
            --gray-color: #6c757d;
            --black-color: #000000;
            --white-color: #ffffff;
            /* ألوان خاصة للحالات المختلفة */
            --urgent-color: #dc3545; /* أحمر للحالات العاجلة */
            --critical-color: #fd7e14; /* برتقالي للحالات الحرجة */
            --moderate-color: #ffc107; /* أصفر للحالات المتوسطة */
            --normal-color: #28a745; /* أخضر للحالات العادية */
            --excellent-color: #6f42c1; /* بنفسجي للمتميزين */
            /* Gradients محسنة */
            --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --gradient-success: linear-gradient(135deg, #43cea2 0%, #185a9d 100%);
            --gradient-info: linear-gradient(135deg, #20a2ff 0%, #667eea 100%);
            --gradient-warning: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --gradient-danger: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            --gradient-secondary: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            --gradient-dark: linear-gradient(135deg, #343a40 0%, #23272b 100%);
            --gradient-purple: linear-gradient(135deg, #6f42c1 0%, #5a32a3 100%);
            --gradient-indigo: linear-gradient(135deg, #6610f2 0%, #520dc2 100%);
            --gradient-pink: linear-gradient(135deg, #e83e8c 0%, #d91a72 100%);
            --gradient-orange: linear-gradient(135deg, #fd7e14 0%, #e55a12 100%);
            --gradient-yellow: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
            --gradient-teal: linear-gradient(135deg, #20c997 0%, #1abc9c 100%);
            --gradient-cyan: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            --gradient-navy: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
            /* Shadows محسنة */
            --box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            --box-shadow-hover: 0 20px 40px rgba(0,0,0,0.15);
            --box-shadow-card: 0 4px 15px rgba(0,0,0,0.08);
            --box-shadow-strong: 0 15px 35px rgba(0,0,0,0.15);
            /* متغيرات جديدة */
            --border-radius: 15px;
            --border-radius-large: 20px;
            --border-radius-small: 10px;
            --transition-base: all 0.3s ease;
            --spacing-base: 1rem;
            --spacing-large: 2rem;
            /* ألوان للخلفيات الفاتحة */
            --light-red: #ffebee;
            --light-orange: #fff3e0;
            --light-yellow: #fffde7;
            --light-green: #e8f5e8;
            --light-blue: #e3f2fd;
            --light-purple: #f3e5f5;
            --light-gray: #f5f5f5;
            /* ألوان للنصوص */
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --text-success: #155724;
            --text-danger: #721c24;
            --text-warning: #856404;
            --text-info: #0c5460;
            --text-light: #6c757d;
            --text-dark: #1d1e22;
            /* ألوان للحدود */
            --border-primary: #667eea;
            --border-secondary: #6c757d;
            --border-success: #28a745;
            --border-danger: #dc3545;
            --border-warning: #fd7e14;
            --border-info: #17a2b8;
            --border-light: #dee2e6;
            --border-dark: #2c3e50;
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
            margin: 0;
            padding: 0;
            line-height: 1.6;
            color: var(--dark-color);
        }

        /* تصميم رأس الصفحة المحسن */
        .page-header-enhanced {
            background: var(--gradient-primary);
            border-radius: var(--border-radius-large);
            box-shadow: var(--box-shadow);
            margin-bottom: var(--spacing-large);
            overflow: hidden;
            position: relative;
        }

            .page-header-enhanced::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
                opacity: 0.1;
                pointer-events: none;
            }

            .page-header-enhanced .card-body {
                position: relative;
                z-index: 2;
                padding: 3rem 2rem;
            }

            .page-header-enhanced h1 {
                font-size: 2.5rem;
                font-weight: 700;
                text-shadow: 0 2px 4px rgba(0,0,0,0.1);
                margin-bottom: 0.5rem;
                margin-top: 0;
            }

            .page-header-enhanced .lead {
                font-size: 1.2rem;
                opacity: 0.9;
                text-shadow: 0 1px 2px rgba(0,0,0,0.1);
                margin-bottom: 0;
            }

        /* بطاقات الإحصائيات المحسنة */
        .stat-card-enhanced {
            border-radius: var(--border-radius-large);
            border: none;
            box-shadow: var(--box-shadow);
            transition: var(--transition-base);
            overflow: hidden;
            position: relative;
            height: 200px;
            cursor: pointer;
            will-change: transform;
        }

            .stat-card-enhanced:hover {
                transform: translateY(-10px) scale(1.02);
                box-shadow: var(--box-shadow-hover);
            }

            .stat-card-enhanced::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="85" cy="15" r="15" fill="white" opacity="0.1"/><circle cx="15" cy="85" r="10" fill="white" opacity="0.05"/></svg>');
                pointer-events: none;
            }

            .stat-card-enhanced .card-body {
                position: relative;
                z-index: 2;
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: center;
                text-align: center;
                height: 100%;
                padding: 1.5rem;
            }

        .stat-icon-enhanced {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.9;
            transition: var(--transition-base);
        }

        .stat-number-enhanced {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            line-height: 1;
        }

        .stat-label-enhanced {
            font-size: 1rem;
            font-weight: 500;
            opacity: 0.9;
            margin-bottom: 0;
        }

        .stat-subtitle-enhanced {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 0.25rem;
            margin-bottom: 0;
        }

        /* ألوان البطاقات */
        .stat-card-primary {
            background: var(--gradient-primary);
            color: white;
        }

        .stat-card-success {
            background: var(--gradient-success);
            color: white;
        }

        .stat-card-info {
            background: var(--gradient-info);
            color: white;
        }

        .stat-card-warning {
            background: var(--gradient-warning);
            color: white;
        }

        .stat-card-danger {
            background: var(--gradient-danger);
            color: white;
        }

        .stat-card-secondary {
            background: var(--gradient-secondary);
            color: white;
        }

        .stat-card-dark {
            background: var(--gradient-dark);
            color: white;
        }

        /* التبويبات المحسنة */
        .nav-tabs-enhanced {
            border: none;
            background: white;
            border-radius: var(--border-radius);
            padding: 0.5rem;
            box-shadow: var(--box-shadow);
            margin-bottom: var(--spacing-large);
            display: flex;
            flex-wrap: wrap;
        }

            .nav-tabs-enhanced .nav-link {
                border: none;
                border-radius: var(--border-radius-small);
                padding: 1rem 1.5rem;
                margin: 0 0.25rem;
                color: var(--dark-color);
                font-weight: 500;
                transition: var(--transition-base);
                position: relative;
                overflow: hidden;
                white-space: nowrap;
                text-decoration: none;
                cursor: pointer;
            }

                .nav-tabs-enhanced .nav-link::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: -100%;
                    width: 100%;
                    height: 100%;
                    background: var(--gradient-primary);
                    transition: left 0.3s ease;
                    z-index: -1;
                }

                .nav-tabs-enhanced .nav-link.active,
                .nav-tabs-enhanced .nav-link:hover {
                    color: white;
                    background: var(--gradient-primary);
                }

                    .nav-tabs-enhanced .nav-link.active::before,
                    .nav-tabs-enhanced .nav-link:hover::before {
                        left: 0;
                    }

        /* بطاقات المحتوى المحسنة */
        .content-card-enhanced {
            border-radius: var(--border-radius-large);
            border: none;
            box-shadow: var(--box-shadow);
            background: white;
            overflow: hidden;
            margin-bottom: var(--spacing-large);
        }

            .content-card-enhanced .card-header {
                background: var(--gradient-primary);
                border: none;
                padding: 1.5rem 2rem;
                margin-bottom: 0;
            }

            .content-card-enhanced .card-body {
                padding: 2rem;
            }

        /* مراحل المعالجة المحسنة */
        .stage-item-enhanced {
            background: white;
            border-radius: var(--border-radius);
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: var(--box-shadow-card);
            transition: var(--transition-base);
            border-left: 5px solid transparent;
            cursor: pointer;
            will-change: transform;
        }

            .stage-item-enhanced:hover {
                transform: translateX(-5px);
                box-shadow: var(--box-shadow-strong);
            }

            /* ألوان مراحل المعالجة */
            .stage-item-enhanced.stage-primary {
                border-left-color: var(--primary-color);
            }

            .stage-item-enhanced.stage-success {
                border-left-color: var(--success-color);
            }

            .stage-item-enhanced.stage-info {
                border-left-color: var(--info-color);
            }

            .stage-item-enhanced.stage-warning {
                border-left-color: var(--warning-color);
            }

            .stage-item-enhanced.stage-danger {
                border-left-color: var(--danger-color);
            }

            .stage-item-enhanced.stage-secondary {
                border-left-color: #6c757d;
            }

            .stage-item-enhanced.stage-dark {
                border-left-color: var(--dark-color);
            }

        /* أيقونات المراحل */
        .stage-icon-enhanced {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-left: 1rem;
            transition: var(--transition-base);
            flex-shrink: 0;
        }

            .stage-icon-enhanced:hover {
                transform: scale(1.1) rotate(5deg);
            }

        /* ألوان أيقونات المراحل */
        .stage-icon-primary {
            background: var(--gradient-primary);
        }

        .stage-icon-success {
            background: var(--gradient-success);
        }

        .stage-icon-info {
            background: var(--gradient-info);
        }

        .stage-icon-warning {
            background: var(--gradient-warning);
        }

        .stage-icon-danger {
            background: var(--gradient-danger);
        }

        .stage-icon-secondary {
            background: var(--gradient-secondary);
        }

        .stage-icon-dark {
            background: var(--gradient-dark);
        }

        /* شريط التقدم المحسن */
        .progress-enhanced {
            height: 8px;
            border-radius: var(--border-radius-small);
            background: #e9ecef;
            overflow: hidden;
            margin-top: 0.5rem;
        }

            .progress-enhanced .progress-bar {
                border-radius: var(--border-radius-small);
                transition: width 1s ease-in-out;
                position: relative;
            }

                .progress-enhanced .progress-bar::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.2) 50%, transparent 100%);
                    animation: shimmer 2s infinite;
                }



        .btn-primary-enhanced {
            background: var(--gradient-primary);
            color: white;
        }

        .btn-success-enhanced {
            background: var(--gradient-success);
            color: white;
        }

        /* رسوم بيانية مصغرة */
        .mini-chart {
            width: 80px;
            height: 40px;
            margin: 0.5rem 0;
            opacity: 0.8;
        }

        /* تأثيرات الحركة */
        .fade-in-up {
            opacity: 0;
            transform: translateY(30px);
            animation: fadeInUp 0.6s ease forwards;
        }

        /* ✅ تأخيرات الحركة */
        .delay-1 {
            animation-delay: 0.1s;
        }

        .delay-2 {
            animation-delay: 0.2s;
        }

        .delay-3 {
            animation-delay: 0.3s;
        }

        .delay-4 {
            animation-delay: 0.4s;
        }

        .delay-5 {
            animation-delay: 0.5s;
        }

        /* مؤشرات الإشعارات */
        .notification-badge {
            position: absolute;
            top: -8px;
            right: -8px;
            background: var(--danger-color);
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: 600;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            z-index: 10;
        }

        /* Grid Layout للأعمدة المخصصة */
        .col-md-2-4 {
            flex: 0 0 auto;
            width: 20%;
            padding-left: 0.75rem;
            padding-right: 0.75rem;
        }

        /* إحصائيات صغيرة محسنة */
        .stat-mini {
            padding: 0.75rem;
            border-radius: var(--border-radius-small);
            background: rgba(255,255,255,0.15);
            margin: 0.25rem;
            text-align: center;
            transition: var(--transition-base);
            backdrop-filter: blur(10px);
        }

            .stat-mini:hover {
                background: rgba(255,255,255,0.25);
                transform: translateY(-2px);
            }

        .stat-mini-number {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
            line-height: 1;
        }

        .stat-mini-label {
            font-size: 0.8rem;
            opacity: 0.9;
            margin-bottom: 0;
        }

        /* تحسينات إضافية للتفاعل */
        .stage-item-enhanced:hover .stage-icon-enhanced {
            transform: scale(1.1) rotate(5deg);
        }

        .stage-item-enhanced:hover {
            box-shadow: var(--box-shadow-strong);
        }

        .stat-card-enhanced:hover .stat-icon-enhanced {
            transform: scale(1.1);
            animation: pulse 2s infinite;
        }

        /* حالات التحميل */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: rotate 1s linear infinite;
        }

        /* تحسينات إمكانية الوصول */
        .stat-card-enhanced:focus,
        .stage-item-enhanced:focus,
        .btn-enhanced:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        /* حالات الخطأ والنجاح */
        .alert-enhanced {
            border-radius: var(--border-radius);
            border: none;
            box-shadow: var(--box-shadow-card);
            padding: 1rem 1.5rem;
            margin-bottom: 1rem;
        }

        .alert-success-enhanced {
            background: var(--gradient-success);
            color: white;
        }

        .alert-danger-enhanced {
            background: var(--gradient-danger);
            color: white;
        }

        .alert-warning-enhanced {
            background: var(--gradient-warning);
            color: white;
        }

        .alert-info-enhanced {
            background: var(--gradient-info);
            color: white;
        }

        /* ✅ استخدام @@keyframes بدلاً من  لتجنب خطأ Razor */
        @@keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @@keyframes shimmer {
            0% {
                transform: translateX(-100%);
            }

            100% {
                transform: translateX(100%);
            }
        }

        @@keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.05);
            }
        }

        @@keyframes rotate {
            from {
                transform: rotate(0deg);
            }

            to {
                transform: rotate(360deg);
            }
        }

        /* ✅ تحسينات الاستجابة - استخدام @@media */
        @@media (max-width: 1200px) {
            .col-md-2-4 {
                width: 25%;
            }
        }

        @@media (max-width: 992px) {
            .col-md-2-4 {
                width: 33.333333%;
            }

            .nav-tabs-enhanced .nav-link {
                padding: 0.75rem 1rem;
                font-size: 0.9rem;
            }
        }

        @@media (max-width: 768px) {
            .page-header-enhanced h1 {
                font-size: 2rem;
            }

            .page-header-enhanced .card-body {
                padding: 2rem 1rem;
            }

            .stat-card-enhanced {
                height: 150px;
                margin-bottom: 1rem;
            }

            .stat-number-enhanced {
                font-size: 2rem;
            }

            .stat-icon-enhanced {
                font-size: 2.5rem;
            }

            .nav-tabs-enhanced {
                flex-direction: column;
            }

                .nav-tabs-enhanced .nav-link {
                    margin: 0.25rem 0;
                    text-align: center;
                }

            .col-md-2-4 {
                width: 50%;
                margin-bottom: 1rem;
            }

            .stage-item-enhanced {
                padding: 1rem;
            }

            .stage-icon-enhanced {
                width: 40px;
                height: 40px;
                font-size: 1.2rem;
            }

            .content-card-enhanced .card-body {
                padding: 1rem;
            }
        }

        @@media (max-width: 576px) {
            .col-md-2-4 {
                width: 100%;
            }

            .stat-card-enhanced {
                height: 120px;
            }

            .stat-number-enhanced {
                font-size: 1.5rem;
            }

            .stat-icon-enhanced {
                font-size: 2rem;
                margin-bottom: 0.5rem;
            }

            .stage-item-enhanced:hover {
                transform: translateX(0);
            }

            .stat-card-enhanced:hover {
                transform: translateY(-5px);
            }
        }

        /* تحسينات الطباعة */
        @@media print {
            .stat-card-enhanced,
            .content-card-enhanced {
                box-shadow: none;
                border: 1px solid #ddd;
            }

            .page-header-enhanced {
                background: #f8f9fa !important;
                color: #000 !important;
            }
        }


        /* تحسينات الأداء */
        * {
            box-sizing: border-box;
        }

        .stat-card-enhanced,
        .stage-item-enhanced,
        .btn-enhanced {
            will-change: transform;
        }

        /* علامات ميدالية  منسق الموارد */
        .badge.fs-5 {
            font-size: 1.25rem !important;
        }

        .badge.fs-6 {
            font-size: 1rem !important;
        }

        .hr-coordinator-card:hover {
            transform: translateX(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .stat-card-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .stat-mini-label {
            font-size: 14px;
            display: inline-block;
            white-space: nowrap; /* يمنع النص من النزول لسطر جديد */
        }
        /* تحسين مظهر البطاقات عند تمرير الماوس */
        .stat-card-enhanced[data-bs-toggle="tooltip"] {
            cursor: help;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            position: relative;
        }

            .stat-card-enhanced[data-bs-toggle="tooltip"]:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            }

        /* تحسين مظهر الـ tooltip */
        .tooltip {
            font-size: 12px;
        }

        .tooltip-inner {
            max-width: 300px;
            text-align: right;
            background-color: #2c3e50;
            border-radius: 8px;
            padding: 10px;
            line-height: 1.4;
        }

        /* إضافة أيقونة صغيرة للبطاقات التي لها شرح */
        .stat-card-enhanced[data-bs-toggle="tooltip"]::after {
            content: "ℹ️";
            position: absolute;
            top: 8px;
            right: 8px;
            font-size: 12px;
            opacity: 0.7;
            transition: opacity 0.2s ease;
        }

        .stat-card-enhanced[data-bs-toggle="tooltip"]:hover::after {
            opacity: 1;
        }
        /* إ التصنيف الشامل للمشرفين */
        .supervisor-card {
            transition: all 0.3s ease;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

            .supervisor-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 16px rgba(0,0,0,0.15);
            }

        .performance-bar {
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
        }

        .filter-btn.active {
            background: linear-gradient(135deg, #007bff, #0056b3);
            transform: scale(1.05);
        }

        .stats-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            border: none;
        }

        /* Tooltip للعناوين في الجدول */
        /* Tooltip أنيق واحترافي */
        .tooltip-header {
            cursor: help;
            position: relative;
            transition: background-color 0.2s ease-in-out;
        }

            .tooltip-header:hover::after {
                content: attr(data-tooltip);
                position: absolute;
                top: 110%;
                left: 50%;
                transform: translateX(-50%) scale(0.95);
                background: linear-gradient(135deg, #2c3e50, #34495e);
                color: #fff;
                padding: 12px 16px;
                border-radius: 10px;
                font-size: 13px;
                line-height: 1.6;
                white-space: pre-line; /* يدعم الأسطر المتعددة */
                z-index: 1000;
                box-shadow: 0 6px 18px rgba(0,0,0,0.3);
                max-width: 320px;
                text-align: center;
                opacity: 0;
                pointer-events: none;
                transition: opacity 0.25s ease, transform 0.25s ease;
            }

            .tooltip-header:hover::after {
                opacity: 1;
                transform: translateX(-50%) scale(1);
            }

            /* السهم */
            .tooltip-header:hover::before {
                content: '';
                position: absolute;
                top: 100%;
                left: 50%;
                transform: translateX(-50%);
                border: 7px solid transparent;
                border-top-color: #34495e;
                z-index: 1001;
            }

            /* تأثير بسيط عند تمرير */
            .tooltip-header:hover {
                background-color: rgba(255,255,255,0.07);
                border-radius: 6px;
            }

    </style>
</head>
<body>



    <!-- العنوان -->
    <div class="col-md-12">
        <!-- بدلاً من col-md-4 -->
        <h1>
            <i class="fas fa-chart-bar me-3"></i>
            الإحصائيات
        </h1>
        <p class="lead mb-0">
            إحصائيات شاملة لجميع الطلبات والمراحل
        </p>
    </div>

    <!-- البطاقات الإحصائية بجانب العنوان -->
    <!-- التبويبات -->
    <ul class="nav nav-tabs nav-tabs-enhanced fade-in-up" id="statisticsTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="stages-tab" data-bs-toggle="tab" data-bs-target="#stageStats" type="button" role="tab">
                <i class="fas fa-layer-group me-2"></i>المراحل والتحويل
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="departments-tab" data-bs-toggle="tab" data-bs-target="#departmentStats" type="button" role="tab">
                <i class="fas fa-building me-2"></i>الأقسام
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="assistants-tab" data-bs-toggle="tab" data-bs-target="#assistantManagers" type="button" role="tab">
                <i class="fas fa-user-tie me-2"></i>المُسَاعِدُون
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="hr-coordinators-tab" data-bs-toggle="tab" data-bs-target="#hrCoordinators" type="button" role="tab">
                <i class="fas fa-users-cog me-2"></i>المُنَسْقُون
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="supervisors-tab" data-bs-toggle="tab" data-bs-target="#supervisorStats" type="button" role="tab">
                <i class="fas fa-user-check me-2"></i>المُشْرِفُون
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="hr-managers-tab" data-bs-toggle="tab" data-bs-target="#hrManagers" type="button" role="tab">
                <i class="fas fa-user-shield me-2"></i>مدير الموارد البشرية
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="completed-supervisors-tab" data-bs-toggle="tab" data-bs-target="#completedSupervisors" type="button" role="tab">
                <i class="fas fa-award me-2"></i>الطلبات المنجزة للمشرفين
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="pending-orders-tab" data-bs-toggle="tab" data-bs-target="#pendingOrders" type="button" role="tab">
                <i class="fas fa-clock text-warning me-2"></i>الطلبات المعلقة والمنجزة
            </button>
        </li>
    </ul>

    <!-- محتوى التبويبات -->
    <div class="tab-content fade-in-up">

        <!-- تبويب مراحل المعالجة والتحويل المدموج -->
        <div class="tab-pane fade show active" id="stageStats" role="tabpanel">

            <!-- البطاقات الإحصائية الأساسية - داخل التبويب -->
            <div class="card content-card-enhanced mb-4">
                <div class="card-header text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>
                        الإحصائيات العامة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <!-- بطاقة إجمالي الطلبات -->
                        <div class="col-6 col-lg-3">
                            <div class="card stat-card-enhanced stat-card-primary text-white fade-in-up delay-1">
                                <div class="card-body p-3">
                                    <div class="stat-icon-enhanced"><i class="fas fa-clipboard-list"></i></div>
                                    <div class="stat-number-enhanced" data-animate="counter">@Model.GeneralStatistics.TotalRequests</div>
                                    <div class="stat-label-enhanced">إجمالي الطلبات</div>
                                    <div class="stat-subtitle-enhanced">
                                        @if (Model.GeneralStatistics.CancelledRequests > 0)
                                        {
                                            <text>ملغي: @Model.GeneralStatistics.CancelledRequests</text>
                                        }
                                        else
                                        {
                                            <text>لا توجد طلبات ملغاة</text>
                                        }
                                    </div>
                                    <canvas class="mini-chart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- بطاقة المنجزة -->
                        <div class="col-6 col-lg-3">
                            <div class="card stat-card-enhanced stat-card-success text-white fade-in-up delay-2">
                                <div class="card-body p-3">
                                    <div class="stat-icon-enhanced"><i class="fas fa-check-circle"></i></div>
                                    <div class="stat-number-enhanced" data-animate="counter">@Model.GeneralStatistics.CompletedRequests</div>
                                    <div class="stat-label-enhanced">منجز</div>
                                    <div class="stat-subtitle-enhanced">
                                        @if (Model.GeneralStatistics.TotalRequests > 0)
                                        {
                                            @Math.Round((double)Model.GeneralStatistics.CompletedRequests / Model.GeneralStatistics.TotalRequests * 100, 1)
                                            <text>% معدل الإنجاز</text>
                                        }
                                    </div>
                                    <canvas class="mini-chart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- بطاقة تحت التنفيذ -->
                        <div class="col-6 col-lg-3">
                            <div class="card stat-card-enhanced stat-card-warning text-white fade-in-up delay-3">
                                <div class="card-body p-3">
                                    <div class="stat-icon-enhanced">
                                        <i class="fas fa-clock"></i>
                                        @if (Model.GeneralStatistics.PendingRequests > 50)
                                        {
                                            <span class="notification-badge">!</span>
                                        }
                                    </div>
                                    <div class="stat-number-enhanced" data-animate="counter">@Model.GeneralStatistics.PendingRequests</div>
                                    <div class="stat-label-enhanced">تحت التنفيذ</div>
                                    <div class="stat-subtitle-enhanced">
                                        @if (Model.GeneralStatistics.TotalRequests > 0)
                                        {
                                            @Math.Round((double)Model.GeneralStatistics.PendingRequests / Model.GeneralStatistics.TotalRequests * 100, 1)
                                            <text>% من الإجمالي</text>
                                        }
                                    </div>
                                    <canvas class="mini-chart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- بطاقة متوسط الزمن -->
                        <div class="col-6 col-lg-3">
                            <div class="card stat-card-enhanced stat-card-info text-white fade-in-up delay-4">
                                <div class="card-body p-3">
                                    <div class="stat-icon-enhanced"><i class="fas fa-business-time"></i></div>
                                    <div class="stat-number-enhanced" data-animate="decimal">@Model.GeneralStatistics.AverageTimeToComplete.ToString("F1")</div>
                                    <div class="stat-label-enhanced">متوسط زمن الإنجاز</div>
                                    <div class="stat-subtitle-enhanced">أيام عمل</div>
                                    <canvas class="mini-chart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الجزء الأول: مراحل المعالجة -->
            <div class="card content-card-enhanced mb-4">
                <div class="card-header text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-layer-group me-2"></i>
                        توزيع الطلبات حسب مراحل المعالجة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8">
                            <!-- مراحل المعالجة -->
                            <div class="stage-item-enhanced stage-secondary">
                                <div class="d-flex align-items-center">
                                    <div class="stage-icon-enhanced stage-icon-secondary">
                                        <i class="fas fa-user-tie"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">مدير القسم</h6>
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="text-muted">@Model.StageStatistics.DepartmentManager طلب</span>
                                            <span class="badge bg-secondary">@(Model.GeneralStatistics.TotalRequests > 0 ? Math.Round((double)Model.StageStatistics.DepartmentManager / Model.GeneralStatistics.TotalRequests * 100, 1) : 0)%</span>
                                        </div>
                                        <div class="progress progress-enhanced">
                                            <div class="progress-bar bg-secondary" style="width: @(Model.GeneralStatistics.TotalRequests > 0 ? (double)Model.StageStatistics.DepartmentManager / Model.GeneralStatistics.TotalRequests * 100 : 0)%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="stage-item-enhanced stage-primary">
                                <div class="d-flex align-items-center">
                                    <div class="stage-icon-enhanced stage-icon-primary">
                                        <i class="fas fa-user-md"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">مساعد المدير للخدمات الطبية (A1)</h6>
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="text-muted">@Model.StageStatistics.AssistantManagerA1 طلب</span>
                                            <span class="badge bg-primary">@(Model.GeneralStatistics.TotalRequests > 0 ? Math.Round((double)Model.StageStatistics.AssistantManagerA1 / Model.GeneralStatistics.TotalRequests * 100, 1) : 0)%</span>
                                        </div>
                                        <div class="progress progress-enhanced">
                                            <div class="progress-bar bg-primary" style="width: @(Model.GeneralStatistics.TotalRequests > 0 ? (double)Model.StageStatistics.AssistantManagerA1 / Model.GeneralStatistics.TotalRequests * 100 : 0)%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="stage-item-enhanced stage-info">
                                <div class="d-flex align-items-center">
                                    <div class="stage-icon-enhanced stage-icon-info">
                                        <i class="fas fa-user-nurse"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">مساعد المدير لخدمات التمريض (A2)</h6>
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="text-muted">@Model.StageStatistics.AssistantManagerA2 طلب</span>
                                            <span class="badge bg-info">@(Model.GeneralStatistics.TotalRequests > 0 ? Math.Round((double)Model.StageStatistics.AssistantManagerA2 / Model.GeneralStatistics.TotalRequests * 100, 1) : 0)%</span>
                                        </div>
                                        <div class="progress progress-enhanced">
                                            <div class="progress-bar bg-info" style="width: @(Model.GeneralStatistics.TotalRequests > 0 ? (double)Model.StageStatistics.AssistantManagerA2 / Model.GeneralStatistics.TotalRequests * 100 : 0)%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="stage-item-enhanced stage-warning">
                                <div class="d-flex align-items-center">
                                    <div class="stage-icon-enhanced stage-icon-warning">
                                        <i class="fas fa-cogs"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">مساعد المدير للخدمات الإدارية (A3)</h6>
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="text-muted">@Model.StageStatistics.AssistantManagerA3 طلب</span>
                                            <span class="badge bg-warning">@(Model.GeneralStatistics.TotalRequests > 0 ? Math.Round((double)Model.StageStatistics.AssistantManagerA3 / Model.GeneralStatistics.TotalRequests * 100, 1) : 0)%</span>
                                        </div>
                                        <div class="progress progress-enhanced">
                                            <div class="progress-bar bg-warning" style="width: @(Model.GeneralStatistics.TotalRequests > 0 ? (double)Model.StageStatistics.AssistantManagerA3 / Model.GeneralStatistics.TotalRequests * 100 : 0)%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="stage-item-enhanced stage-success">
                                <div class="d-flex align-items-center">
                                    <div class="stage-icon-enhanced stage-icon-success">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">مساعد المدير للموارد البشرية (A4)</h6>
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="text-muted">@Model.StageStatistics.AssistantManagerA4 طلب</span>
                                            <span class="badge bg-success">@(Model.GeneralStatistics.TotalRequests > 0 ? Math.Round((double)Model.StageStatistics.AssistantManagerA4 / Model.GeneralStatistics.TotalRequests * 100, 1) : 0)%</span>
                                        </div>
                                        <div class="progress progress-enhanced">
                                            <div class="progress-bar bg-success" style="width: @(Model.GeneralStatistics.TotalRequests > 0 ? (double)Model.StageStatistics.AssistantManagerA4 / Model.GeneralStatistics.TotalRequests * 100 : 0)%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="stage-item-enhanced stage-danger">
                                <div class="d-flex align-items-center">
                                    <div class="stage-icon-enhanced stage-icon-danger">
                                        <i class="fas fa-user-cog"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">منسق الموارد البشرية</h6>
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="text-muted">@Model.StageStatistics.HRCoordinator طلب</span>
                                            <span class="badge bg-danger">@(Model.GeneralStatistics.TotalRequests > 0 ? Math.Round((double)Model.StageStatistics.HRCoordinator / Model.GeneralStatistics.TotalRequests * 100, 1) : 0)%</span>
                                        </div>
                                        <div class="progress progress-enhanced">
                                            <div class="progress-bar bg-danger" style="width: @(Model.GeneralStatistics.TotalRequests > 0 ? (double)Model.StageStatistics.HRCoordinator / Model.GeneralStatistics.TotalRequests * 100 : 0)%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="stage-item-enhanced stage-dark">
                                <div class="d-flex align-items-center">
                                    <div class="stage-icon-enhanced stage-icon-dark">
                                        <i class="fas fa-user-check"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">المشرفون</h6>
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="text-muted">@Model.StageStatistics.Supervisors طلب</span>
                                            <span class="badge bg-dark">@(Model.GeneralStatistics.TotalRequests > 0 ? Math.Round((double)Model.StageStatistics.Supervisors / Model.GeneralStatistics.TotalRequests * 100, 1) : 0)%</span>
                                        </div>
                                        <div class="progress progress-enhanced">
                                            <div class="progress-bar bg-dark" style="width: @(Model.GeneralStatistics.TotalRequests > 0 ? (double)Model.StageStatistics.Supervisors / Model.GeneralStatistics.TotalRequests * 100 : 0)%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="stage-item-enhanced stage-primary">
                                <div class="d-flex align-items-center">
                                    <div class="stage-icon-enhanced stage-icon-primary">
                                        <i class="fas fa-user-shield"></i>
                                    </div>
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">مدير الموارد البشرية</h6>
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <span class="text-muted">@Model.StageStatistics.HRManager طلب</span>
                                            <span class="badge bg-primary">@(Model.GeneralStatistics.TotalRequests > 0 ? Math.Round((double)Model.StageStatistics.HRManager / Model.GeneralStatistics.TotalRequests * 100, 1) : 0)%</span>
                                        </div>
                                        <div class="progress progress-enhanced">
                                            <div class="progress-bar bg-primary" style="width: @(Model.GeneralStatistics.TotalRequests > 0 ? (double)Model.StageStatistics.HRManager / Model.GeneralStatistics.TotalRequests * 100 : 0)%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="text-center">
                                <canvas id="stageChart" width="300" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الجزء الثاني: أنواع التحويل - داخل نفس التبويب -->
            <div class="card content-card-enhanced">
                <div class="card-header text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-exchange-alt me-2"></i>
                            إحصائيات أنواع التحويل
                        </h5>
                    </div>
                </div>
                <div class="card-body">
                    @if (Model.TransferTypeStatistics != null && Model.TransferTypeStatistics.Count > 0)
                    {
                        var transferStats = Model.TransferTypeStatistics;

                        // حساب الإحصائيات العامة
                        int transferCount = transferStats.Count;
                        int transferTotalCount = 0;
                        int transferTotalCompleted = 0;
                        int transferTotalPending = 0;
                        int transferTotalCancelled = 0;
                        int transferMaxCount = 0;

                        foreach (var transfer in transferStats)
                        {
                            transferTotalCount += transfer.Count;
                            transferTotalCompleted += transfer.CompletedOrders;
                            transferTotalPending += transfer.PendingOrders;
                            transferTotalCancelled += transfer.CancelledOrders;

                            if (transfer.Count > transferMaxCount)
                            {
                                transferMaxCount = transfer.Count;
                            }
                        }

                        double overallSuccessRate = transferTotalCount > 0 ? (double)transferTotalCompleted / transferTotalCount * 100 : 0;

                        <!-- ملخص سريع للتحويل -->
                        <div class="row mb-4">
                            <div class="col-md-2 mb-3">
                                <div class="card stat-card-enhanced stat-card-primary text-white h-100">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-list-alt"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@transferCount</div>
                                        <div class="stat-label-enhanced">عدد الأنواع</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 mb-3">
                                <div class="card stat-card-enhanced stat-card-info text-white h-100">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-exchange-alt"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@transferTotalCount</div>
                                        <div class="stat-label-enhanced">إجمالي التحويلات</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 mb-3">
                                <div class="card stat-card-enhanced stat-card-success text-white h-100">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-check-circle"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@transferTotalCompleted</div>
                                        <div class="stat-label-enhanced">مكتملة</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 mb-3">
                                <div class="card stat-card-enhanced stat-card-warning text-white h-100">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-hourglass-half"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@transferTotalPending</div>
                                        <div class="stat-label-enhanced">معلقة</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 mb-3">
                                <div class="card stat-card-enhanced stat-card-danger text-white h-100">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-times-circle"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@transferTotalCancelled</div>
                                        <div class="stat-label-enhanced">ملغية</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 mb-3">
                                <div class="card stat-card-enhanced stat-card-gradient text-white h-100">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@overallSuccessRate.ToString("F1")%</div>
                                        <div class="stat-label-enhanced">نسبة النجاح</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- جدول أنواع التحويل المدموج -->
                        <div class="row">
                            @{
                                // ترتيب يدوي بدون lambda expression
                                var sortedTransfers = new List<dynamic>();

                                // إنشاء قائمة مؤقتة للترتيب
                                var tempList = new List<(dynamic transfer, int count)>();
                                foreach (var transfer in transferStats)
                                {
                                    tempList.Add((transfer, (int)transfer.Count));
                                }

                                // ترتيب يدوي حسب العدد (من الأكبر للأصغر)
                                for (int i = 0; i < tempList.Count - 1; i++)
                                {
                                    for (int j = i + 1; j < tempList.Count; j++)
                                    {
                                        if (tempList[j].count > tempList[i].count)
                                        {
                                            var temp = tempList[i];
                                            tempList[i] = tempList[j];
                                            tempList[j] = temp;
                                        }
                                    }
                                }

                                // تحويل للقائمة النهائية
                                foreach (var item in tempList)
                                {
                                    sortedTransfers.Add(item.transfer);
                                }

                                int rankIndex = 1;
                            }

                            @foreach (var transfer in sortedTransfers)
                            {
                                var cardClass = rankIndex <= 3 ? "border-primary" : "";
                                var successRate = (int)transfer.Count > 0 ? (double)(int)transfer.CompletedOrders / (int)transfer.Count * 100 : 0;

                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card h-100 @cardClass">
                                        <div class="card-body p-3">
                                            <div class="row align-items-center">
                                                <div class="col-8">
                                                    <h6 class="mb-1 fw-bold">@transfer.TransferType</h6>
                                                    <small class="text-muted">@transfer.Count تحويل (@transfer.PercentageDisplay)</small>
                                                </div>
                                                <div class="col-4 text-center">
                                                    @if (rankIndex == 1)
                                                    {
                                                        <span class="badge bg-warning fs-6">🥇 @rankIndex</span>
                                                    }
                                                    else if (rankIndex == 2)
                                                    {
                                                        <span class="badge bg-secondary fs-6">🥈 @rankIndex</span>
                                                    }
                                                    else if (rankIndex == 3)
                                                    {
                                                        <span class="badge bg-info fs-6">🥉 @rankIndex</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-dark fs-6">@rankIndex</span>
                                                    }
                                                </div>
                                            </div>

                                            @if ((int)transfer.Count > 0)
                                            {
                                                <!-- شريط تقدم مبسط -->
                                                <div class="progress mt-2" style="height: 6px;">
                                                    <div class="progress-bar bg-success"
                                                         style="width: @((double)(int)transfer.CompletedOrders / (int)transfer.Count * 100)%"
                                                         title="مكتملة: @transfer.CompletedOrders"></div>
                                                    <div class="progress-bar bg-warning"
                                                         style="width: @((double)(int)transfer.PendingOrders / (int)transfer.Count * 100)%"
                                                         title="معلقة: @transfer.PendingOrders"></div>
                                                    <div class="progress-bar bg-danger"
                                                         style="width: @((double)(int)transfer.CancelledOrders / (int)transfer.Count * 100)%"
                                                         title="ملغية: @transfer.CancelledOrders"></div>
                                                </div>

                                                <!-- معدل النجاح -->
                                                <div class="mt-2 d-flex justify-content-between">
                                                    <small class="text-muted">معدل النجاح:</small>
                                                    <span class="badge @(successRate >= 80 ? "bg-success" : successRate >= 60 ? "bg-warning" : "bg-danger")">
                                                        @successRate.ToString("F1")%
                                                    </span>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </div>
                                rankIndex++;
                            }
                        </div>

                        <!-- الرسم البياني -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="text-center">
                                    <h6 class="mb-3">الرسم البياني لأنواع التحويل</h6>
                                    <div style="max-height: 400px; overflow: hidden;">
                                        <canvas id="transferChart" width="600" height="250" style="max-width: 100%; height: auto;"></canvas>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-4">
                            <i class="fas fa-inbox fa-2x text-muted mb-3"></i>
                            <h6 class="text-muted">لا توجد بيانات لأنواع التحويل</h6>
                            <p class="text-muted mb-0">لم يتم العثور على إحصائيات لأنواع التحويل حتى الآن.</p>
                        </div>
                    }
                </div>
            </div>

            <!-- القسم الرابع: الإحصائيات الشهرية -->
            <div class="card content-card-enhanced">
                <div class="card-header text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-calendar-alt me-2"></i>
                            الإحصائيات الشهرية لسنة @Model.MonthlyStatistics.CurrentYear
                        </h5>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-light text-dark me-2">
                                @Model.MonthlyStatistics.ActiveMonthsCount من 12 شهر نشط
                            </span>
                            <span class="@Model.MonthlyStatistics.TrendIcon">
                                @Model.MonthlyStatistics.YearlyTrend
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if (Model.MonthlyStatistics.TotalYearlyRequests > 0)
                    {
                        <!-- ملخص سريع للسنة -->
                        <div class="row mb-4">
                            <div class="col-md-2 mb-3">
                                <div class="card stat-card-enhanced stat-card-primary text-white h-100">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-calendar-check"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@Model.MonthlyStatistics.TotalYearlyRequestsDisplay</div>
                                        <div class="stat-label-enhanced">إجمالي السنة</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 mb-3">
                                <div class="card stat-card-enhanced stat-card-success text-white h-100">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-check-double"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@Model.MonthlyStatistics.TotalCompletedRequests</div>
                                        <div class="stat-label-enhanced">منجز</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 mb-3">
                                <div class="card stat-card-enhanced stat-card-warning text-white h-100">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-hourglass-half"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@Model.MonthlyStatistics.TotalPendingRequests</div>
                                        <div class="stat-label-enhanced">معلق</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 mb-3">
                                <div class="card stat-card-enhanced stat-card-info text-white h-100">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@Model.MonthlyStatistics.MonthlyAverageDisplay</div>
                                        <div class="stat-label-enhanced">متوسط شهري</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 mb-3">
                                <div class="card stat-card-enhanced stat-card-gradient text-white h-100">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-percentage"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@Model.MonthlyStatistics.YearlyCompletionRateDisplay</div>
                                        <div class="stat-label-enhanced">معدل الإنجاز</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 mb-3">
                                <div class="card stat-card-enhanced stat-card-secondary text-white h-100">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-times-circle"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@Model.MonthlyStatistics.TotalCancelledRequests</div>
                                        <div class="stat-label-enhanced">ملغي</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- معلومات أفضل وأسوأ شهر -->
                        @if (!string.IsNullOrEmpty(Model.MonthlyStatistics.BestMonth))
                        {
                            <div class="row mb-4">
                                <div class="col-md-6">
                                    <div class="alert alert-success border-0">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-trophy fa-2x text-warning me-3"></i>
                                            <div>
                                                <h6 class="alert-heading mb-1">أفضل شهر: @Model.MonthlyStatistics.BestMonth</h6>
                                                <p class="mb-0">@Model.MonthlyStatistics.BestMonthCount طلب</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="alert alert-info border-0">
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-chart-bar fa-2x text-primary me-3"></i>
                                            <div>
                                                <h6 class="alert-heading mb-1">أقل شهر: @Model.MonthlyStatistics.WorstMonth</h6>
                                                <p class="mb-0">@Model.MonthlyStatistics.WorstMonthCount طلب</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }

                        <!-- جدول الإحصائيات الشهرية -->
                        <div class="row">
                            <div class="col-md-8">
                                <h6 class="mb-3">تفاصيل الإحصائيات الشهرية</h6>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th><i class="fas fa-calendar me-1"></i>الشهر</th>
                                                <th><i class="fas fa-list-ol me-1"></i>العدد</th>
                                                <th><i class="fas fa-check me-1"></i>منجز</th>
                                                <th><i class="fas fa-clock me-1"></i>معلق</th>
                                                <th><i class="fas fa-times me-1"></i>ملغي</th>
                                                <th><i class="fas fa-percentage me-1"></i>النسبة</th>
                                                <th><i class="fas fa-chart-line me-1"></i>الإنجاز</th>
                                                <th><i class="fas fa-star me-1"></i>الأداء</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var month in Model.MonthlyStatistics.MonthlyBreakdown)
                                            {
                                                <tr class="@(month.IsActiveMonth ? "" : "table-secondary opacity-50")">
                                                    <td>
                                                        <strong>@month.MonthName</strong>
                                                        @if (month.IsHighPerformanceMonth)
                                                        {
                                                            <i class="fas fa-star text-warning ms-1" title="شهر متميز"></i>
                                                        }
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-@(month.IsActiveMonth ? "primary" : "secondary") fs-6">
                                                            @month.RequestCountDisplay
                                                        </span>
                                                    </td>
                                                    <td>
                                                        @if (month.CompletedCount > 0)
                                                        {
                                                            <span class="text-success fw-bold">@month.CompletedCount</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="text-muted">0</span>
                                                        }
                                                    </td>
                                                    <td>
                                                        @if (month.PendingCount > 0)
                                                        {
                                                            <span class="text-warning fw-bold">@month.PendingCount</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="text-muted">0</span>
                                                        }
                                                    </td>
                                                    <td>
                                                        @if (month.CancelledCount > 0)
                                                        {
                                                            <span class="text-danger fw-bold">@month.CancelledCount</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="text-muted">0</span>
                                                        }
                                                    </td>
                                                    <td>
                                                        @if (month.IsActiveMonth)
                                                        {
                                                            <span class="badge bg-info">@month.MonthlyPercentageDisplay</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="text-muted">--</span>
                                                        }
                                                    </td>
                                                    <td>
                                                        @if (month.IsActiveMonth)
                                                        {
                                                            <span class="badge <EMAIL>">@month.CompletionRateDisplay</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="text-muted">--</span>
                                                        }
                                                    </td>
                                                    <td>
                                                        @if (month.IsActiveMonth)
                                                        {
                                                            <span class="badge <EMAIL>">@month.PerformanceLevel</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="badge bg-secondary">غير نشط</span>
                                                        }
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- الرسم البياني الشهري -->
                            <div class="col-md-4">
                                <h6 class="mb-3">الرسم البياني الشهري</h6>
                                <div class="text-center">
                                    <canvas id="monthlyChart" width="300" height="400"></canvas>
                                </div>

                                <!-- مؤشرات الرسم البياني -->
                                <div class="mt-3">
                                    <div class="d-flex justify-content-center flex-wrap gap-2">
                                        <span class="badge bg-primary">
                                            <i class="fas fa-circle me-1"></i>الطلبات الشهرية
                                        </span>
                                        <span class="badge bg-success">
                                            <i class="fas fa-circle me-1"></i>المكتملة
                                        </span>
                                        <span class="badge bg-warning">
                                            <i class="fas fa-circle me-1"></i>المعلقة
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- إحصائيات سريعة إضافية -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card bg-light border-0">
                                    <div class="card-body">
                                        <div class="row text-center">
                                            <div class="col-md-3">
                                                <h6 class="text-muted mb-1">الأشهر النشطة</h6>
                                                <h4 class="text-primary mb-0">@Model.MonthlyStatistics.ActiveMonthsCount/12</h4>
                                            </div>
                                            <div class="col-md-3">
                                                <h6 class="text-muted mb-1">متوسط الطلبات</h6>
                                                <h4 class="text-info mb-0">@Model.MonthlyStatistics.MonthlyAverageDisplay</h4>
                                            </div>
                                            <div class="col-md-3">
                                                <h6 class="text-muted mb-1">الاتجاه السنوي</h6>
                                                <h4 class="mb-0">
                                                    <span class="@Model.MonthlyStatistics.TrendIcon.Replace("fas fa-", "").Replace(" text-", " text-")">
                                                        @Model.MonthlyStatistics.YearlyTrend
                                                    </span>
                                                </h4>
                                            </div>
                                            <div class="col-md-3">
                                                <h6 class="text-muted mb-1">معدل الإنجاز</h6>
                                                <h4 class="text-success mb-0">@Model.MonthlyStatistics.YearlyCompletionRateDisplay</h4>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                    else
                    {
                        <!-- حالة عدم وجود بيانات -->
                        <div class="text-center py-5">
                            <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد بيانات للسنة الحالية</h5>
                            <p class="text-muted mb-0">لم يتم العثور على أي طلبات في سنة @Model.MonthlyStatistics.CurrentYear حتى الآن.</p>
                        </div>
                    }
                </div>
            </div>
            <!-- القسم الخامس: إحصائيات أنواع الطلبات - النسخة البسيطة -->
            <!-- القسم الخامس: إحصائيات أنواع الطلبات - النسخة البسيطة -->
            <div class="card content-card-enhanced">
                <div class="card-header text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-tags me-2"></i>
                        إحصائيات أنواع الطلبات
                    </h5>
                </div>
                <div class="card-body">
                    @if (Model.OrderTypesStatistics != null && Model.OrderTypesStatistics.Count > 0)
                    {
                        var orderTypesStats = Model.OrderTypesStatistics;

                        // حساب الإجماليات بطريقة يدوية لتجنب Lambda expressions
                        int orderTypesTotalOrders = 0;
                        int orderTypesTotalCompleted = 0;
                        int orderTypesTotalPending = 0;
                        int orderTypesTotalCancelled = 0;

                        foreach (var orderType in orderTypesStats)
                        {
                            orderTypesTotalOrders += orderType.Count;
                            orderTypesTotalCompleted += orderType.CompletedOrders;
                            orderTypesTotalPending += orderType.PendingOrders;
                            orderTypesTotalCancelled += orderType.CancelledOrders;
                        }

                        <!-- ملخص سريع -->
                        <div class="row mb-4">
                            <div class="col-md-3 mb-3">
                                <div class="card stat-card-enhanced stat-card-primary text-white h-100">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-list-ol"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@orderTypesStats.Count</div>
                                        <div class="stat-label-enhanced">أنواع الطلبات</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card stat-card-enhanced stat-card-info text-white h-100">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-file-alt"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@orderTypesTotalOrders</div>
                                        <div class="stat-label-enhanced">إجمالي الطلبات</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card stat-card-enhanced stat-card-success text-white h-100">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-check-circle"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@orderTypesTotalCompleted</div>
                                        <div class="stat-label-enhanced">مكتملة</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card stat-card-enhanced stat-card-warning text-white h-100">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-clock"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@orderTypesTotalPending</div>
                                        <div class="stat-label-enhanced">معلقة</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- جدول أنواع الطلبات البسيط -->
                        <div class="row">
                            <div class="col-md-8">
                                <h6 class="mb-3">تفاصيل أنواع الطلبات</h6>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th><i class="fas fa-hashtag me-1"></i>ترتيب</th>
                                                <th><i class="fas fa-tag me-1"></i>نوع الطلب</th>
                                                <th><i class="fas fa-list-ol me-1"></i>العدد</th>
                                                <th><i class="fas fa-percentage me-1"></i>النسبة</th>
                                                <th><i class="fas fa-check me-1"></i>مكتمل</th>
                                                <th><i class="fas fa-clock me-1"></i>معلق</th>
                                                <th><i class="fas fa-chart-line me-1"></i>معدل الإنجاز</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var orderType in orderTypesStats)
                                            {
                                                var completionRate = orderType.Count > 0 ? (double)orderType.CompletedOrders / orderType.Count * 100 : 0;
                                                var completionClass = completionRate >= 80 ? "success" : completionRate >= 60 ? "info" : completionRate >= 40 ? "warning" : "danger";

                                                <tr>
                                                    <td>
                                                        @if (orderType.Ranking == 1)
                                                        {
                                                            <span class="badge bg-warning fs-6">🥇 #@orderType.Ranking</span>
                                                        }
                                                        else if (orderType.Ranking == 2)
                                                        {
                                                            <span class="badge bg-secondary fs-6">🥈 #@orderType.Ranking</span>
                                                        }
                                                        else if (orderType.Ranking == 3)
                                                        {
                                                            <span class="badge bg-info fs-6">🥉 #@orderType.Ranking</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="badge bg-dark fs-6">#@orderType.Ranking</span>
                                                        }
                                                    </td>
                                                    <td>
                                                        <strong>@orderType.TransferType</strong>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-primary fs-6">@orderType.Count</span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info">@orderType.PercentageDisplay</span>
                                                    </td>
                                                    <td>
                                                        @if (orderType.CompletedOrders > 0)
                                                        {
                                                            <span class="text-success fw-bold">@orderType.CompletedOrders</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="text-muted">0</span>
                                                        }
                                                    </td>
                                                    <td>
                                                        @if (orderType.PendingOrders > 0)
                                                        {
                                                            <span class="text-warning fw-bold">@orderType.PendingOrders</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="text-muted">0</span>
                                                        }
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-@completionClass">@completionRate.ToString("F1")%</span>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- الرسم البياني البسيط -->
                            <div class="col-md-4">
                                <h6 class="mb-3">التوزيع البياني</h6>
                                <div class="text-center">
                                    <canvas id="simpleOrderTypesChart" width="300" height="300"></canvas>
                                </div>
                            </div>
                        </div>
                    }
                    else
                    {
                        <!-- حالة عدم وجود بيانات -->
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد بيانات لأنواع الطلبات</h5>
                            <p class="text-muted mb-0">لم يتم العثور على أي طلبات مصنفة حسب النوع.</p>
                        </div>
                    }
                </div>
            </div>
            <!-- القسم السادس: توزيع الطلبات حسب الحالة -->
            <div class="card content-card-enhanced">
                <div class="card-header text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-pie me-2"></i>
                            توزيع الطلبات حسب الحالة
                        </h5>
                        <div class="d-flex align-items-center">
                            @{
                                int statusCount = Model.OrderStatusStatistics?.Count ?? 0;
                                int statusTotalCount = 0;
                                if (Model.OrderStatusStatistics != null)
                                {
                                    foreach (var status in Model.OrderStatusStatistics)
                                    {
                                        statusTotalCount += status.Count;
                                    }
                                }
                            }
                            <span class="badge bg-light text-dark me-2">
                                @statusCount حالة مختلفة
                            </span>
                            <span class="badge bg-info">
                                @statusTotalCount طلب
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    @if (Model.OrderStatusStatistics != null && Model.OrderStatusStatistics.Count > 0)
                    {
                        var statusStats = Model.OrderStatusStatistics;

                        // حساب الإجماليات بطريقة يدوية
                        int statusTotalOrders = 0;
                        int statusThisMonth = 0;
                        int statusThisWeek = 0;

                        foreach (var status in statusStats)
                        {
                            statusTotalOrders += status.Count;
                            statusThisMonth += status.ThisMonthCount;
                            statusThisWeek += status.ThisWeekCount;
                        }

                        <!-- ملخص سريع -->
                        <div class="row mb-4">
                            <div class="col-md-3 mb-3">
                                <div class="card stat-card-enhanced stat-card-primary text-white h-100">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-clipboard-list"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@statusStats.Count</div>
                                        <div class="stat-label-enhanced">حالات مختلفة</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card stat-card-enhanced stat-card-info text-white h-100">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-list-ol"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@statusTotalOrders</div>
                                        <div class="stat-label-enhanced">إجمالي الطلبات</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card stat-card-enhanced stat-card-success text-white h-100">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-calendar-alt"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@statusThisMonth</div>
                                        <div class="stat-label-enhanced">هذا الشهر</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card stat-card-enhanced stat-card-warning text-white h-100">
                                    <div class="card-body text-center py-3">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-calendar-week"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@statusThisWeek</div>
                                        <div class="stat-label-enhanced">هذا الأسبوع</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- محتوى الحالات -->
                        <div class="row">
                            <!-- جدول الحالات -->
                            <div class="col-md-8">
                                <h6 class="mb-3">تفاصيل حالات الطلبات</h6>
                                <div class="table-responsive">
                                    <table class="table table-striped table-hover">
                                        <thead class="table-dark">
                                            <tr>
                                                <th><i class="fas fa-hashtag me-1"></i>ترتيب</th>
                                                <th><i class="fas fa-flag me-1"></i>حالة الطلب</th>
                                                <th><i class="fas fa-list-ol me-1"></i>العدد</th>
                                                <th><i class="fas fa-percentage me-1"></i>النسبة</th>
                                                <th><i class="fas fa-calendar me-1"></i>هذا الشهر</th>
                                                <th><i class="fas fa-calendar-week me-1"></i>هذا الأسبوع</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var status in statusStats)
                                            {
                                                var statusClass = GetStatusClass(status.TransferType);
                                                var statusIcon = GetStatusIcon(status.TransferType);

                                                <tr>
                                                    <td>
                                                        @if (status.Ranking == 1)
                                                        {
                                                            <span class="badge bg-warning fs-6">🥇 #@status.Ranking</span>
                                                        }
                                                        else if (status.Ranking == 2)
                                                        {
                                                            <span class="badge bg-secondary fs-6">🥈 #@status.Ranking</span>
                                                        }
                                                        else if (status.Ranking == 3)
                                                        {
                                                            <span class="badge bg-info fs-6">🥉 #@status.Ranking</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="badge bg-dark fs-6">#@status.Ranking</span>
                                                        }
                                                    </td>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <i class="@statusIcon me-2 text-@statusClass"></i>
                                                            <strong>@status.TransferType</strong>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-@statusClass fs-6">@status.Count</span>
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-info">@status.PercentageDisplay</span>
                                                    </td>
                                                    <td>
                                                        @if (status.ThisMonthCount > 0)
                                                        {
                                                            <span class="text-success fw-bold">@status.ThisMonthCount</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="text-muted">0</span>
                                                        }
                                                    </td>
                                                    <td>
                                                        @if (status.ThisWeekCount > 0)
                                                        {
                                                            <span class="text-primary fw-bold">@status.ThisWeekCount</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="text-muted">0</span>
                                                        }
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                            <!-- الرسم البياني الدائري -->
                            <div class="col-md-4">
                                <h6 class="mb-3">التوزيع البياني للحالات</h6>
                                <div class="text-center">
                                    <canvas id="orderStatusChart" width="300" height="300"></canvas>
                                </div>

                                <!-- مؤشرات الرسم البياني -->
                                <div class="mt-3">
                                    <div class="d-flex justify-content-center flex-wrap gap-2">
                                        <span class="badge bg-success">
                                            <i class="fas fa-circle me-1"></i>مقبول
                                        </span>
                                        <span class="badge bg-primary">
                                            <i class="fas fa-circle me-1"></i>مدير الموارد البشرية
                                        </span>
                                        <span class="badge bg-dark">
                                            <i class="fas fa-circle me-1"></i>المشرفون
                                        </span>
                                        <span class="badge bg-danger">
                                            <i class="fas fa-circle me-1"></i>منسق الموارد البشرية
                                        </span>
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-circle me-1"></i>مدير القسم
                                        </span>
                                        <span class="badge bg-info">
                                            <i class="fas fa-circle me-1"></i>مساعدين المدير
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    }
                    else
                    {
                        <!-- حالة عدم وجود بيانات -->
                        <div class="text-center py-5">
                            <i class="fas fa-chart-pie fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد بيانات للحالات</h5>
                            <p class="text-muted mb-0">لم يتم العثور على أي طلبات مصنفة حسب الحالة.</p>
                        </div>
                    }
                </div>
            </div>

        </div> <!-- إغلاق التبويب المدموج -->
        <!-- تبويب الأقسام المبسط - مُصحح بالكامل 🏢📊 -->
        <div class="tab-pane fade" id="departmentStats" role="tabpanel">
            <div class="card content-card-enhanced">
                <div class="card-header text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-building me-2"></i>
                            إحصائيات الأقسام التفصيلية
                        </h5>
                    </div>
                </div>
                <div class="card-body">
                    @{
                        var deptStats = Model.DepartmentStatistics;
                        int deptCount = 0;
                        int deptTotalOrders = 0;
                        int deptTotalCompleted = 0;
                        int deptTotalPending = 0;
                        int deptTotalCancelled = 0; // إضافة جديدة
                        int deptTopPerformers = 0;
                        double deptCompletionSum = 0;
                        int deptTotalProcessed = 0;

                        if (deptStats != null)
                        {
                            foreach (var dept in deptStats)
                            {
                                deptCount++;
                                deptTotalOrders += dept.TotalOrders;
                                deptTotalCompleted += dept.CompletedOrders;
                                deptTotalPending += dept.PendingOrders;
                                deptTotalCancelled += dept.CancelledOrders; // إضافة حساب الملغي
                                deptCompletionSum += dept.CompletionPercentage;
                                deptTotalProcessed += dept.CompletedOrders + dept.CancelledOrders;

                                if (dept.IsTopPerformer)
                                {
                                    deptTopPerformers++;
                                }
                            }
                        }

                        double deptAvgProcessing = deptTotalOrders > 0 ?
                        (double)deptTotalProcessed / deptTotalOrders * 100 : 0;
                        int totalPending = deptTotalPending;
                        int urgentDepts = 0;

                        if (deptStats != null)
                        {
                            foreach (var dept in deptStats)
                            {
                                if (dept.PendingOrders > 10)
                                {
                                    urgentDepts++;
                                }
                            }
                        }
                    }
                    <!-- 🚨 المكان الأول: إدراج التنبيه هنا - بعد حساب البيانات وقبل الملخص -->
                    @if (totalPending > 0)
                    {
                        <div class="alert @(urgentDepts > 0 ? "alert-danger" : "alert-warning") alert-dismissible fade show mb-4">
                            <div class="d-flex align-items-center">
                                <i class="fas @(urgentDepts > 0 ? "fa-exclamation-triangle" : "fa-clock") fa-2x me-3"></i>
                                <div>
                                    <strong>@(urgentDepts > 0 ? "🚨 تحذير عاجل!" : "⚠️ تنبيه:")</strong>
                                    <span class="ms-2">@totalPending طلب معلق</span>
                                    @if (urgentDepts > 0)
                                    {
                                        <small class="d-block text-muted">@urgentDepts قسم يحتاج تدخل فوري</small>
                                    }
                                </div>
                                <button class="btn btn-sm btn-@(urgentDepts > 0 ? "danger" : "warning") ms-auto" onclick="scrollToPending()">
                                    <i class="fas fa-arrow-down me-1"></i>
                                    عرض الأقسام
                                </button>
                            </div>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    }

                    <!-- 📊 ملخص سريع مبسط -->
                    <div class="row mb-4">
                        <div class="col-md-2 mb-3">
                            <div class="card stat-card-enhanced stat-card-primary text-white h-100">
                                <div class="card-body text-center">
                                    <div class="stat-icon-enhanced">
                                        <i class="fas fa-building"></i>
                                    </div>
                                    <div class="stat-number-enhanced">@deptCount</div>
                                    <div class="stat-label-enhanced">إجمالي الأقسام</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="card stat-card-enhanced stat-card-info text-white h-100">
                                <div class="card-body text-center">
                                    <div class="stat-icon-enhanced">
                                        <i class="fas fa-clipboard-list"></i>
                                    </div>
                                    <div class="stat-number-enhanced">@deptTotalOrders</div>
                                    <div class="stat-label-enhanced">إجمالي الطلبات</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="card stat-card-enhanced stat-card-success text-white h-100">
                                <div class="card-body text-center">
                                    <div class="stat-icon-enhanced">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div class="stat-number-enhanced">@deptTotalCompleted</div>
                                    <div class="stat-label-enhanced">إجمالي المنجز</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="card stat-card-enhanced stat-card-warning text-white h-100">
                                <div class="card-body text-center">
                                    <div class="stat-icon-enhanced">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="stat-number-enhanced">@deptTotalPending</div>
                                    <div class="stat-label-enhanced">قيد التنفيذ</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="card stat-card-enhanced stat-card-gradient text-white h-100">
                                <div class="card-body text-center">
                                    <div class="stat-icon-enhanced">
                                        <i class="fas fa-percentage"></i>
                                    </div>
                                    <div class="stat-number-enhanced">@deptAvgProcessing.ToString("F1")%</div>
                                    <div class="stat-label-enhanced">متوسط الإنجاز</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="card stat-card-enhanced stat-card-danger text-white h-100">
                                <div class="card-body text-center">
                                    <div class="stat-icon-enhanced">
                                        <i class="fas fa-times-circle"></i>
                                    </div>
                                    <div class="stat-number-enhanced">@deptTotalCancelled</div>
                                    <div class="stat-label-enhanced">إجمالي الملغي</div>
                                </div>
                            </div>
                        </div>
                    </div>


                    <!-- 📋 عرض الأقسام المبسط -->
                    <div id="departmentTableView">
                        <div class="row" id="departmentsList">
                            @{
                                // ترتيب يدوي يطابق الكود الخلفي تماماً
                                var sortedDepts = new List<dynamic>();
                                if (deptStats != null)
                                {
                                    foreach (var dept in deptStats)
                                    {
                                        sortedDepts.Add(dept);
                                    }

                                    // ترتيب نفس الكود الخلفي: نسبة الإنجاز ثم العدد المكتمل
                                    for (int i = 0; i < sortedDepts.Count - 1; i++)
                                    {
                                        for (int j = 0; j < sortedDepts.Count - i - 1; j++)
                                        {
                                            // إذا نسبة الإنجاز مختلفة
                                            if (sortedDepts[j].CompletionPercentage != sortedDepts[j + 1].CompletionPercentage)
                                            {
                                                if (sortedDepts[j].CompletionPercentage < sortedDepts[j + 1].CompletionPercentage)
                                                {
                                                    var temp = sortedDepts[j];
                                                    sortedDepts[j] = sortedDepts[j + 1];
                                                    sortedDepts[j + 1] = temp;
                                                }
                                            }
                                            // إذا تساوت نسبة الإنجاز، قارن العدد المكتمل
                                            else if (sortedDepts[j].CompletedOrders < sortedDepts[j + 1].CompletedOrders)
                                            {
                                                var temp = sortedDepts[j];
                                                sortedDepts[j] = sortedDepts[j + 1];
                                                sortedDepts[j + 1] = temp;
                                            }
                                        }
                                    }
                                }
                            }

                            @foreach (var dept in sortedDepts)
                            {
                                <div class="col-lg-6 col-xl-4 mb-3 department-item" data-name="@dept.DepartmentName.ToLower()">
                                    <div class="card border-0 shadow-sm h-100">
                                        <div class="card-body">
                                            <!-- عنوان القسم مع الترتيب -->
                                            <div class="d-flex justify-content-between align-items-start mb-3">
                                                <div>
                                                    <h6 class="card-title mb-1">
                                                        @if (dept.IsTopPerformer)
                                                        {
                                                            @if (dept.Ranking == 1)
                                                            {
                                                                <!-- 🥇 ذهبية بألوان حقيقية -->
                                                                <span class="badge me-2 fs-5 p-2" style="background: linear-gradient(45deg, #FFD700, #FFA500); color: #8B4513;">🥇 #1</span>
                                                            }
                                                            else if (dept.Ranking == 2)
                                                            {
                                                                <!-- 🥈 فضية بألوان حقيقية -->
                                                                <span class="badge me-2 fs-5 p-2" style="background: linear-gradient(45deg, #C0C0C0, #A9A9A9); color: #2F4F4F;">🥈 #2</span>
                                                            }
                                                            else if (dept.Ranking == 3)
                                                            {
                                                                <!-- 🥉 برونزية بألوان حقيقية -->
                                                                <span class="badge me-2 fs-5 p-2" style="background: linear-gradient(45deg, #CD7F32, #D2691E); color: #FFFFFF;">🥉 #3</span>
                                                            }
                                                            else if (dept.Ranking <= 5)
                                                            {
                                                                <!-- المراكز 4-5 -->
                                                                <span class="badge bg-primary me-2 fs-6 p-2">#@dept.Ranking</span>
                                                            }
                                                        }
                                                        else
                                                        {
                                                            <!-- باقي المراكز -->
                                                            <span class="badge bg-secondary me-2 fs-6 p-1">#@dept.Ranking</span>
                                                        }
                                                        @dept.DepartmentName
                                                    </h6>
                                                    <!-- ✅ حذف حالة الأداء من هنا -->
                                                </div>
                                                <div class="text-end">
                                                    <!-- ✅ نقل حالة الأداء هنا - الركن الأيسر العلوي -->
                                                    <span class="badge <EMAIL>">@dept.PerformanceStatus</span>
                                                </div>
                                            </div>

                                            <!-- الإحصائيات الأساسية -->
                                            <div class="row text-center mb-3">
                                                <div class="col-3">
                                                    <div class="text-primary">
                                                        <div class="fw-bold">@dept.TotalOrders</div>
                                                        <small class="text-muted">إجمالي</small>
                                                    </div>
                                                </div>
                                                <div class="col-3">
                                                    <div class="text-success">
                                                        <div class="fw-bold">@dept.CompletedOrders</div>
                                                        <small class="text-muted">منجز</small>
                                                    </div>
                                                </div>
                                                <div class="col-3">
                                                    <div class="text-warning">
                                                        <div class="fw-bold">@dept.PendingOrders</div>
                                                        <small class="text-muted">معلق</small>
                                                    </div>
                                                </div>
                                                <div class="col-3">
                                                    <div class="text-danger">
                                                        <div class="fw-bold">@dept.CancelledOrders</div>
                                                        <small class="text-muted">ملغي</small>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- شريط التقدم -->
                                            @if (dept.TotalOrders > 0)
                                            {
                                                <div class="progress mb-3" style="height: 8px;">
                                                    <div class="progress-bar bg-success"
                                                         style="width: @dept.CompletionPercentage%"
                                                         title="منجز: @dept.CompletionRateDisplay"></div>
                                                    <div class="progress-bar bg-warning"
                                                         style="width: @dept.PendingPercentage%"
                                                         title="معلق: @dept.PendingPercentage.ToString("F1")%"></div>
                                                    <div class="progress-bar bg-danger"
                                                         style="width: @dept.CancellationPercentage%"
                                                         title="ملغي: @dept.CancellationPercentage.ToString("F1")%"></div>
                                                </div>
                                            }

                                            <!-- معلومات إضافية -->
                                            <div class="row small text-muted">
                                                <div class="col-6">
                                                    <i class="fas fa-percentage me-1"></i>
                                                    نسبة الإنجاز: <strong class="<EMAIL>">@dept.CompletionRateDisplay</strong>
                                                </div>
                                                <div class="col-6">
                                                    <i class="fas fa-clock me-1"></i>
                                                    متوسط الوقت: <strong>@dept.AverageTimeDisplay</strong>
                                                </div>
                                            </div>

                                            <!-- النشاط الزمني المحدث -->
                                            <div class="row small text-muted mt-2">
                                                <div class="col-12">
                                                    <!-- عنوان قسم البيانات الزمنية مع الاتجاه في نفس السطر -->
                                                    <div class="d-flex justify-content-between align-items-center mb-2">
                                                        <div>
                                                            <i class="fas fa-chart-line me-1 text-primary"></i>
                                                            <strong class="text-primary">النشاط الزمني:</strong>
                                                        </div>
                                                        <div>
                                                            <small class="text-info">
                                                                <i class="fas fa-trending-up me-1"></i>
                                                                <strong>الاتجاه:</strong> @dept.TrendDirection
                                                            </small>
                                                        </div>
                                                    </div>

                                                    <!-- البيانات الزمنية في سطر واحد مع badges -->
                                                    <div class="d-flex flex-wrap gap-1">
                                                        <span class="badge bg-primary badge-sm">
                                                            📅 شهر: <strong>@dept.ThisMonthOrders</strong>
                                                        </span>
                                                        <span class="badge bg-info badge-sm">
                                                            📊 ٣ أشهر: <strong>@dept.Last3MonthsOrders</strong>
                                                        </span>
                                                        <span class="badge bg-success badge-sm">
                                                            📈 ٦ أشهر: <strong>@dept.Last6MonthsOrders</strong>
                                                        </span>
                                                        <span class="badge bg-secondary badge-sm">
                                                            🏆 سنة: <strong>@dept.LastYearOrders</strong>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>

                    <!-- 📊 عرض الرسم البياني البسيط -->
                    <div id="departmentChartView" class="d-none">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">📊 رسم بياني لنسب الإنجاز</h6>
                            </div>
                            <div class="card-body">
                                <canvas id="departmentChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- تبويب مساعدو المدير - محسن بحذر -->
        <div class="tab-pane fade" id="assistantManagers" role="tabpanel">
            <div class="card content-card-enhanced">
                <div class="card-header text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-user-tie me-2"></i>
                            إحصائيات مساعدي المدير
                        </h5>
                    </div>
                </div>
                <div class="card-body">
                    @{
                        var assistantStats = Model.AssistantManagerStatistics;

                        // ✅ الكود الأصلي - بدون تغيير
                        int assistantCount = 0;
                        int totalRequests = 0;
                        int totalCompleted = 0;
                        double totalCompletionTime = 0;
                        int validTimeEntries = 0;

                        // ✨ إضافات جديدة فقط - بدون كسر الكود الموجود
                        int totalReturned = 0;
                        int totalCancelled = 0;
                        int totalUnderExecution = 0;
                        int totalWorksDone = 0; // إجمالي الأعمال الفعلية
                        int totalOrdersProcessed = 0; // إجمالي الطلبات المُعالجة

                        foreach (var assistant in assistantStats)
                        {
                            assistantCount++;
                            totalRequests += assistant.UnderExecution + assistant.Completed + assistant.Returned + assistant.Cancelled;
                            totalCompleted += assistant.Completed;

                            // ✨ إضافات جديدة
                            totalReturned += assistant.Returned;
                            totalCancelled += assistant.Cancelled;
                            totalUnderExecution += assistant.UnderExecution;

                            // حساب الأعمال الفعلية (إذا كانت الخاصية موجودة)
                            if (assistant.GetType().GetProperty("TotalWorksDone") != null)
                            {
                                totalWorksDone += assistant.TotalWorksDone;
                            }

                            // حساب الطلبات المُعالجة (إذا كانت الخاصية موجودة)
                            if (assistant.GetType().GetProperty("TotalOrdersProcessed") != null)
                            {
                                totalOrdersProcessed += assistant.TotalOrdersProcessed;
                            }
                            else
                            {
                                // احتياطي: حساب من البيانات الموجودة
                                totalOrdersProcessed += assistant.Completed + assistant.Returned + assistant.Cancelled;
                            }

                            if (assistant.AverageCompletionTime > 0)
                            {
                                totalCompletionTime += assistant.AverageCompletionTime;
                                validTimeEntries++;
                            }
                        }

                        string averageCompletionTime = validTimeEntries > 0 ? (totalCompletionTime / validTimeEntries).ToString("F1") : "0.0";

                        // ✨ حسابات جديدة آمنة
                        double assistantOverallProcessingRate = totalRequests > 0 ? (double)totalOrdersProcessed / totalRequests * 100 : 0;
                        double assistantOverallWorksPerOrder = totalOrdersProcessed > 0 ? (double)totalWorksDone / totalOrdersProcessed : 1;
                    }

                    <!-- ملخص سريع محسن - مع الحفاظ على الأصل -->
                    <div class="row mb-4">
                        <!-- البطاقة الأولى: عدد المساعدين - كما هي -->
                        <div class="col-md-2 mb-3">
                            <div class="card stat-card-enhanced stat-card-primary text-white h-100">
                                <div class="card-body text-center">
                                    <div class="stat-icon-enhanced">
                                        <i class="fas fa-user-tie"></i>
                                    </div>
                                    <div class="stat-number-enhanced">@assistantCount</div>
                                    <div class="stat-label-enhanced">عدد المساعدين</div>
                                </div>
                            </div>
                        </div>

                        <!-- ✨ بطاقة جديدة: إجمالي الأعمال -->
                        <div class="col-md-2 mb-3">
                            <div class="card stat-card-enhanced stat-card-warning text-white h-100">
                                <div class="card-body text-center">
                                    <div class="stat-icon-enhanced">
                                        <i class="fas fa-briefcase"></i>
                                    </div>
                                    <div class="stat-number-enhanced">@totalWorksDone</div>
                                    <div class="stat-label-enhanced">إجمالي الأعمال</div>
                                    <small class="text-light opacity-75">النقرات الفعلية</small>
                                </div>
                            </div>
                        </div>

                        <!-- البطاقة الثانية: إجمالي الطلبات - كما هي -->
                        <div class="col-md-2 mb-3">
                            <div class="card stat-card-enhanced stat-card-info text-white h-100">
                                <div class="card-body text-center">
                                    <div class="stat-icon-enhanced">
                                        <i class="fas fa-tasks"></i>
                                    </div>
                                    <div class="stat-number-enhanced">@totalRequests</div>
                                    <div class="stat-label-enhanced">إجمالي الطلبات</div>
                                </div>
                            </div>
                        </div>

                        <!-- البطاقة الثالثة: إجمالي المنجز - كما هي -->
                        <div class="col-md-2 mb-3">
                            <div class="card stat-card-enhanced stat-card-success text-white h-100">
                                <div class="card-body text-center">
                                    <div class="stat-icon-enhanced">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                    <div class="stat-number-enhanced">@totalCompleted</div>
                                    <div class="stat-label-enhanced">إجمالي المنجز</div>
                                </div>
                            </div>
                        </div>

                        <!-- ✨ بطاقة جديدة: تحت التنفيذ -->
                        <div class="col-md-2 mb-3">
                            <div class="card stat-card-enhanced stat-card-warning text-white h-100">
                                <div class="card-body text-center">
                                    <div class="stat-icon-enhanced">
                                        <i class="fas fa-hourglass-half"></i>
                                    </div>
                                    <div class="stat-number-enhanced">@totalUnderExecution</div>
                                    <div class="stat-label-enhanced">تحت التنفيذ</div>
                                </div>
                            </div>
                        </div>

                        <!-- البطاقة الرابعة: متوسط زمن الإنجاز - محسنة -->
                        <div class="col-md-2 mb-3">
                            <div class="card stat-card-enhanced stat-card-gradient text-white h-100">
                                <div class="card-body text-center">
                                    <div class="stat-icon-enhanced">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div class="stat-number-enhanced">@assistantOverallProcessingRate.ToString("F1")%</div>
                                    <div class="stat-label-enhanced">نسبة المعالجة</div>
                                    <small class="text-light opacity-75">متوسط الإنجاز: @averageCompletionTime يوم</small>
                                </div>
                            </div>
                        </div>
                    </div>


                    <!-- عرض الجدول المحسن - مع الحفاظ على البنية الأساسية -->
                    <div id="assistantManagerTableView">
                        <div class="assistant-manager-list" style="max-height: 600px; overflow-y: auto;">
                            @{
                                var assistantRankIndex = 1; // ✅ اسم فريد لمساعدي المدير
                            }
                            @foreach (var am in Model.AssistantManagerStatistics)
                            {
                                var total = am.UnderExecution + am.Completed + am.Returned + am.Cancelled;

                                <div class='assistant-manager-card stage-item-enhanced stage-info mb-3'
                                     data-assistantmanager='@am.AssistantManagerName.ToLower()'
                                     data-ranking='@assistantRankIndex'
                                     data-completed='@am.Completed'
                                     data-total='@total'>
                                    <div class="d-flex align-items-center">
                                        <div class="stage-icon-enhanced stage-icon-info">
                                            @{
                                                // ✨ أيقونات تخصص المساعد
                                                string specialtyIcon = "fas fa-user-tie";
                                                if (am.AssistantManagerName.Contains("الطبية")) specialtyIcon = "fas fa-stethoscope";
                                                else if (am.AssistantManagerName.Contains("التمريض")) specialtyIcon = "fas fa-user-nurse";
                                                else if (am.AssistantManagerName.Contains("الإدارية")) specialtyIcon = "fas fa-building";
                                                else if (am.AssistantManagerName.Contains("الموارد")) specialtyIcon = "fas fa-users";
                                            }
                                            <i class="@specialtyIcon"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <div class="row align-items-center">
                                                <div class="col-md-3">
                                                    <div class="d-flex align-items-center">
                                                        <!-- ✨ نظام ترتيب بسيط -->
                                                        @if (assistantRankIndex == 1)
                                                        {
                                                            <span class="badge me-2 fs-5 p-2" style="background: linear-gradient(45deg, #FFD700, #FFA500); color: #8B4513;">🥇</span>
                                                        }
                                                        else if (assistantRankIndex == 2)
                                                        {
                                                            <span class="badge me-2 fs-5 p-2" style="background: linear-gradient(45deg, #C0C0C0, #A9A9A9); color: #2F4F4F;">🥈</span>
                                                        }
                                                        else if (assistantRankIndex == 3)
                                                        {
                                                            <span class="badge me-2 fs-5 p-2" style="background: linear-gradient(45deg, #CD7F32, #D2691E); color: #FFFFFF;">🥉</span>
                                                        }
                                                        else if (assistantRankIndex == 4)
                                                        {
                                                            <span class="badge bg-primary me-2 fs-6 p-2">#@assistantRankIndex</span>
                                                        }

                                                        <div>
                                                            <h6 class="mb-1">@am.AssistantManagerName</h6>

                                                            @{
                                                                // حساب النسب والمؤشرات
                                                                double processingRate = total > 0 ? (double)(am.Completed + am.Returned + am.Cancelled) / total * 100 : 0;
                                                                string performanceLevel = processingRate >= 90 ? "ممتاز" : processingRate >= 75 ? "جيد جداً" : processingRate >= 60 ? "جيد" : "يحتاج تحسين";

                                                                // حساب الطلبات المُعالجة والأعمال
                                                                int ordersProcessed = am.Completed + am.Returned + am.Cancelled;
                                                                int worksTotal = 0;
                                                                double worksPerOrder = 1.0;
                                                                string trendDirection = "مستقر";

                                                                // محاولة الحصول على القيم المحسنة إذا كانت متوفرة
                                                                var worksProperty = am.GetType().GetProperty("TotalWorksDone");
                                                                var trendProperty = am.GetType().GetProperty("TrendDirection");

                                                                if (worksProperty != null)
                                                                {
                                                                    worksTotal = (int)(worksProperty.GetValue(am) ?? ordersProcessed);
                                                                }
                                                                else
                                                                {
                                                                    // حساب تقديري بناءً على العمليات
                                                                    worksTotal = ordersProcessed > 0 ? ordersProcessed : 1;
                                                                }

                                                                if (trendProperty != null)
                                                                {
                                                                    trendDirection = trendProperty.GetValue(am)?.ToString() ?? "مستقر";
                                                                }

                                                                worksPerOrder = ordersProcessed > 0 ? (double)worksTotal / ordersProcessed : 1.0;
                                                            }

                                                            <!-- ✨ التنسيق الجديد مثل مديري الموارد البشرية -->
                                                            <small class="text-muted d-block">
                                                                ⏱️ زمن الإنجاز: @am.AverageCompletionTime.ToString("F1") يوم
                                                            </small>
                                                            <small class="text-success fw-bold d-block">
                                                                📈 نسبة المعالجة: @processingRate.ToString("F1")%
                                                            </small>
                                                            <small class="text-primary fw-bold d-block">
                                                                🏆 الطلبات المُعالجة: @ordersProcessed
                                                            </small>
                                                            <small class="text-warning fw-bold d-block">
                                                                💼 إجمالي الأعمال: @worksTotal
                                                            </small>
                                                            <small class="text-secondary d-block">
                                                                ⚡ متوسط: @worksPerOrder.ToString("F1") عمل/طلب
                                                            </small>

                                                            <!-- 🔄 اتجاه الأداء -->
                                                            @if (trendDirection != "مستقر")
                                                            {
                                                                string trendDisplay = trendDirection switch
                                                                {
                                                                    "متزايد" => "↗️ متزايد",
                                                                    "متناقص" => "↘️ متناقص",
                                                                    _ => "➡️ مستقر"
                                                                };

                                                                <small class="text-info d-block">
                                                                    🔄 الاتجاه: @trendDisplay
                                                                </small>
                                                            }
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-7">
                                                    <div class="row text-center">
                                                        <div class="col-3">
                                                            <div class="stat-mini">
                                                                <div class="stat-mini-number text-warning">@am.UnderExecution</div>
                                                                <div class="stat-mini-label nowrap">⏳ تحت التنفيذ</div>
                                                            </div>
                                                        </div>
                                                        <div class="col-3">
                                                            <div class="stat-mini">
                                                                <div class="stat-mini-number text-success">@am.Completed</div>
                                                                <div class="stat-mini-label nowrap">✅ منجز</div>
                                                            </div>
                                                        </div>
                                                        <div class="col-3">
                                                            <div class="stat-mini">
                                                                <div class="stat-mini-number text-info">@am.Returned</div>
                                                                <div class="stat-mini-label nowrap">↩️ معاد</div>
                                                            </div>
                                                        </div>
                                                        <div class="col-3">
                                                            <div class="stat-mini">
                                                                <div class="stat-mini-number text-danger">@am.Cancelled</div>
                                                                <div class="stat-mini-label nowrap">❌ ملغي</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-2 text-center">
                                                    <div class="mb-2">
                                                        <small class="text-muted fw-bold d-block mb-1">📅 التوزيع الزمني</small>
                                                        <div>
                                                            <span class="badge badge-sm bg-primary me-1" title="إنجازات هذا الشهر">
                                                                📅 شهر: @am.ThisMonthOperations
                                                            </span>
                                                            <span class="badge badge-sm bg-info me-1" title="إنجازات آخر 3 أشهر">
                                                                📊 ٣ أشهر: @am.Last3MonthsOperations
                                                            </span>
                                                            <span class="badge badge-sm bg-success me-1" title="إنجازات آخر 6 أشهر">
                                                                📈 ٦ أشهر: @am.Last6MonthsOperations
                                                            </span>
                                                            <span class="badge badge-sm bg-secondary" title="إنجازات آخر سنة">
                                                                🏆 سنة: @am.LastYearOperations
                                                            </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            @if (total > 0)
                                            {
                                                <div class="progress progress-enhanced mt-3">
                                                    <div class="progress-bar bg-success" style="width: @((double)am.Completed / total * 100)%" title="منجز: @am.Completed"></div>
                                                    <div class="progress-bar bg-warning" style="width: @((double)am.UnderExecution / total * 100)%" title="تحت التنفيذ: @am.UnderExecution"></div>
                                                    <div class="progress-bar bg-info" style="width: @((double)am.Returned / total * 100)%" title="معاد: @am.Returned"></div>
                                                    <div class="progress-bar bg-danger" style="width: @((double)am.Cancelled / total * 100)%" title="ملغي: @am.Cancelled"></div>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                </div>
                                assistantRankIndex++; // ✅ زيادة مؤشر الترتيب الفريد
                            }
                        </div>
                    </div>

                    <!-- عرض الرسم البياني - كما هو -->
                    <div id="assistantManagerChartView" class="d-none">
                        <div class="text-center">
                            <canvas id="assistantManagerChart" width="600" height="400"></canvas>
                        </div>
                    </div>

                </div>
            </div>
        </div>

        <!-- تبويب منسقي الموارد البشرية - محدث مع التوزيع الزمني الجديد -->
        <!-- تبويب منسقي الموارد البشرية - النسخة النهائية الكاملة -->
        <div class="tab-pane fade" id="hrCoordinators" role="tabpanel">
            <div class="card content-card-enhanced">
                <div class="card-header text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-users-cog me-2"></i>
                            إحصائيات منسقي الموارد البشرية
                        </h5>
                    </div>
                </div>
                <div class="card-body">
                    @if (Model.HRCoordinatorStatistics != null && Model.HRCoordinatorStatistics.Count > 0)
                    {
                        var coordStats = Model.HRCoordinatorStatistics;

                        // ✅ استخراج الإحصائيات العامة والمنسقين الفعليين
                        var globalStats = (OrderFlowCore.Application.DTOs.HRCoordinatorReportDto)null;
                        var actualCoordinators = new System.Collections.Generic.List<OrderFlowCore.Application.DTOs.HRCoordinatorReportDto>();

                        foreach (var coord in coordStats)
                        {
                            if (coord.CoordinatorName == "___GLOBAL_STATS___")
                            {
                                globalStats = coord;
                            }
                            else
                            {
                                actualCoordinators.Add(coord);
                            }
                        }

                        // 🏆 ترتيب المنسقين حسب الأداء (المنجز + التحويل المباشر)
                        var rankedCoordinators = actualCoordinators
                        .OrderByDescending(c => c.TotalCompleted)
                        .ThenByDescending(c => c.SuccessRate)
                        .ToList();

                        int coordCount = actualCoordinators.Count;
                        int coordTotalOperations = 0;
                        int coordTotalCompleted = 0;
                        int coordTotalIncoming = globalStats?.IncomingOrders ?? 0;
                        int coordTotalDirectToManager = 0;

                        // حساب الإحصائيات العامة مع معدل الإنجاز الفعلي
                        int coordTotalProcessed = 0; // ✨ الطلبات المُعالجة فعلياً (بدون "الطلبات القادمة")
                        int coordTotalCancelled = 0; // ✨ الطلبات الملغية
                        int coordTotalWorksDone = 0; // ✨ إجمالي الأعمال المنجزة
                        int coordTotalActualCompleted = 0; // ✨ الطلبات المُنجزة فعلياً (منجز + تحويل مباشر)
                        double coordTotalCompletionTime = 0;
                        int coordValidTimeEntries = 0;

                        foreach (var coord in actualCoordinators)
                        {
                            coordTotalOperations += coord.TotalOperations;
                            coordTotalCompleted += coord.TotalCompleted;
                            coordTotalDirectToManager += coord.DirectToManager;

                            // ✨ حساب الطلبات المُعالجة فعلياً (بدون "الطلبات القادمة")
                            coordTotalProcessed += (coord.Completed + coord.Restored + coord.Reassigned +
                            coord.DirectToManager + coord.Cancelled);

                            coordTotalCancelled += coord.Cancelled;
                            coordTotalWorksDone += coord.TotalWorksDone;

                            // ✨ حساب الطلبات المُنجزة فعلياً (النتائج الإيجابية)
                            coordTotalActualCompleted += (coord.Completed + coord.DirectToManager);

                            if (coord.AverageCompletionTime > 0)
                            {
                                coordTotalCompletionTime += coord.AverageCompletionTime;
                                coordValidTimeEntries++;
                            }
                        }

                        // ✅ حساب نسبة المعالجة العامة
                        // المعادلة: (الطلبات المُعالجة ÷ (الطلبات المُعالجة + الطلبات القادمة)) × 100
                        int coordTotalWithIncoming = coordTotalProcessed + coordTotalIncoming;
                        double coordOverallProcessingRate = coordTotalWithIncoming > 0 ?
                        (double)coordTotalProcessed / coordTotalWithIncoming * 100 : 0;

                        // ✅ متوسط زمن الإنجاز العام
                        string coordAverageCompletionTime = coordValidTimeEntries > 0 ?
                        (coordTotalCompletionTime / coordValidTimeEntries).ToString("F1") : "0.0";

                        // ✅ متوسط الأعمال لكل طلب العام
                        double coordOverallWorksPerOrder = coordTotalProcessed > 0 ?
                        (double)coordTotalWorksDone / coordTotalProcessed : 0;

                        // ✨ حساب معدل الإنجاز الفعلي العام
                        // المعادلة: (الطلبات المُنجزة ÷ الطلبات المُعالجة) × 100
                        double coordActualCompletionRate = coordTotalProcessed > 0 ?
                        (double)coordTotalActualCompleted / coordTotalProcessed * 100 : 0;

                        <!-- ✨ ملخص سريع مع الشرح البسيط للإحصائيات المهمة -->
                        <div class="d-flex justify-content-between align-items-stretch gap-2 flex-wrap mb-4">

                            <!-- 1️⃣ نسبة المعالجة - مع شرح -->
                            <div class="flex-fill" style="min-width: 120px; max-width: 160px;">
                                <div class="card stat-card-enhanced stat-card-primary text-white h-100"
                                     data-bs-toggle="tooltip"
                                     data-bs-placement="top"
                                     title="نسبة المعالجة: من كل 100 طلب يصل للقسم، كم تم معالجته؟ &#10;🟢 85%+ ممتاز | 🟡 75-84% جيد | 🔴 أقل من 75% يحتاج تحسين">
                                    <div class="card-body text-center p-2">
                                        <div class="stat-icon-enhanced"><i class="fas fa-chart-line"></i></div>
                                        <div class="stat-number-enhanced">@coordOverallProcessingRate.ToString("F1")%</div>
                                        <div class="stat-label-enhanced">نسبة المعالجة</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 2️⃣ معدل الإنجاز الفعلي - مع شرح -->
                            <div class="flex-fill" style="min-width: 120px; max-width: 160px;">
                                <div class="card stat-card-enhanced stat-card-success text-white h-100"
                                     data-bs-toggle="tooltip"
                                     data-bs-placement="top"
                                     title="معدل الإنجاز الفعلي: من الطلبات المُعالجة، كم نسبة النجاح الحقيقي؟ &#10;🎯 يشمل: المنجز + التحويل المباشر للمدير &#10;🟢 70%+ ممتاز | 🟡 60-69% جيد | 🔴 أقل من 60% يحتاج مراجعة">
                                    <div class="card-body text-center p-2">
                                        <div class="stat-icon-enhanced"><i class="fas fa-trophy"></i></div>
                                        <div class="stat-number-enhanced">@coordActualCompletionRate.ToString("F1")%</div>
                                        <div class="stat-label-enhanced">معدل الإنجاز الفعلي</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 3️⃣ متوسط زمن الإنجاز - مع شرح -->
                            <div class="flex-fill" style="min-width: 120px; max-width: 160px;">
                                <div class="card stat-card-enhanced stat-card-info text-white h-100"
                                     data-bs-toggle="tooltip"
                                     data-bs-placement="top"
                                     title="متوسط زمن الإنجاز: كم يوم عمل يحتاج لإنجاز الطلب الواحد؟ &#10;⚡ 0-1 يوم: فوري | 🚀 1-3 أيام: سريع | ⏰ 3-7 أيام: متوسط | 🐌 +7 أيام: بطيء">
                                    <div class="card-body text-center p-2">
                                        <div class="stat-icon-enhanced"><i class="fas fa-clock"></i></div>
                                        <div class="stat-number-enhanced">@coordAverageCompletionTime</div>
                                        <div class="stat-label-enhanced">متوسط زمن الإنجاز</div>
                                    </div>
                                </div>
                            </div>

                            <!-- 4️⃣ متوسط عمل/طلب - مع شرح -->
                            <div class="flex-fill" style="min-width: 120px; max-width: 160px;">
                                <div class="card stat-card-enhanced stat-card-warning text-white h-100"
                                     data-bs-toggle="tooltip"
                                     data-bs-placement="top"
                                     title="متوسط عمل/طلب: مؤشر تعقيد العمل - كم إجراء يحتاج كل طلب؟ &#10;🟢 1.0-1.2: بسيط ومباشر | 🟡 1.3-1.7: متوسط التعقيد | 🔴 1.8+: معقد ويحتاج مراجعات">
                                    <div class="card-body text-center p-2">
                                        <div class="stat-icon-enhanced"><i class="fas fa-calculator"></i></div>
                                        <div class="stat-number-enhanced">@coordOverallWorksPerOrder.ToString("F1")</div>
                                        <div class="stat-label-enhanced">متوسط عمل/طلب</div>
                                    </div>
                                </div>
                            </div>

                            <!-- باقي البطاقات بدون شرح (واضحة ومفهومة) -->
                            <div class="flex-fill" style="min-width: 120px; max-width: 160px;">
                                <div class="card stat-card-enhanced stat-card-primary text-white h-100">
                                    <div class="card-body text-center p-2">
                                        <div class="stat-icon-enhanced"><i class="fas fa-users-cog"></i></div>
                                        <div class="stat-number-enhanced">@coordCount</div>
                                        <div class="stat-label-enhanced">عدد المنسقين</div>
                                    </div>
                                </div>
                            </div>

                            <div class="flex-fill" style="min-width: 120px; max-width: 160px;">
                                <div class="card stat-card-enhanced stat-card-info text-white h-100">
                                    <div class="card-body text-center p-2">
                                        <div class="stat-icon-enhanced"><i class="fas fa-tasks"></i></div>
                                        <div class="stat-number-enhanced">@coordTotalOperations</div>
                                        <div class="stat-label-enhanced">إجمالي العمليات</div>
                                    </div>
                                </div>
                            </div>

                            <div class="flex-fill" style="min-width: 120px; max-width: 160px;">
                                <div class="card stat-card-enhanced stat-card-success text-white h-100">
                                    <div class="card-body text-center p-2">
                                        <div class="stat-icon-enhanced"><i class="fas fa-check-circle"></i></div>
                                        <div class="stat-number-enhanced">@coordTotalCompleted</div>
                                        <div class="stat-label-enhanced">إجمالي المنجز</div>
                                    </div>
                                </div>
                            </div>

                            <div class="flex-fill" style="min-width: 120px; max-width: 160px;">
                                <div class="card stat-card-enhanced stat-card-warning text-white h-100">
                                    <div class="card-body text-center p-2">
                                        <div class="stat-icon-enhanced"><i class="fas fa-arrow-right"></i></div>
                                        <div class="stat-number-enhanced">@coordTotalDirectToManager</div>
                                        <div class="stat-label-enhanced">تحويل مباشر للمدير</div>
                                    </div>
                                </div>
                            </div>

                            <div class="flex-fill" style="min-width: 120px; max-width: 160px;">
                                <div class="card stat-card-enhanced stat-card-secondary text-white h-100">
                                    <div class="card-body text-center p-2">
                                        <div class="stat-icon-enhanced"><i class="fas fa-inbox"></i></div>
                                        <div class="stat-number-enhanced">@coordTotalIncoming</div>
                                        <div class="stat-label-enhanced">الطلبات القادمة</div>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <!-- 🏆 عرض الجدول مع نظام الترتيب البسيط -->
                        <div id="hrCoordinatorTableView">
                            <div class="hr-coordinator-list" style="max-height: 600px; overflow-y: auto;">
                                @{
                                    var rankIndex = 1;
                                }
                                @foreach (var coordinator in rankedCoordinators)
                                {
                                    <div class='hr-coordinator-card stage-item-enhanced stage-success mb-3'
                                         data-coordinator='@coordinator.CoordinatorName.ToLower()'
                                         data-ranking='@rankIndex'>

                                        <div class="d-flex align-items-center">
                                            <div class="stage-icon-enhanced stage-icon-success">
                                                <i class="fas fa-users-cog"></i>
                                            </div>

                                            <div class="flex-grow-1">
                                                <div class="row align-items-center">
                                                    <!-- اسم المنسق مع نظام الترتيب البسيط -->
                                                    <div class="col-md-3">
                                                        <div class="d-flex align-items-center">
                                                            <!-- 🏆 نظام الترتيب البسيط مع الألوان الصحيحة -->
                                                            @if (rankIndex == 1)
                                                            {
                                                                <span class="badge me-2 fs-5 p-2" style="background: linear-gradient(45deg, #FFD700, #FFA500); color: #8B4513; border: 2px solid #DAA520;">🥇</span>
                                                            }
                                                            else if (rankIndex == 2)
                                                            {
                                                                <span class="badge me-2 fs-5 p-2" style="background: linear-gradient(45deg, #C0C0C0, #A9A9A9); color: #2F4F4F; border: 2px solid #808080;">🥈</span>
                                                            }
                                                            else if (rankIndex == 3)
                                                            {
                                                                <span class="badge me-2 fs-5 p-2" style="background: linear-gradient(45deg, #CD7F32, #D2691E); color: #FFFFFF; border: 2px solid #A0522D;">🥉</span>
                                                            }
                                                            else if (rankIndex <= 5)
                                                            {
                                                                <span class="badge bg-primary me-2 fs-6 p-2">#@rankIndex</span>
                                                            }
                                                            else
                                                            {
                                                                <span class="badge bg-secondary me-2 fs-6 p-1">#@rankIndex</span>
                                                            }

                                                            <div>
                                                                <h6 class="mb-1">@coordinator.CoordinatorName</h6>
                                                                <small class="text-muted">
                                                                    تلقائي: @coordinator.AutomaticTransfers | يدوي: @coordinator.ManualTransfers
                                                                </small>
                                                                <br>
                                                                <small class="text-muted">
                                                                    إجمالي العمليات: @coordinator.TotalOperations |
                                                                    تلقائي: @coordinator.AutoTransferRateDisplay
                                                                </small>
                                                                <!-- ✨ إضافة إجمالي الأعمال المنجزة -->
                                                                <br>
                                                                <small class="text-success fw-bold">
                                                                    💼 إجمالي الأعمال المنجزة: @coordinator.TotalWorksDone عمل
                                                                </small>
                                                                <!-- ✨ إضافة الطلبات متعددة المراحل إذا وجدت -->
                                                                @if (coordinator.MultiStageOrders > 0)
                                                                {
                                                                    <br>
                                                                    <small class="text-warning">
                                                                        🔄 طلبات متعددة المراحل: @coordinator.MultiStageOrders طلب
                                                                    </small>
                                                                }
                                                                <!-- 🔄 الاتجاه: اتجاه محدث -->
                                                                <br>
                                                                <small class="text-info">
                                                                    🔄 الاتجاه: @(string.IsNullOrEmpty(coordinator.TrendDirection) ? "مستقر" : coordinator.TrendDirection)
                                                                    @(coordinator.TrendDirection == "متزايد" ? "↗️" : coordinator.TrendDirection == "متناقص" ? "↘️" : "➡️")
                                                                </small>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- الإحصائيات التفصيلية -->
                                                    <div class="col-md-7">
                                                        <div class="row text-center">
                                                            <div class="col">
                                                                <div class="stat-mini">
                                                                    <div class="stat-mini-number text-success">@coordinator.Completed</div>
                                                                    <div class="stat-mini-label">✅ منجز</div>
                                                                </div>
                                                            </div>
                                                            <div class="col">
                                                                <div class="stat-mini">
                                                                    <div class="stat-mini-number text-info">@coordinator.Restored</div>
                                                                    <div class="stat-mini-label">↩️ مستعاد</div>
                                                                </div>
                                                            </div>
                                                            <div class="col">
                                                                <div class="stat-mini">
                                                                    <div class="stat-mini-number text-warning">@coordinator.Reassigned</div>
                                                                    <div class="stat-mini-label">🔄 معاد تكليف</div>
                                                                </div>
                                                            </div>
                                                            <div class="col">
                                                                <div class="stat-mini">
                                                                    <div class="stat-mini-number text-primary">@coordinator.DirectToManager</div>
                                                                    <div class="stat-mini-label">📤 تحويل مباشر</div>
                                                                </div>
                                                            </div>
                                                            <div class="col">
                                                                <div class="stat-mini">
                                                                    <div class="stat-mini-number text-danger">@coordinator.NeedsAction</div>
                                                                    <div class="stat-mini-label">⚠️ يتطلب إجراء</div>
                                                                </div>
                                                            </div>
                                                            <div class="col">
                                                                <div class="stat-mini">
                                                                    <div class="stat-mini-number text-secondary">@coordinator.Cancelled</div>
                                                                    <div class="stat-mini-label">❌ ملغي</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- ✅ الجانب الأيسر: التوزيع الزمني (مثل المديرين) -->
                                                    <div class="col-md-2 text-center">
                                                        <div class="mb-2">
                                                            <small class="text-muted fw-bold d-block mb-1">📅 التوزيع الزمني</small>
                                                            <div>
                                                                <span class="badge badge-sm bg-primary me-1" title="إنجازات هذا الشهر">
                                                                    📅 شهر: @coordinator.ThisMonthOperations
                                                                </span>
                                                                <span class="badge badge-sm bg-info me-1" title="إنجازات آخر 3 أشهر">
                                                                    📊 ٣ أشهر: @coordinator.Last3MonthsOperations
                                                                </span>
                                                                <span class="badge badge-sm bg-success me-1" title="إنجازات آخر 6 أشهر">
                                                                    📈 ٦ أشهر: @coordinator.Last6MonthsOperations
                                                                </span>
                                                                <span class="badge badge-sm bg-secondary" title="إنجازات آخر سنة">
                                                                    🏆 سنة: @coordinator.LastYearOperations
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- شريط التقدم -->
                                                @if (coordinator.TotalOperations > 0)
                                                {
                                                    <div class="progress progress-enhanced mt-3" style="height: 8px;">
                                                        <div class="progress-bar bg-success"
                                                             style="width: @((double)coordinator.Completed / coordinator.TotalOperations * 100)%"
                                                             title="منجز: @coordinator.Completed"></div>
                                                        <div class="progress-bar bg-info"
                                                             style="width: @((double)coordinator.Restored / coordinator.TotalOperations * 100)%"
                                                             title="مستعاد: @coordinator.Restored"></div>
                                                        <div class="progress-bar bg-warning"
                                                             style="width: @((double)coordinator.Reassigned / coordinator.TotalOperations * 100)%"
                                                             title="معاد تكليف: @coordinator.Reassigned"></div>
                                                        <div class="progress-bar bg-primary"
                                                             style="width: @((double)coordinator.DirectToManager / coordinator.TotalOperations * 100)%"
                                                             title="تحويل مباشر: @coordinator.DirectToManager"></div>
                                                        <div class="progress-bar bg-danger"
                                                             style="width: @((double)coordinator.NeedsAction / coordinator.TotalOperations * 100)%"
                                                             title="يتطلب إجراء: @coordinator.NeedsAction"></div>
                                                        <div class="progress-bar bg-secondary"
                                                             style="width: @((double)coordinator.Cancelled / coordinator.TotalOperations * 100)%"
                                                             title="ملغي: @coordinator.Cancelled"></div>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    rankIndex++;
                                }
                            </div>
                        </div>

                        <!-- عرض الرسم البياني -->
                        <div id="hrCoordinatorChartView" class="d-none">
                            <div class="text-center">
                                <canvas id="hrCoordinatorChart" width="600" height="400"></canvas>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد بيانات لمنسقي الموارد البشرية</h5>
                            <p class="text-muted">لم يتم العثور على إحصائيات لمنسقي الموارد البشرية حتى الآن.</p>
                        </div>
                    }
                </div>
            </div>
        </div>



        <!-- تبويب المشرفين - محسن ومتكامل بالكامل -->
        <div class="tab-pane fade" id="supervisorStats" role="tabpanel">
            <div class="card content-card-enhanced">
                <div class="card-header text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-user-check me-2"></i>
                            إحصائيات المشرفين التفصيلية المحسنة (مُرتبة)
                        </h5>
                        <small class="text-light opacity-75">
                            مُرتب حسب: الكفاءة → الإنجاز → المعالجة → السرعة
                        </small>
                    </div>
                </div>
                <div class="card-body">
                    @if (Model.SupervisorStatistics != null && Model.SupervisorStatistics.Count > 0)
                    {
                        var superStats = Model.SupervisorStatistics;
                        int superCount = superStats.Count;

                        // حساب جميع المتغيرات المطلوبة
                        int superTotalRequests = 0;
                        int superTotalCompleted = 0;
                        int superTotalProcessedOrders = 0;
                        int superTotalClicks = 0;
                        int superTotalPending = 0;
                        double totalEfficiencyScore = 0;

                        // متغيرات فئات الأداء
                        int excellentCount = 0;
                        int veryGoodCount = 0;
                        int goodCount = 0;
                        int needsImprovementCount = 0;
                        int topPerformersCount = 0;
                        int highPerformersCount = 0;
                        int activeCount = 0;
                        int lowEfficiencyCount = 0;

                        // حساب كل الإحصائيات
                        foreach (var supervisor in superStats)
                        {
                            int supervisorTotal = supervisor.TotalOrders;
                            int supervisorProcessedOrders = supervisor.TotalCompletedWork;
                            int supervisorTotalClicks = supervisor.TotalCompletedWork;

                            superTotalRequests += supervisorTotal;
                            superTotalCompleted += supervisor.Completed;
                            superTotalProcessedOrders += supervisorProcessedOrders;
                            superTotalClicks += supervisorTotalClicks;
                            superTotalPending += supervisor.UnderExecution;
                            totalEfficiencyScore += supervisor.EfficiencyScore;

                            // فئات الأداء
                            if (supervisor.EfficiencyScore >= 85) excellentCount++;
                            else if (supervisor.EfficiencyScore >= 70) veryGoodCount++;
                            else if (supervisor.EfficiencyScore >= 55) goodCount++;
                            else needsImprovementCount++;

                            if (supervisor.IsTopPerformer && supervisor.IsHighPerformer) topPerformersCount++;
                            if (supervisor.IsHighPerformer) highPerformersCount++;
                            if (supervisor.SupervisorTier == "نشط") activeCount++;
                            if (supervisor.EfficiencyScore < 40) lowEfficiencyCount++;
                        }

                        double avgEfficiencyScore = superCount > 0 ? totalEfficiencyScore / superCount : 0;

                        // أفضل 5 مشرفين
                        var top5Supervisors = new List<OrderFlowCore.Application.DTOs.SupervisorStatisticsDto>();
                        for (int i = 0; i < Math.Min(5, superStats.Count); i++)
                        {
                            top5Supervisors.Add(superStats[i]);
                        }

                        <!-- ملخص محسن مع معايير الترتيب -->
                        <div class="row mb-4 text-center">
                            <div class="col-md-2 mb-3">
                                <div class="card stat-card-enhanced stat-card-primary text-white h-100">
                                    <div class="card-body">
                                        <div class="stat-icon-enhanced"><i class="fas fa-users"></i></div>
                                        <div class="stat-number-enhanced">@superCount</div>
                                        <div class="stat-label-enhanced">عدد المشرفين</div>
                                        <small class="text-light opacity-75">مُرتب حسب الكفاءة</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 mb-3">
                                <div class="card stat-card-enhanced stat-card-info text-white h-100">
                                    <div class="card-body">
                                        <div class="stat-icon-enhanced"><i class="fas fa-tasks"></i></div>
                                        <div class="stat-number-enhanced">@superTotalRequests</div>
                                        <div class="stat-label-enhanced">إجمالي الطلبات</div>
                                        <small class="text-light opacity-75">جميع الطلبات</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 mb-3">
                                <div class="card stat-card-enhanced stat-card-success text-white h-100">
                                    <div class="card-body">
                                        <div class="stat-icon-enhanced"><i class="fas fa-check-double"></i></div>
                                        <div class="stat-number-enhanced">@superTotalProcessedOrders</div>
                                        <div class="stat-label-enhanced">الأعمال المُعالجة</div>
                                        <small class="text-light opacity-75">بدون المعلق</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 mb-3">
                                <div class="card stat-card-enhanced stat-card-warning text-white h-100">
                                    <div class="card-body">
                                        <div class="stat-icon-enhanced"><i class="fas fa-mouse-pointer"></i></div>
                                        <div class="stat-number-enhanced">@superTotalClicks</div>
                                        <div class="stat-label-enhanced">إجمالي الجهود</div>
                                        <small class="text-light opacity-75">النقرات الفعلية</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 mb-3">
                                <div class="card stat-card-enhanced stat-card-warning text-white h-100">
                                    <div class="card-body">
                                        <div class="stat-icon-enhanced"><i class="fas fa-clock"></i></div>
                                        <div class="stat-number-enhanced">@superTotalPending</div>
                                        <div class="stat-label-enhanced">المعلقة</div>
                                        <small class="text-light opacity-75">تحت التنفيذ</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 mb-3">
                                <div class="card stat-card-enhanced stat-card-gradient text-white h-100">
                                    <div class="card-body">
                                        <div class="stat-icon-enhanced"><i class="fas fa-chart-line"></i></div>
                                        <div class="stat-number-enhanced">@avgEfficiencyScore.ToString("F1")</div>
                                        <div class="stat-label-enhanced">متوسط الكفاءة</div>
                                        <small class="text-light opacity-75">
                                            @((superTotalRequests > 0 ? (double)superTotalProcessedOrders / superTotalRequests * 100 : 0).ToString("F1"))% معالجة
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- مؤشرات الأداء العامة -->
                        <div class="alert alert-info mb-4">
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <i class="fas fa-medal text-warning me-2"></i>
                                    <strong>المتميزون:</strong> @topPerformersCount
                                </div>
                                <div class="col-md-3">
                                    <i class="fas fa-star text-success me-2"></i>
                                    <strong>عالي الأداء:</strong> @highPerformersCount
                                </div>
                                <div class="col-md-3">
                                    <i class="fas fa-chart-line text-primary me-2"></i>
                                    <strong>نشطون:</strong> @activeCount
                                </div>
                                <div class="col-md-3">
                                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                    <strong>يحتاج تحسين:</strong> @lowEfficiencyCount
                                </div>
                            </div>
                        </div>

                        <!-- قائمة المشرفين المُرتبة -->
                        <div id="supervisorTableView">
                            <div class="supervisor-list" style="max-height: 700px; overflow-y: auto;">
                                @foreach (var supervisor in superStats)
                                {
                                    var totalSupervisor = supervisor.TotalOrders;
                                    var processedOrders = supervisor.TotalCompletedWork;
                                    var totalClicks = supervisor.TotalCompletedWork;
                                    var processingRate = totalSupervisor > 0 ? (double)processedOrders / totalSupervisor * 100 : 0;
                                    var pendingOrders = supervisor.UnderExecution;
                                    var averageClicksPerOrder = processedOrders > 0 ? (double)totalClicks / processedOrders : 0;

                                    <div class='supervisor-card stage-item-enhanced stage-dark mb-3 @(supervisor.IsTopPerformer ? "border-warning" : "") @(supervisor.IsHighPerformer ? "border-success" : "")'
                                         data-supervisor='@supervisor.SupervisorName.ToLower()'
                                         data-ranking='@supervisor.Ranking'
                                         data-efficiency='@supervisor.EfficiencyScore.ToString("F1")'
                                         data-completion='@supervisor.CompletionPercentage.ToString("F1")'
                                         data-processing='@supervisor.ProcessingRate.ToString("F1")'>

                                        <div class="d-flex align-items-center">
                                            <!-- أيقونة الترتيب المحسنة -->
                                            <div class="stage-icon-enhanced stage-icon-dark me-3">
                                                @if (supervisor.Ranking == 1)
                                                {
                                                    <i class="fas fa-trophy text-warning"></i>
                                                }
                                                else if (supervisor.Ranking <= 3)
                                                {
                                                    <i class="fas fa-medal text-info"></i>
                                                }
                                                else if (supervisor.Ranking <= 5)
                                                {
                                                    <i class="fas fa-star text-primary"></i>
                                                }
                                                else
                                                {
                                                    <i class="fas fa-user-check"></i>
                                                }
                                            </div>

                                            <div class="flex-grow-1">
                                                <div class="row align-items-center">
                                                    <!-- معلومات المشرف الأساسية -->
                                                    <div class="col-md-4">
                                                        <div class="d-flex align-items-center">
                                                            <!-- شارة الترتيب المحسنة -->
                                                            @if (supervisor.Ranking == 1)
                                                            {
                                                                <span class="badge me-2 fs-4 p-2 shadow" style="background: linear-gradient(45deg, #FFD700, #FFA500); color: #8B4513;">🥇 #1</span>
                                                            }
                                                            else if (supervisor.Ranking == 2)
                                                            {
                                                                <span class="badge me-2 fs-5 p-2 shadow" style="background: linear-gradient(45deg, #C0C0C0, #A9A9A9); color: #2F4F4F;">🥈 #2</span>
                                                            }
                                                            else if (supervisor.Ranking == 3)
                                                            {
                                                                <span class="badge me-2 fs-5 p-2 shadow" style="background: linear-gradient(45deg, #CD7F32, #D2691E); color: #FFFFFF;">🥉 #3</span>
                                                            }
                                                            else if (supervisor.Ranking <= 5)
                                                            {
                                                                <span class="badge bg-primary me-2 fs-6 p-2 shadow">⭐ #@supervisor.Ranking</span>
                                                            }
                                                            else if (supervisor.Ranking <= 10)
                                                            {
                                                                <span class="badge bg-info me-2 fs-6 p-2">📈 #@supervisor.Ranking</span>
                                                            }
                                                            else
                                                            {
                                                                <span class="badge bg-secondary me-2 fs-6 p-1">#@supervisor.Ranking</span>
                                                            }

                                                            <div>
                                                                <div class="d-flex align-items-center mb-1">
                                                                    <h6 class="mb-0 me-2">@supervisor.SupervisorName</h6>
                                                                    <!-- شارة الفئة -->
                                                                    <span class="badge @(supervisor.SupervisorTier == "متميز" ? "bg-warning" :
                                                            supervisor.SupervisorTier == "نشط" ? "bg-success" :
                                                            supervisor.SupervisorTier == "عادي" ? "bg-info" : "bg-secondary") badge-sm">
                                                                        @supervisor.SupervisorTier
                                                                    </span>
                                                                </div>

                                                                <!-- نقاط الكفاءة -->
                                                                <div class="mb-1">
                                                                    <small class="text-primary fw-bold d-block">
                                                                        نقاط الكفاءة: @supervisor.EfficiencyScore.ToString("F1")
                                                                        <span class="badge @(supervisor.PerformanceLevel == "ممتاز" ? "bg-success" :
                                                                supervisor.PerformanceLevel == "جيد جداً" ? "bg-info" :
                                                                supervisor.PerformanceLevel == "جيد" ? "bg-primary" :
                                                                supervisor.PerformanceLevel == "متوسط" ? "bg-warning" : "bg-danger") badge-sm ms-1">
                                                                            @supervisor.PerformanceLevel
                                                                        </span>
                                                                    </small>
                                                                </div>

                                                                <!-- المؤشرات الأساسية -->
                                                                <small class="text-success fw-bold d-block">
                                                                    إنجاز مكتمل: @supervisor.CompletionPercentage.ToString("F1")%
                                                                </small>
                                                                <small class="text-info fw-bold d-block">
                                                                    نسبة المعالجة: @supervisor.ProcessingRate.ToString("F1")%
                                                                </small>
                                                                <small class="text-muted d-block">
                                                                    متوسط الزمن: @supervisor.AverageCompletionTime.ToString("F1") يوم
                                                                </small>
                                                                <small class="text-primary d-block">
                                                                    إجمالي الأعمال: @totalClicks
                                                                    <span class="text-secondary">(@averageClicksPerOrder.ToString("F1") نقرة/طلب)</span>
                                                                </small>
                                                                <small class="text-info d-block">
                                                                    الاتجاه: @supervisor.TrendDirectionDisplay
                                                                </small>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- الإحصائيات التفصيلية -->
                                                    <div class="col-md-6">
                                                        <div class="row text-center">
                                                            <div class="col-3">
                                                                <div class="stat-mini">
                                                                    <div class="stat-mini-number text-warning">@supervisor.UnderExecution</div>
                                                                    <div class="stat-mini-label text-muted">معلق</div>
                                                                    <small class="text-muted">@((totalSupervisor > 0 ? (double)supervisor.UnderExecution / totalSupervisor * 100 : 0).ToString("F0"))%</small>
                                                                </div>
                                                            </div>
                                                            <div class="col-3">
                                                                <div class="stat-mini">
                                                                    <div class="stat-mini-number text-success">@supervisor.Completed</div>
                                                                    <div class="stat-mini-label text-success">مكتمل</div>
                                                                    <small class="text-success">@((totalSupervisor > 0 ? (double)supervisor.Completed / totalSupervisor * 100 : 0).ToString("F0"))%</small>
                                                                </div>
                                                            </div>
                                                            <div class="col-3">
                                                                <div class="stat-mini">
                                                                    <div class="stat-mini-number text-info">@supervisor.NeedsAction</div>
                                                                    <div class="stat-mini-label text-info">يحتاج إجراء</div>
                                                                    <small class="text-info">@((totalSupervisor > 0 ? (double)supervisor.NeedsAction / totalSupervisor * 100 : 0).ToString("F0"))%</small>
                                                                </div>
                                                            </div>
                                                            <div class="col-3">
                                                                <div class="stat-mini">
                                                                    <div class="stat-mini-number text-danger">@supervisor.Returned</div>
                                                                    <div class="stat-mini-label text-danger">معاد</div>
                                                                    <small class="text-danger">@((totalSupervisor > 0 ? (double)supervisor.Returned / totalSupervisor * 100 : 0).ToString("F0"))%</small>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- الإحصائيات الزمنية -->
                                                    <div class="col-md-2 text-center">
                                                        <div class="mb-2">
                                                            <small class="text-muted fw-bold d-block mb-1">التوزيع الزمني</small>
                                                            <div>
                                                                <span class="badge badge-sm bg-primary me-1" title="إنجازات هذا الشهر">
                                                                    @supervisor.ThisMonthOperations
                                                                </span>
                                                                <span class="badge badge-sm bg-info me-1" title="إنجازات آخر 3 أشهر">
                                                                    @supervisor.Last3MonthsOperations
                                                                </span>
                                                                <span class="badge badge-sm bg-success me-1" title="إنجازات آخر 6 أشهر">
                                                                    @supervisor.Last6MonthsOperations
                                                                </span>
                                                                <span class="badge badge-sm bg-secondary" title="إنجازات آخر سنة">
                                                                    @supervisor.LastYearOperations
                                                                </span>
                                                            </div>

                                                            <!-- معدلات النشاط -->
                                                            @if (supervisor.MonthlyActivityRate > 0)
                                                            {
                                                                <div class="mt-2">
                                                                    <small class="text-primary d-block">الشهري: @supervisor.MonthlyActivityRateDisplay</small>
                                                                </div>
                                                            }
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- شريط التقدم المحسن -->
                                                @if (totalSupervisor > 0)
                                                {
                                                    <div class="mt-3">
                                                        <div class="progress progress-enhanced mb-2" style="height: 12px;">
                                                            <div class="progress-bar bg-success"
                                                                 style="width: @((double)supervisor.Completed / totalSupervisor * 100)%"
                                                                 title="مكتمل: @supervisor.Completed (@((double)supervisor.Completed / totalSupervisor * 100).ToString(" F1")%)"></div>
                                                            <div class="progress-bar bg-info"
                                                                 style="width: @((double)supervisor.NeedsAction / totalSupervisor * 100)%"
                                                                 title="يحتاج إجراءات: @supervisor.NeedsAction (@((double)supervisor.NeedsAction / totalSupervisor * 100).ToString(" F1")%)"></div>
                                                            <div class="progress-bar bg-danger"
                                                                 style="width: @((double)supervisor.Returned / totalSupervisor * 100)%"
                                                                 title="معاد: @supervisor.Returned (@((double)supervisor.Returned / totalSupervisor * 100).ToString(" F1")%)"></div>
                                                            <div class="progress-bar bg-warning"
                                                                 style="width: @((double)supervisor.UnderExecution / totalSupervisor * 100)%"
                                                                 title="معلق: @supervisor.UnderExecution (@((double)supervisor.UnderExecution / totalSupervisor * 100).ToString(" F1")%)"></div>
                                                        </div>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>


                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد بيانات للمشرفين</h5>
                            <p class="text-muted">لم يتم العثور على إحصائيات للمشرفين حتى الآن.</p>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- تبويب مديري الموارد البشرية - مُصحح بالكامل -->
        <div class="tab-pane fade" id="hrManagers" role="tabpanel">
            <div class="card content-card-enhanced">
                <div class="card-header text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-user-shield me-2"></i>
                            إحصائيات مديري الموارد البشرية
                        </h5>
                    </div>
                </div>
                <div class="card-body">
                    @if (Model.HRManagerStatistics != null && Model.HRManagerStatistics.Count > 0)
                    {
                        var hrManagerStats = Model.HRManagerStatistics;
                        var rankedManagers = new System.Collections.Generic.List<OrderFlowCore.Application.DTOs.HRManagerReportDto>();

                        // ✅ إصلاح Lambda Expression - طريقة آمنة
                        foreach (var manager in hrManagerStats)
                        {
                            rankedManagers.Add(manager);
                        }

                        // ✅ ترتيب مُصحح - بناءً على DTO الجديد
                        rankedManagers = rankedManagers
                        .OrderByDescending(m => m.TotalOrdersProcessed) // ✅ مُصحح: الطلبات المُعالجة
                        .ThenByDescending(m => m.TotalWorksDone)        // ✅ الأعمال الفعلية
                        .ThenByDescending(m => m.ProcessingRate)        // ✅ نسبة المعالجة
                        .ToList();

                        int hrManagerCount = hrManagerStats.Count;
                        int hrManagerTotalUnderExecution = 0;
                        int hrManagerTotalCompleted = 0;
                        int hrManagerTotalStatusChanged = 0; // ✅ إضافة
                        int hrManagerTotalOrdersProcessed = 0; // ✅ مُصحح: الطلبات المُعالجة
                        int hrManagerTotalReturned = 0;
                        int hrManagerTotalCancelled = 0;
                        int hrManagerTotalWorksDone = 0;
                        double hrManagerTotalCompletionTime = 0;
                        int hrManagerValidTimeEntries = 0;

                        // ✅ حساب الإجماليات مُصحح
                        foreach (var manager in hrManagerStats)
                        {
                            hrManagerTotalUnderExecution += manager.UnderExecution;
                            hrManagerTotalCompleted += manager.Completed;
                            hrManagerTotalStatusChanged += manager.StatusChanged; // ✅ إضافة
                            hrManagerTotalOrdersProcessed += manager.TotalOrdersProcessed; // ✅ مُصحح
                            hrManagerTotalReturned += manager.Returned;
                            hrManagerTotalCancelled += manager.Cancelled;
                            hrManagerTotalWorksDone += manager.TotalWorksDone;

                            if (manager.AverageCompletionTime > 0)
                            {
                                hrManagerTotalCompletionTime += manager.AverageCompletionTime;
                                hrManagerValidTimeEntries++;
                            }
                        }

                        // ✅ حساب الإجماليات للعرض
                        int hrManagerTotalOperationsForDisplay = hrManagerTotalUnderExecution + hrManagerTotalCompleted + hrManagerTotalStatusChanged + hrManagerTotalReturned + hrManagerTotalCancelled;
                        string hrManagerAverageCompletionTime = hrManagerValidTimeEntries > 0 ? (hrManagerTotalCompletionTime / hrManagerValidTimeEntries).ToString("F1") : "0.0";
                        double overallSuccessRate = hrManagerTotalOperationsForDisplay > 0 ? (double)hrManagerTotalOrdersProcessed / hrManagerTotalOperationsForDisplay * 100 : 0;
                        double overallWorksPerOrder = hrManagerTotalOrdersProcessed > 0 ? (double)hrManagerTotalWorksDone / hrManagerTotalOrdersProcessed : 0;

                        <!-- ملخص سريع مُحدث -->
                        <div class="row mb-4">
                            <div class="col-lg-2 col-md-4 mb-3">
                                <div class="card stat-card-enhanced stat-card-primary text-white h-100">
                                    <div class="card-body text-center">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-user-shield"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@hrManagerCount</div>
                                        <div class="stat-label-enhanced">عدد المديرين</div>
                                    </div>
                                </div>
                            </div>
                            <!-- ✅ محذوف: إجمالي العمليات -->
                            <!-- ✅ إجمالي الأعمال الفعلية -->
                            <div class="col-lg-2 col-md-4 mb-3">
                                <div class="card stat-card-enhanced stat-card-warning text-white h-100">
                                    <div class="card-body text-center">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-briefcase"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@hrManagerTotalWorksDone</div>
                                        <div class="stat-label-enhanced">إجمالي الأعمال</div>
                                        <small class="text-light opacity-75">النقرات الفعلية</small>
                                    </div>
                                </div>
                            </div>

                            <!-- ✅ إجمالي الطلبات المُعالجة -->
                            <div class="col-lg-2 col-md-4 mb-3">
                                <div class="card stat-card-enhanced stat-card-success text-white h-100">
                                    <div class="card-body text-center">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-check-double"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@hrManagerTotalOrdersProcessed</div>
                                        <div class="stat-label-enhanced">طلبات معالجة</div>
                                        <small class="text-light opacity-75">طلبات مُنجزة</small>
                                    </div>
                                </div>
                            </div>

                            <!-- ✅ متوسط الأعمال لكل طلب -->
                            <div class="col-lg-2 col-md-4 mb-3">
                                <div class="card stat-card-enhanced stat-card-info text-white h-100">
                                    <div class="card-body text-center">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-chart-bar"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@overallWorksPerOrder.ToString("F1")</div>
                                        <div class="stat-label-enhanced">متوسط عمل/طلب</div>
                                        <small class="text-light opacity-75">مؤشر التعقيد</small>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-2 col-md-4 mb-3">
                                <div class="card stat-card-enhanced stat-card-warning text-white h-100">
                                    <div class="card-body text-center">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-hourglass-half"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@hrManagerTotalUnderExecution</div>
                                        <div class="stat-label-enhanced">تحت التنفيذ</div>
                                    </div>
                                </div>
                            </div>

                            <!-- ✅ نسبة المعالجة العامة -->
                            <div class="col-lg-2 col-md-4 mb-3">
                                <div class="card stat-card-enhanced stat-card-gradient text-white h-100">
                                    <div class="card-body text-center">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-chart-line"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@overallSuccessRate.ToString("F1")%</div>
                                        <div class="stat-label-enhanced">نسبة المعالجة</div>
                                        <small class="text-light opacity-75">متوسط الإنجاز: @hrManagerAverageCompletionTime يوم</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- عرض الجدول المُصحح -->
                        <div id="hrManagerTableView">
                            <div class="hr-manager-list" style="max-height: 600px; overflow-y: auto;">
                                @{
                                    var rankIndex = 1;
                                }
                                @foreach (var manager in rankedManagers)
                                {
                                    // ✅ الإحصائيات المُصححة
                                    var totalManager = manager.UnderExecution + manager.Completed + manager.StatusChanged + manager.Returned + manager.Cancelled;
                                    var processedOrders = manager.TotalOrdersProcessed; // ✅ مُصحح
                                    var totalWorks = manager.TotalWorksDone;
                                    var processingRate = manager.ProcessingRate;
                                    var worksPerOrder = manager.WorksPerOrder;

                                    <div class='hr-manager-card stage-item-enhanced stage-info mb-3'
                                         data-manager='@manager.ManagerName.ToLower()'
                                         data-ranking='@rankIndex'
                                         data-processed='@processedOrders'
                                         data-works='@totalWorks'>

                                        <div class="d-flex align-items-center">
                                            <div class="stage-icon-enhanced stage-icon-info">
                                                <i class="fas fa-user-shield"></i>
                                            </div>

                                            <div class="flex-grow-1">
                                                <div class="row align-items-center">
                                                    <!-- 🎯 الجانب الأيمن: اسم المدير مع الإحصائيات المهمة -->
                                                    <div class="col-md-4">
                                                        <div class="d-flex align-items-center">
                                                            <!-- 🏆 نظام الترتيب -->
                                                            @if (rankIndex == 1)
                                                            {
                                                                <span class="badge me-2 fs-5 p-2" style="background: linear-gradient(45deg, #FFD700, #FFA500); color: #8B4513;">🥇</span>
                                                            }
                                                            else if (rankIndex == 2)
                                                            {
                                                                <span class="badge me-2 fs-5 p-2" style="background: linear-gradient(45deg, #C0C0C0, #A9A9A9); color: #2F4F4F;">🥈</span>
                                                            }
                                                            else if (rankIndex == 3)
                                                            {
                                                                <span class="badge me-2 fs-5 p-2" style="background: linear-gradient(45deg, #CD7F32, #D2691E); color: #FFFFFF;">🥉</span>
                                                            }
                                                            else if (rankIndex <= 5)
                                                            {
                                                                <span class="badge bg-primary me-2 fs-6 p-2">#@rankIndex</span>
                                                            }
                                                            else
                                                            {
                                                                <span class="badge bg-secondary me-2 fs-6 p-1">#@rankIndex</span>
                                                            }

                                                            <div>
                                                                <h6 class="mb-1">@manager.ManagerName</h6>
                                                                <small class="text-muted d-block">
                                                                    ⏱️ زمن الإنجاز: @manager.AverageCompletionTime.ToString("F1") يوم
                                                                </small>
                                                                <!-- ✅ نسبة المعالجة -->
                                                                <small class="text-success fw-bold d-block">
                                                                    📈 نسبة المعالجة: @processingRate.ToString("F1")%
                                                                </small>
                                                                <!-- ❌ حذف: إجمالي العمليات -->
                                                                <!-- ✅ الإحصائيات الثلاث المهمة -->
                                                                <small class="text-primary fw-bold d-block">
                                                                    🏆 الطلبات المُعالجة: @processedOrders
                                                                </small>
                                                                <small class="text-warning fw-bold d-block">
                                                                    💼 إجمالي الأعمال: @totalWorks
                                                                </small>
                                                                <small class="text-secondary d-block">
                                                                    ⚡ متوسط: @worksPerOrder.ToString("F1") عمل/طلب
                                                                </small>

                                                                <!-- 🔄 اتجاه الأداء -->
                                                                @if (!string.IsNullOrEmpty(manager.TrendDirection) && manager.TrendDirection != "مستقر")
                                                                {
                                                                    <small class="text-info d-block">
                                                                        🔄 الاتجاه: @manager.TrendDirectionDisplay
                                                                    </small>
                                                                }
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- 📊 الوسط: الإحصائيات التفصيلية -->
                                                    <div class="col-md-6">
                                                        <div class="row text-center">
                                                            <div class="col">
                                                                <div class="stat-mini">
                                                                    <div class="stat-mini-number text-warning">@manager.UnderExecution</div>
                                                                    <div class="stat-mini-label text-muted">⏳ تحت التنفيذ</div>
                                                                </div>
                                                            </div>
                                                            <div class="col">
                                                                <div class="stat-mini">
                                                                    <div class="stat-mini-number text-success">@manager.Completed</div>
                                                                    <div class="stat-mini-label text-success">✅ منجز</div>
                                                                </div>
                                                            </div>
                                                            <div class="col">
                                                                <div class="stat-mini">
                                                                    <div class="stat-mini-number text-primary">@manager.StatusChanged</div>
                                                                    <div class="stat-mini-label text-primary">🔄 تغيير حالة</div>
                                                                </div>
                                                            </div>
                                                            <div class="col">
                                                                <div class="stat-mini">
                                                                    <div class="stat-mini-number text-info">@manager.Returned</div>
                                                                    <div class="stat-mini-label text-info">↩️ معاد</div>
                                                                </div>
                                                            </div>
                                                            <div class="col">
                                                                <div class="stat-mini">
                                                                    <div class="stat-mini-number text-danger">@manager.Cancelled</div>
                                                                    <div class="stat-mini-label text-danger">❌ ملغي</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- ✅ الجانب الأيسر: الإحصائيات الزمنية -->
                                                    <div class="col-md-2 text-center">
                                                        <div class="mb-2">
                                                            <small class="text-muted fw-bold d-block mb-1">📅 التوزيع الزمني</small>
                                                            <div>
                                                                <span class="badge badge-sm bg-primary me-1" title="إنجازات هذا الشهر">
                                                                    📅 شهر: @manager.ThisMonthOperations
                                                                </span>
                                                                <span class="badge badge-sm bg-info me-1" title="إنجازات آخر 3 أشهر">
                                                                    📊 ٣ أشهر: @manager.Last3MonthsOperations
                                                                </span>
                                                                <span class="badge badge-sm bg-success me-1" title="إنجازات آخر 6 أشهر">
                                                                    📈 ٦ أشهر: @manager.Last6MonthsOperations
                                                                </span>
                                                                <span class="badge badge-sm bg-secondary" title="إنجازات آخر سنة">
                                                                    🏆 سنة: @manager.LastYearOperations
                                                                </span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- 📊 شريط التقدم المُحسن -->
                                                @if (totalManager > 0)
                                                {
                                                    <div class="progress progress-enhanced mt-3" style="height: 10px;">
                                                        <div class="progress-bar bg-success"
                                                             style="width: @((double)manager.Completed / totalManager * 100)%"
                                                             title="منجز: @manager.Completed"></div>
                                                        <div class="progress-bar bg-primary"
                                                             style="width: @((double)manager.StatusChanged / totalManager * 100)%"
                                                             title="تغيير حالة: @manager.StatusChanged"></div>
                                                        <div class="progress-bar bg-info"
                                                             style="width: @((double)manager.Returned / totalManager * 100)%"
                                                             title="معاد: @manager.Returned"></div>
                                                        <div class="progress-bar bg-danger"
                                                             style="width: @((double)manager.Cancelled / totalManager * 100)%"
                                                             title="ملغي: @manager.Cancelled"></div>
                                                        <div class="progress-bar bg-warning"
                                                             style="width: @((double)manager.UnderExecution / totalManager * 100)%"
                                                             title="تحت التنفيذ: @manager.UnderExecution"></div>
                                                    </div>
                                                }
                                            </div>
                                        </div>
                                    </div>
                                    rankIndex++;
                                }
                            </div>
                        </div>

                        <!-- عرض الرسم البياني -->
                        <div id="hrManagerChartView" class="d-none">
                            <div class="text-center">
                                <canvas id="hrManagerChart" width="600" height="400"></canvas>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد بيانات لمديري الموارد البشرية</h5>
                            <p class="text-muted">لم يتم العثور على إحصائيات لمديري الموارد البشرية حتى الآن.</p>
                        </div>
                    }
                </div>
            </div>
        </div>
        <!-- تبويب إحصائيات الطلبات المنجزة حسب اسم المشرف -->
        <div class="tab-pane fade" id="completedSupervisors" role="tabpanel">
            <div class="card content-card-enhanced">
                <div class="card-header text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-award me-2"></i>
                            إحصائيات الطلبات المنجزة حسب أسماء المشرفين
                        </h5>
                    </div>
                </div>
                <div class="card-body">
                    @if (Model.CompletedSupervisorStatistics != null && Model.CompletedSupervisorStatistics.Count > 0)
                    {
                        var completedSuperStats = Model.CompletedSupervisorStatistics;

                        // ✅ حل مشكلة Lambda - حساب الإحصائيات مسبقاً
                        var totalOperationsSum = 0;
                        var totalWorksDoneSum = 0;
                        var totalCompletedSum = 0;

                        foreach (var stat in completedSuperStats)
                        {
                            totalOperationsSum += stat.TotalOperations;
                            totalWorksDoneSum += stat.TotalWorksDone;
                            totalCompletedSum += stat.CompletedCount;
                        }

                        // ✅ ترتيب يدوي بدون Lambda (لتجنب مشكلة dynamic)
                        var rankedSupervisors = new List<dynamic>();
                        foreach (var supervisor in completedSuperStats)
                        {
                            rankedSupervisors.Add(supervisor);
                        }

                        // ترتيب يدوي bubble sort
                        for (int i = 0; i < rankedSupervisors.Count - 1; i++)
                        {
                            for (int j = 0; j < rankedSupervisors.Count - 1 - i; j++)
                            {
                                // ترتيب تنازلي حسب CompletedCount أولاً
                                if (rankedSupervisors[j].CompletedCount < rankedSupervisors[j + 1].CompletedCount)
                                {
                                    var temp = rankedSupervisors[j];
                                    rankedSupervisors[j] = rankedSupervisors[j + 1];
                                    rankedSupervisors[j + 1] = temp;
                                }
                                // إذا كانت متساوية، ترتيب حسب TotalWorksDone
                                else if (rankedSupervisors[j].CompletedCount == rankedSupervisors[j + 1].CompletedCount &&
                                rankedSupervisors[j].TotalWorksDone < rankedSupervisors[j + 1].TotalWorksDone)
                                {
                                    var temp = rankedSupervisors[j];
                                    rankedSupervisors[j] = rankedSupervisors[j + 1];
                                    rankedSupervisors[j + 1] = temp;
                                }
                            }
                        }

                        <!-- ملخص سريع محسن -->
                        <div class="row mb-4">
                            <div class="col-lg-3 col-md-4 mb-3">
                                <div class="card stat-card-enhanced stat-card-primary text-white h-100">
                                    <div class="card-body text-center">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-users"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@completedSuperStats.Count</div>
                                        <div class="stat-label-enhanced">عدد المشرفين</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-4 mb-3">
                                <div class="card stat-card-enhanced stat-card-info text-white h-100">
                                    <div class="card-body text-center">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-tasks"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@totalOperationsSum</div>
                                        <div class="stat-label-enhanced">إجمالي العمليات</div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-4 mb-3">
                                <div class="card stat-card-enhanced stat-card-warning text-white h-100">
                                    <div class="card-body text-center">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-briefcase"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@totalWorksDoneSum</div>
                                        <div class="stat-label-enhanced">إجمالي الأعمال</div>
                                        <small class="text-light opacity-75">جميع النقرات</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-3 col-md-4 mb-3">
                                <div class="card stat-card-enhanced stat-card-success text-white h-100">
                                    <div class="card-body text-center">
                                        <div class="stat-icon-enhanced">
                                            <i class="fas fa-check-double"></i>
                                        </div>
                                        <div class="stat-number-enhanced">@totalCompletedSum</div>
                                        <div class="stat-label-enhanced">إجمالي المنجز</div>
                                        <small class="text-light opacity-75">مكتمل بنجاح</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- عرض الجدول المنظف -->
                        <!-- 📋 عرض المشرفين المبسط -->
                        <div id="supervisorTableView">
                            <div class="row" id="supervisorsList">
                                @{
                                    var rankIndex = 1;
                                }
                                @foreach (var supervisor in rankedSupervisors)
                                {
                                    var totalSupervisor = supervisor.TotalOperations;
                                    <div class="col-lg-6 col-xl-4 mb-3 supervisor-item" data-name="@supervisor.SupervisorName.ToLower()">
                                        <div class="card border-0 shadow-sm h-100">
                                            <div class="card-body">
                                                <!-- عنوان المشرف مع الترتيب -->
                                                <div class="d-flex justify-content-between align-items-start mb-3">
                                                    <div>
                                                        <h6 class="card-title mb-1">
                                                            @if (rankIndex == 1)
                                                            {
                                                                <span class="badge me-2 fs-5 p-2" style="background: linear-gradient(45deg, #FFD700, #FFA500); color: #8B4513;">🥇 #1</span>
                                                            }
                                                            else if (rankIndex == 2)
                                                            {
                                                                <span class="badge me-2 fs-5 p-2" style="background: linear-gradient(45deg, #C0C0C0, #A9A9A9); color: #2F4F4F;">🥈 #2</span>
                                                            }
                                                            else if (rankIndex == 3)
                                                            {
                                                                <span class="badge me-2 fs-5 p-2" style="background: linear-gradient(45deg, #CD7F32, #D2691E); color: #FFFFFF;">🥉 #3</span>
                                                            }
                                                            else if (rankIndex <= 5)
                                                            {
                                                                <span class="badge bg-primary me-2 fs-6 p-2">#@rankIndex</span>
                                                            }
                                                            else
                                                            {
                                                                <span class="badge bg-secondary me-2 fs-6 p-1">#@rankIndex</span>
                                                            }
                                                            @supervisor.SupervisorName
                                                        </h6>
                                                    </div>
                                                    <div class="text-end">
                                                        <span class="badge bg-info">إجمالي: @totalSupervisor</span>
                                                    </div>
                                                </div>

                                                <!-- الإحصائيات الأساسية -->
                                                <div class="row text-center mb-3">
                                                    <div class="col-4">
                                                        <div class="text-success">
                                                            <div class="fw-bold">@supervisor.CompletedCount</div>
                                                            <small class="text-muted">منجز</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-4">
                                                        <div class="text-warning">
                                                            <div class="fw-bold">@supervisor.Returned</div>
                                                            <small class="text-muted">معاد</small>
                                                        </div>
                                                    </div>
                                                    <div class="col-4">
                                                        <div class="text-danger">
                                                            <div class="fw-bold">@supervisor.NeedsAction</div>
                                                            <small class="text-muted">يتطلب إجراء</small>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- شريط التقدم -->
                                                @if (totalSupervisor > 0)
                                                {
                                                    <div class="progress mb-3" style="height: 8px;">
                                                        <div class="progress-bar bg-success"
                                                             style="width: @((double)supervisor.CompletedCount / totalSupervisor * 100)%"
                                                             title="منجز: @supervisor.CompletedCount"></div>
                                                        <div class="progress-bar bg-warning"
                                                             style="width: @((double)supervisor.Returned / totalSupervisor * 100)%"
                                                             title="معاد: @supervisor.Returned"></div>
                                                        <div class="progress-bar bg-danger"
                                                             style="width: @((double)supervisor.NeedsAction / totalSupervisor * 100)%"
                                                             title="يتطلب إجراء: @supervisor.NeedsAction"></div>
                                                    </div>
                                                }

                                                <!-- النشاط الزمني -->
                                                <div class="row small text-muted">
                                                    <div class="col-12">
                                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                                            <div>
                                                                <i class="fas fa-chart-line me-1 text-primary"></i>
                                                                <strong class="text-primary">النشاط الزمني:</strong>
                                                            </div>
                                                            @if (!string.IsNullOrEmpty(supervisor.TrendDirection) && supervisor.TrendDirection != "مستقر")
                                                            {
                                                                <div>
                                                                    <small class="text-info">
                                                                        🔄 الاتجاه: @supervisor.TrendDirectionDisplay
                                                                    </small>
                                                                </div>
                                                            }
                                                        </div>
                                                        <div class="d-flex flex-wrap gap-1">
                                                            <span class="badge bg-primary badge-sm">📅 شهر: <strong>@supervisor.ThisMonthOperations</strong></span>
                                                            <span class="badge bg-info badge-sm">📊 ٣ أشهر: <strong>@supervisor.Last3MonthsOperations</strong></span>
                                                            <span class="badge bg-success badge-sm">📈 ٦ أشهر: <strong>@supervisor.Last6MonthsOperations</strong></span>
                                                            <span class="badge bg-secondary badge-sm">🏆 سنة: <strong>@supervisor.LastYearOperations</strong></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    rankIndex++;
                                }
                            </div>
                        </div>


                        <!-- عرض الرسم البياني -->
                        <div id="completedSupervisorChartView" class="d-none">
                            <div class="text-center">
                                <canvas id="completedSupervisorChart" width="600" height="400"></canvas>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="text-center py-5">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد بيانات منجزة للمشرفين</h5>
                            <p class="text-muted">لم يتم العثور على إحصائيات منجزة للمشرفين حتى الآن.</p>
                        </div>
                    }
                </div>
            </div>
        </div>

        <!-- ✅ تبويب الطلبات المعلقة والتأخير - المُصحح -->
        <div class="tab-pane fade" id="pendingOrders" role="tabpanel">
            <!-- الجدول الموحد المحسن - الطلبات المعلقة (الكل) -->
            <div class="card content-card-enhanced">
                <div class="card-header text-white" style="background: linear-gradient(135deg, #ff8c42, #6f42c1);">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fas fa-layer-group me-2"></i>
                            المراقبة الشاملة - طلبات معلقة
                        </h5>
                        <small class="text-light opacity-75">
                            آخر تحديث: @DateTime.Now.ToString("HH:mm")
                        </small>
                    </div>
                </div>
            </div>
            <div class="card-body">
                @{
                    int totalPendingAll = 0;
                    int urgentOrdersCount = 0;
                    int criticalOrdersCount = 0;
                    int totalEntities = 0;

                    if (Model.UnifiedPendingStats != null)
                    {
                        var stats = Model.UnifiedPendingStats as IEnumerable<dynamic> ?? Enumerable.Empty<dynamic>();
                        totalPendingAll = stats.Sum(e => (int)e.TotalPendingOrders);
                        urgentOrdersCount = stats.Sum(e => (int)e.UrgentOrders);
                        criticalOrdersCount = stats.Sum(e => (int)e.CriticalOrders);
                        totalEntities = stats.Count();
                    }
                }

                <!-- بطاقات الملخص السريع - مُصححة -->
                <div class="row mb-4">
                    <!-- بطاقة إجمالي الطلبات -->
                    <div class="col-md-2 mb-3">
                        <div class="card text-center h-100" style="background: linear-gradient(135deg, #6f42c1, #e83e8c); color: white;">
                            <div class="card-body py-3">
                                <i class="fas fa-list fa-2x mb-2 opacity-75"></i>
                                @{
                                    int summaryTotalPending = 0;
                                    if (Model.UnifiedPendingStats != null)
                                    {
                                        var summaryStats = Model.UnifiedPendingStats as IEnumerable<dynamic> ?? Enumerable.Empty<dynamic>();
                                        summaryTotalPending = summaryStats.Sum(e => (int)e.TotalPendingOrders);
                                    }
                                }
                                <h3 class="mb-1">@summaryTotalPending</h3>
                                <p class="mb-0 small">إجمالي الطلبات</p>
                            </div>
                        </div>
                    </div>

                    <!-- بطاقة الطلبات المعلقة العادية -->
                    <div class="col-md-2 mb-3">
                        <div class="card text-center h-100" style="background: linear-gradient(135deg, #17a2b8, #28a745); color: white;">
                            <div class="card-body py-3">
                                <i class="fas fa-clock fa-2x mb-2 opacity-75"></i>
                                @{
                                    int summaryRegularPending = 0;
                                    if (Model.UnifiedPendingStats != null)
                                    {
                                        var summaryStats = Model.UnifiedPendingStats as IEnumerable<dynamic> ?? Enumerable.Empty<dynamic>();
                                        summaryRegularPending = summaryStats.Sum(e => (int)(e.RegularPendingCount ?? 0));
                                    }
                                }
                                <h3 class="mb-1">@summaryRegularPending</h3>
                                <p class="mb-0 small">طلبات معلقة</p>
                            </div>
                        </div>
                    </div>

                    <!-- بطاقة الطلبات التي تتطلب إجراءات - مُصححة -->
                    <div class="col-md-2 mb-3">
                        <div class="card text-center h-100" style="background: linear-gradient(135deg, #ffc107, #fd7e14); color: white;">
                            <div class="card-body py-3">
                                <i class="fas fa-exclamation-triangle fa-2x mb-2 opacity-75"></i>
                                @{
                                    int summaryActionRequired = 0;
                                    if (Model.UnifiedPendingStats != null)
                                    {
                                        var summaryStats = Model.UnifiedPendingStats as IEnumerable<dynamic> ?? Enumerable.Empty<dynamic>();

                                        // ✅ حساب شامل يشمل جميع أنواع الطلبات الثانوية
                                        foreach (var entity in summaryStats)
                                        {
                                            var entityType = entity.EntityType?.ToString() ?? "";

                                            // للمشرفين ومساعدي المدير: يتطلب إجراء
                                            if (entityType == "Supervisor" || entityType == "Assistant")
                                            {
                                                summaryActionRequired += (int)(entity.ActionRequiredCount ?? 0);
                                            }
                                            // للأقسام والمنسق: معاد
                                            else if (entityType == "Department" || entityType == "Coordinator")
                                            {
                                                summaryActionRequired += (int)(entity.ReturnedCount ?? 0);
                                            }
                                            // للمدير: عادة لا يوجد (فقط معلق)
                                        }
                                    }
                                }
                                <h3 class="mb-1">@summaryActionRequired</h3>
                                <p class="mb-0 small">تتطلب إجراءات/معادة</p>
                            </div>
                        </div>
                    </div>

                    <!-- بطاقة الطلبات المعادة - جديدة -->
                    <div class="col-md-2 mb-3">
                        <div class="card text-center h-100" style="background: linear-gradient(135deg, #17a2b8, #6f42c1); color: white;">
                            <div class="card-body py-3">
                                <i class="fas fa-undo fa-2x mb-2 opacity-75"></i>
                                @{
                                    int summaryReturnedOrders = 0;
                                    if (Model.UnifiedPendingStats != null)
                                    {
                                        var summaryStats = Model.UnifiedPendingStats as IEnumerable<dynamic> ?? Enumerable.Empty<dynamic>();
                                        summaryReturnedOrders = summaryStats.Sum(e => (int)(e.ReturnedCount ?? 0));
                                    }
                                }
                                <h3 class="mb-1">@summaryReturnedOrders</h3>
                                <p class="mb-0 small">طلبات معادة</p>
                            </div>
                        </div>
                    </div>

                    <!-- بطاقة الطلبات العاجلة -->
                    <div class="col-md-2 mb-3">
                        <div class="card text-center h-100" style="background: linear-gradient(135deg, #ff6b42, #dc3545); color: white;">
                            <div class="card-body py-3">
                                <i class="fas fa-fire fa-2x mb-2 opacity-75"></i>
                                @{
                                    int summaryUrgentOrders = 0;
                                    if (Model.UnifiedPendingStats != null)
                                    {
                                        var summaryStats = Model.UnifiedPendingStats as IEnumerable<dynamic> ?? Enumerable.Empty<dynamic>();
                                        summaryUrgentOrders = summaryStats.Sum(e => (int)e.UrgentOrders);
                                    }
                                }
                                <h3 class="mb-1">@summaryUrgentOrders</h3>
                                <p class="mb-0 small">طلبات عاجلة</p>
                            </div>
                        </div>
                    </div>

                    <!-- بطاقة الجهات -->
                    <div class="col-md-2 mb-3">
                        <div class="card text-center h-100" style="background: linear-gradient(135deg, #28a745, #20c997); color: white;">
                            <div class="card-body py-3">
                                <i class="fas fa-users fa-2x mb-2 opacity-75"></i>
                                @{
                                    int summaryTotalEntities = 0;
                                    if (Model.UnifiedPendingStats != null)
                                    {
                                        var summaryStats = Model.UnifiedPendingStats as IEnumerable<dynamic> ?? Enumerable.Empty<dynamic>();
                                        summaryTotalEntities = summaryStats.Count();
                                    }
                                }
                                <h3 class="mb-1">@summaryTotalEntities</h3>
                                <p class="mb-0 small">جهات لديها طلبات</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الجدول الرئيسي المحسن -->
                <div class="table-responsive">
                    <!-- الجدول الرئيسي المحسن مع العمود المدموج -->
                    <div class="table-responsive">
                        <table class="table table-hover align-middle shadow-sm">
                            <thead class="table-danger">
                                <tr>
                                    <th class="text-center tooltip-header" style="width: 8%;"
                                        data-tooltip="ترتيب الأولوية بناءً على عدد الطلبات المعلقة ومتوسط أيام الانتظار">
                                        الترتيب
                                        <i class="fas fa-info-circle ms-1" style="font-size: 0.8em; opacity: 0.6;"></i>
                                    </th>
                                    <th class="tooltip-header" style="width: 29%;"
                                        data-tooltip="اسم الجهة ونوعها (مشرف، قسم، منسق، مساعد مدير، مدير الموارد البشرية)">
                                        الجهة
                                        <i class="fas fa-info-circle ms-1" style="font-size: 0.8em; opacity: 0.6;"></i>
                                    </th>
                                    <th class="text-center tooltip-header" style="width: 10%;"
                                        data-tooltip="العدد الإجمالي للطلبات المعلقة - تشمل العادية والتي تتطلب إجراء والمعادة">
                                        الطلبات المعلقة
                                        <i class="fas fa-info-circle ms-1" style="font-size: 0.8em; opacity: 0.6;"></i>
                                    </th>
                                    <th class="text-center tooltip-header" style="width: 11%;"
                                        data-tooltip="متوسط عدد أيام الانتظار لدى هذه الجهة (أيام عمل فقط)">
                                        متوسط أيام الانتظار
                                        <i class="fas fa-info-circle ms-1" style="font-size: 0.8em; opacity: 0.6;"></i>
                                    </th>
                                    <th class="text-center tooltip-header" style="width: 34%;"
                                        data-tooltip="عرض تفصيلي لأرقام الطلبات المعلقة حسب النوع مع الأيام">
                                        أرقام الطلبات
                                        <i class="fas fa-info-circle ms-1" style="font-size: 0.8em; opacity: 0.6;"></i>
                                    </th>
                                    <th class="text-center tooltip-header" style="width: 8%;"
                                        data-tooltip="مستوى الأولوية للمتابعة (عاجل، حرج، مراقبة، طبيعي) مع تفصيل عدد الطلبات حسب درجة الأولوية (عاجلة، حرجة، متأخرة، عادية)">
                                        <span style="font-size: 0.85em; display: inline-flex; align-items: center;">
                                            الأولوية
                                            <i class="fas fa-info-circle ms-1" style="font-size: 0.8em; opacity: 0.6;"></i>
                                        </span>
                                    </th>



                                </tr>
                            </thead>

                            <tbody>
                                @if (Model.UnifiedPendingStats != null)
                                {
                                    var priorityIndex = 1;
                                    var stats = Model.UnifiedPendingStats as IEnumerable<dynamic> ?? Enumerable.Empty<dynamic>();
                                    var sortedStats = stats.OrderBy(e => (int)e.Ranking).ToList();
                                    foreach (var entity in sortedStats)
                                    {
                                        var rowClass = entity.EntityType switch
                                        {
                                            "Manager" => "table-danger-subtle",
                                            "Department" => "table-light-subtle",
                                            "Coordinator" => "table-success-subtle",
                                            "Assistant" => "table-warning-subtle",
                                            _ => ""
                                        };

                                        var avgDays = entity.AveragePendingDays;
                                        var urgencyLevel = avgDays > 5 ? "عاجل جداً" :
                                        avgDays > 3 ? "حرج" :
                                        avgDays > 1 ? "متأخر" : "عادي";

                                        <tr class="@rowClass">
                                            <!-- عمود الترتيب -->
                                            <td class="text-center">
                                                <div class="d-flex flex-column align-items-center">
                                                    @if (priorityIndex <= 3)
                                                    {
                                                        <span class="badge bg-danger fs-6 mb-1">@priorityIndex</span>
                                                        <small class="text-danger fw-bold">عالي جداً</small>
                                                    }
                                                    else if (priorityIndex <= 6)
                                                    {
                                                        <span class="badge bg-warning fs-6 mb-1">@priorityIndex</span>
                                                        <small class="text-warning fw-bold">عالي</small>
                                                    }
                                                    else if (priorityIndex <= 10)
                                                    {
                                                        <span class="badge bg-info fs-6 mb-1">@priorityIndex</span>
                                                        <small class="text-info fw-bold">متوسط</small>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-secondary fs-6 mb-1">@priorityIndex</span>
                                                        <small class="text-secondary">منخفض</small>
                                                    }
                                                </div>
                                            </td>

                                            <!-- عمود الجهة مع الإحصائيات -->
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <div class="me-3">
                                                        @{
                                                            var icon = entity.EntityType switch
                                                            {
                                                                "Manager" => "fas fa-user-tie text-danger",
                                                                "Department" => "fas fa-building text-primary",
                                                                "Coordinator" => "fas fa-clipboard-check text-success",
                                                                "Assistant" => "fas fa-user-shield text-warning",
                                                                "Supervisor" => "fas fa-user text-info",
                                                                _ => "fas fa-circle text-secondary"
                                                            };
                                                        }
                                                        <i class="@icon fa-lg"></i>
                                                    </div>
                                                    <div>
                                                        <strong>@entity.EntityName</strong>
                                                        <br>
                                                        <small class="text-muted">
                                                            @{
                                                                var badgeClass = entity.EntityType switch
                                                                {
                                                                    "Manager" => "bg-danger",
                                                                    "Department" => "bg-primary",
                                                                    "Coordinator" => "bg-success",
                                                                    "Assistant" => "bg-warning",
                                                                    "Supervisor" => "bg-info",
                                                                    _ => "bg-secondary"
                                                                };
                                                                var badgeText = entity.EntityType switch
                                                                {
                                                                    "Manager" => "مدير HR",
                                                                    "Department" => "قسم",
                                                                    "Coordinator" => "منسق HR",
                                                                    "Assistant" => "مساعد مدير",
                                                                    "Supervisor" => "مشرف",
                                                                    _ => "غير محدد"
                                                                };
                                                            }
                                                            <span class="badge @badgeClass me-1">@badgeText</span>
                                                        </small>
                                                    </div>
                                                </div>
                                            </td>

                                            <!-- عمود الطلبات المعلقة - محسن مع التقسيم -->
                                            <td class="text-center">
                                                <div class="d-flex flex-column align-items-center gap-2">
                                                    <!-- إجمالي الطلبات -->
                                                    <div class="d-flex align-items-end gap-1">
                                                        <span class="fs-2 fw-bold
                                          @(entity.TotalPendingOrders > 5 ? "text-danger" :
                                            entity.TotalPendingOrders > 3 ? "text-warning" : "text-success")">
                                                            @entity.TotalPendingOrders
                                                        </span>
                                                        <span class="text-muted small mb-1">طلبات معلقة</span>
                                                    </div>


                                                </div>
                                            </td>

                                            <!-- عمود متوسط أيام الانتظار -->
                                            <td class="text-center">
                                                <div class="fs-5 fw-bold @(avgDays > 10 ? "text-danger" : avgDays > 5 ? "text-warning" : avgDays > 2 ? "text-info" : "text-success")">
                                                    @avgDays.ToString("F1") يوم
                                                </div>
                                                <span class="badge @(avgDays > 10 ? "bg-danger" : avgDays > 5 ? "bg-warning" : avgDays > 2 ? "bg-info" : "bg-success")">
                                                    @urgencyLevel
                                                </span>
                                            </td>

                                            <!-- عمود أرقام الطلبات - تصنيف + طلبات في سطر واحد -->
                                            <td class="text-center" style="width: 35%;padding-top: 0.2rem;">
                                                @if (entity.PendingOrdersList != null && entity.PendingOrdersList.Count > 0)
                                                {
                                                    var pendingOrders = entity.PendingOrdersList as IEnumerable<PendingOrderDetails> ?? Enumerable.Empty<PendingOrderDetails>();
                                                    var groupedOrders = pendingOrders
                                                    .Where(o => !string.IsNullOrEmpty(o.OrderCategory))
                                                    .GroupBy(o => o.OrderCategory)
                                                    .OrderBy(g => g.Key == "معلق" ? 0 : g.Key == "يتطلب إجراء" ? 1 : 2)
                                                    .ToList();

                                                    <div class="card border rounded" style="background-color: rgba(0,0,0,0.03);">
                                                        <div class="card-body p-2 text-start">
                                                            @foreach (var group in groupedOrders)
                                                            {
                                                                var categoryName = group.Key;
                                                                var categoryIcon = categoryName switch
                                                                {
                                                                    "معلق" => "fas fa-clock text-info",
                                                                    "يتطلب إجراء" => "fas fa-exclamation-triangle text-warning",
                                                                    "معاد" => "fas fa-undo text-primary",
                                                                    _ => "fas fa-circle text-secondary"
                                                                };

                                                                var badgeColor = categoryName switch
                                                                {
                                                                    "معلق" => "bg-info text-white",
                                                                    "يتطلب إجراء" => "bg-warning text-dark",
                                                                    "معاد" => "bg-primary text-white",
                                                                    _ => "bg-secondary text-white"
                                                                };

                                                                var ordersInGroup = group.OrderByDescending(o => o.PendingDays).ToList();
                                                                var maxDisplay = 5;

                                                                <!-- التصنيف والطلبات في نفس السطر -->
                                                                <div class="d-flex align-items-center flex-wrap gap-2 mb-2" style="font-size: 0.75rem;">
                                                                    <span class="fw-bold d-flex align-items-center">
                                                                        <i class="@categoryIcon me-1" style="font-size: 0.9rem;"></i>
                                                                        @categoryName
                                                                        <span class="badge @badgeColor rounded-circle ms-1"
                                                                              style="min-width: 1.2rem; height: 1.2rem; display: inline-flex; align-items: center; justify-content: center; font-size: 0.70rem;">
                                                                            @ordersInGroup.Count
                                                                        </span> :
                                                                    </span>

                                                                    @foreach (var order in ordersInGroup.Take(maxDisplay))
                                                                    {
                                                                        var orderBadgeClass = order.PendingDays > 7 ? "bg-danger" :
                                                                        order.PendingDays > 5 ? "bg-warning text-dark" :
                                                                        order.PendingDays > 3 ? "bg-info" : "bg-success";

                                                                        <span class="badge @orderBadgeClass"
                                                                              style="font-size:0.70rem; padding:0.25rem 0.35rem;"
                                                                              title="الطلب: @order.OrderId - @order.EmployeeName - @order.PendingDays يوم - @order.Status">
                                                                            #@order.OrderId <small>(@order.PendingDays د)</small>
                                                                        </span>
                                                                    }

                                                                    @if (ordersInGroup.Count > maxDisplay)
                                                                    {
                                                                        <span class="badge bg-secondary" style="font-size:0.75rem;">+@(ordersInGroup.Count - maxDisplay) أخرى</span>
                                                                    }
                                                                </div>
                                                            }
                                                        </div>
                                                    </div>
                                                }
                                                else
                                                {
                                                    <span class="text-muted">لا توجد بيانات</span>
                                                }
                                            </td>
                                            <!-- عمود الحالة والتصنيف المدمج -->
                                            <td class="text-center align-top" style="width: 8%;  padding-top: 0.2rem;">
                                                <!-- إزالة كل الهوامش الخارجية للـ td -->
                                                <div class="d-flex flex-column justify-content-start align-items-center">
                                                    @{
                                                        // تحديد لون وخلفية حسب الأولوية
                                                        var statusClass = avgDays > 5 ? "danger" :
                                                        avgDays > 3 ? "warning" :
                                                        avgDays > 1 ? "info" : "success";

                                                        var statusIcon = avgDays > 5 ? "fa-fire" :
                                                        avgDays > 3 ? "fa-clock" :
                                                        avgDays > 1 ? "fa-eye" : "fa-check";

                                                        var statusText = avgDays > 5 ? "عاجل" :
                                                        avgDays > 3 ? "متابعة" :
                                                        avgDays > 1 ? "مراقبة" : "طبيعي";

                                                        // حساب الطلبات العادية
                                                        var normalOrders = entity.TotalPendingOrders - (entity.UrgentOrders + entity.CriticalOrders + entity.OverdueOrders);
                                                        normalOrders = normalOrders > 0 ? normalOrders : 0;
                                                    }

                                                    <!-- مربع الحالة الرئيسي -->
                                                    <div class="alert alert-@statusClass w-100 text-center" style="font-size: 0.90rem; padding: 0.05rem 0.2rem; margin: 0;">
                                                        <!-- تقليل الـ padding وإزالة الهوامش -->
                                                        <div class="d-flex justify-content-center align-items-center">
                                                            <i class="fas @statusIcon fa-xs me-1"></i>
                                                            <strong>@statusText</strong>
                                                        </div>

                                                        <!-- التصنيف داخل المربع مع خلفية بيضاء -->
                                                        @if (entity.UrgentOrders > 0 || entity.CriticalOrders > 0 || entity.OverdueOrders > 0 || normalOrders > 0)
                                                        {
                                                            <div class="mt-0 bg-white rounded p-0" style="border: 1px solid rgba(0,0,0,0.1); padding: 0.05rem;">
                                                                <!-- إزالة mt-1 وتقليل الـ padding -->
                                                                @if (entity.UrgentOrders > 0)
                                                                {
                                                                    <div class="fw-bold text-danger" style="font-size: 0.8rem; margin: 0;">
                                                                        <!-- إزالة الهوامش الداخلية -->
                                                                        @entity.UrgentOrders عاجلة
                                                                    </div>
                                                                }
                                                                @if (entity.CriticalOrders > 0)
                                                                {
                                                                    <div class="fw-bold text-warning" style="font-size: 0.8rem; margin: 0;">
                                                                        @entity.CriticalOrders حرجة
                                                                    </div>
                                                                }
                                                                @if (entity.OverdueOrders > 0)
                                                                {
                                                                    <div class="fw-bold text-info" style="font-size: 0.8rem; margin: 0;">
                                                                        @entity.OverdueOrders متأخرة
                                                                    </div>
                                                                }
                                                                @if (normalOrders > 0)
                                                                {
                                                                    <div class="fw-bold text-success" style="font-size: 0.8rem; margin: 0;">
                                                                        @normalOrders عادية
                                                                    </div>
                                                                }
                                                            </div>
                                                        }
                                                    </div>
                                                </div>
                                            </td>

                                        </tr>
                                        priorityIndex++;
                                    }
                                }
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>


            <!-- 🎯 المرحلة الثالثة: التحليل التاريخي -->
            <div class="row mb-4 mt-5">
                <!-- الجدول الموحد المحسن - الطلبات المعلقة (الكل) -->
                <div class="card content-card-enhanced">
                    <div class="card-header text-white" style="background: linear-gradient(135deg, #ff8c42, #6f42c1);">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">
                                <i class="fas fa-layer-group me-2"></i>
                                تحليل شامل لأداء المشرفين
                            </h5>
                            <small class="text-light opacity-75">
                                آخر تحديث: @DateTime.Now.ToString("HH:mm")
                            </small>
                        </div>
                    </div>
                </div>
                @{
                    // ✅ التأكد من النوع الصحيح
                    var delayStatsList = Model?.SupervisorDelayStatistics ?? new List<SupervisorDelayStatisticsDto>();
                    var hasDelayData = delayStatsList.Count > 0;

                    // ✅ حساب الإحصائيات بدون lambda
                    int totalCompletedOrders = 0;
                    int totalDelayedOrders = 0;
                    double totalDelaySum = 0;
                    int delayActiveCount = 0;

                    foreach (var item in delayStatsList)
                    {
                        totalCompletedOrders += item.TotalCompletedOrders;
                        totalDelayedOrders += item.DelayedOrders;
                        if (item.TotalCompletedOrders > 0)
                        {
                            totalDelaySum += item.AverageDelayDays;
                            delayActiveCount++;
                        }
                    }

                    double overallOnTimePercentage = totalCompletedOrders > 0 ?
                    (double)(totalCompletedOrders - totalDelayedOrders) / totalCompletedOrders * 100 : 0;
                    double overallAverageDelay = delayActiveCount > 0 ? totalDelaySum / delayActiveCount : 0;
                }

                <!-- بطاقات الملخص السريع مع Tooltips -->
                <div class="row mb-4">
                    <div class="col-md-3">
                        <div class="card text-center h-100 tooltip-card"
                             style="background: linear-gradient(135deg, #3498db, #2980b9); color: white;"
                             title="إجمالي الطلبات التي تم التعامل معها (اعتماد، إجراء، إعادة)">
                            <div class="card-body">
                                <i class="fas fa-check-circle fa-3x mb-3 opacity-75"></i>
                                <h2 class="mb-1">@totalCompletedOrders</h2>
                                <p class="mb-0">إجمالي الطلبات المكتملة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center h-100 tooltip-card"
                             style="background: linear-gradient(135deg, #27ae60, #229954); color: white;"
                             title="نسبة الطلبات التي تم إنجازها خلال يوم واحد أو أقل">
                            <div class="card-body">
                                <i class="fas fa-clock fa-3x mb-3 opacity-75"></i>
                                <h2 class="mb-1">@overallOnTimePercentage.ToString("F1")%</h2>
                                <p class="mb-0">في الوقت المحدد</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center h-100 tooltip-card"
                             style="background: linear-gradient(135deg, #f39c12, #e67e22); color: white;"
                             title="عدد الطلبات التي استغرقت أكثر من يوم واحد للإنجاز">
                            <div class="card-body">
                                <i class="fas fa-exclamation-triangle fa-3x mb-3 opacity-75"></i>
                                <h2 class="mb-1">@totalDelayedOrders</h2>
                                <p class="mb-0">طلبات متأخرة</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center h-100 tooltip-card"
                             style="background: linear-gradient(135deg, #e74c3c, #c0392b); color: white;"
                             title="متوسط الأيام بين تاريخ وصول الطلب وتاريخ اعتماده (أيام العمل فقط - استثناء الجمعة والسبت)">
                            <div class="card-body">
                                <i class="fas fa-hourglass-half fa-3x mb-3 opacity-75"></i>
                                <h2 class="mb-1">@overallAverageDelay.ToString("F1")</h2>
                                <p class="mb-0">متوسط التأخير (يوم)</p>
                            </div>
                        </div>
                    </div>
                </div>

                @if (delayStatsList.Count > 0)
                {
                    <!-- الجدول الرئيسي مع Header Tooltips -->
                    <div class="card border-danger">

                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-danger">
                                        <tr>
                                            <th class="text-center">الترتيب</th>
                                            <th class="tooltip-header"
                                                data-tooltip="اسم المشرف مع مؤشرات الأداء: الكفاءة (تقييم شامل للأداء العام)، متوسط الإنجاز (متوسط أيام إتمام الطلبات - أيام العمل فقط)">
                                                المشرف
                                                <i class="fas fa-info-circle ms-1" style="font-size: 0.8em; opacity: 0.7;"></i>
                                            </th>
                                            <th class="text-center tooltip-header"
                                                data-tooltip="إجمالي الطلبات التي تم التعامل معها (اعتماد، إجراء، إعادة)">
                                                الطلبات المكتملة
                                                <i class="fas fa-info-circle ms-1" style="font-size: 0.8em; opacity: 0.7;"></i>
                                            </th>
                                            <th class="text-center tooltip-header"
                                                data-tooltip="متوسط الأيام بين تاريخ وصول الطلب وتاريخ اعتماده (أيام العمل فقط - استثناء الجمعة والسبت)">
                                                متوسط التأخير
                                                <i class="fas fa-info-circle ms-1" style="font-size: 0.8em; opacity: 0.7;"></i>
                                            </th>
                                            <th class="text-center tooltip-header"
                                                data-tooltip="نسبة الطلبات التي تم إنجازها خلال يوم واحد أو أقل">
                                                في الوقت المحدد
                                                <i class="fas fa-info-circle ms-1" style="font-size: 0.8em; opacity: 0.7;"></i>
                                            </th>
                                            <th class="text-center tooltip-header"
                                                data-tooltip="عدد الطلبات التي استغرقت أكثر من يوم واحد للإنجاز">
                                                الطلبات المتأخرة
                                                <i class="fas fa-info-circle ms-1" style="font-size: 0.8em; opacity: 0.7;"></i>
                                            </th>
                                            <th class="text-center">مستوى الأداء</th>
                                            <th class="text-center">الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @{
                                            // ترتيب يدوي بدون lambda
                                            var orderedList = new List<SupervisorDelayStatisticsDto>();
                                            var rankedSupervisors = new List<SupervisorDelayStatisticsDto>();
                                            var unrankedSupervisors = new List<SupervisorDelayStatisticsDto>();

                                            foreach (var item in delayStatsList)
                                            {
                                                if (item.DelayRanking > 0)
                                                    rankedSupervisors.Add(item);
                                                else
                                                    unrankedSupervisors.Add(item);
                                            }

                                            // ترتيب الفئة المرتبة حسب الرقم
                                            for (int i = 1; i <= 16; i++)
                                            {
                                                foreach (var item in rankedSupervisors)
                                                {
                                                    if (item.DelayRanking == i)
                                                    {
                                                        orderedList.Add(item);
                                                        break;
                                                    }
                                                }
                                            }
                                            // إضافة غير المرتبين
                                            orderedList.AddRange(unrankedSupervisors);
                                        }

                                        @foreach (var supervisor in orderedList)
                                        {
                                            var avgDelay = supervisor.AverageDelayDays;
                                            var onTimePercentage = supervisor.OnTimePercentage;
                                            var ranking = supervisor.DelayRanking;
                                            var hasData = supervisor.HasData;

                                            var rowClass = "";





                                            <tr class="@rowClass">
                                                <td class="text-center">
                                                    <div class="d-flex flex-column align-items-center">
                                                        @if (ranking == 1)
                                                        {
                                                            <span class="badge bg-warning fs-6 mb-1">🥇 @ranking</span>
                                                            <small class="text-warning fw-bold">الأول</small>
                                                        }
                                                        else if (ranking == 2)
                                                        {
                                                            <span class="badge bg-secondary fs-6 mb-1">🥈 @ranking</span>
                                                            <small class="text-secondary fw-bold">الثاني</small>
                                                        }
                                                        else if (ranking == 3)
                                                        {
                                                            <span class="badge bg-danger fs-6 mb-1">🥉 @ranking</span>
                                                            <small class="text-danger fw-bold">الثالث</small>
                                                        }
                                                        else if (ranking > 0)
                                                        {
                                                            <span class="badge bg-dark fs-6 mb-1">@ranking</span>
                                                            <small class="text-muted">عادي</small>
                                                        }
                                                        else
                                                        {
                                                            <span class="badge bg-secondary fs-6 mb-1">--</span>
                                                            <small class="text-muted">غير مصنف</small>
                                                        }
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="me-3">
                                                            @if (hasData)
                                                            {
                                                                @if (ranking <= 3 && avgDelay <= 2)
                                                                {
                                                                    <i class="fas fa-star text-warning fa-lg"></i>
                                                                }
                                                                else if (avgDelay <= 3)
                                                                {
                                                                    <i class="fas fa-thumbs-up text-success fa-lg"></i>
                                                                }
                                                                else if (avgDelay <= 5)
                                                                {
                                                                    <i class="fas fa-clock text-warning fa-lg"></i>
                                                                }
                                                                else
                                                                {
                                                                    <i class="fas fa-exclamation-triangle text-danger fa-lg"></i>
                                                                }
                                                            }
                                                            else
                                                            {
                                                                <i class="fas fa-minus-circle text-secondary fa-lg"></i>
                                                            }
                                                        </div>
                                                        <div>
                                                            <strong>@supervisor.SupervisorName</strong>
                                                            <br>
                                                            @{
                                                                // البحث عن المشرف في SupervisorStatistics للحصول على معلومات إضافية
                                                                double? supervisorEfficiency = null;
                                                                double? supervisorAvgTime = null;

                                                                if (Model?.SupervisorStatistics != null)
                                                                {
                                                                    foreach (var stat in Model.SupervisorStatistics)
                                                                    {
                                                                        if (stat.SupervisorName == supervisor.SupervisorName)
                                                                        {
                                                                            supervisorEfficiency = stat.EfficiencyScore;
                                                                            supervisorAvgTime = stat.AverageCompletionTime;
                                                                            break;
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                            <small class="text-muted">
                                                                @if (supervisorEfficiency.HasValue)
                                                                {
                                                                    <span>كفاءة: @Math.Round(supervisorEfficiency.Value, 1)</span>
                                                                }
                                                                @if (supervisorAvgTime.HasValue && supervisorAvgTime > 0)
                                                                {
                                                                    <span> | متوسط إنجاز: @supervisorAvgTime.Value.ToString("F1") يوم</span>
                                                                }
                                                                @if (!supervisorEfficiency.HasValue && !supervisorAvgTime.HasValue)
                                                                {
                                                                    <span>لا توجد بيانات إضافية</span>
                                                                }
                                                            </small>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="text-center">
                                                    <div class="fs-4 fw-bold @(hasData ? "text-primary" : "text-secondary")">
                                                        @supervisor.TotalCompletedOrders
                                                    </div>
                                                    <small class="text-muted">طلب مكتمل</small>
                                                </td>
                                                <td class="text-center">
                                                    @if (hasData)
                                                    {
                                                        <div class="fs-5 fw-bold @(avgDelay <= 1 ? "text-success" : avgDelay <= 2 ? "text-info" : avgDelay <= 5 ? "text-warning" : "text-danger")">
                                                            @avgDelay.ToString("F1") يوم
                                                        </div>
                                                        <span class="badge @(avgDelay <= 1 ? "bg-success" : avgDelay <= 2 ? "bg-info" : avgDelay <= 5 ? "bg-warning" : "bg-danger")">
                                                            @(avgDelay <= 1 ? "ممتاز" : avgDelay <= 2 ? "جيد" : avgDelay <= 5 ? "متوسط" : "يحتاج تحسين")
                                                        </span>
                                                    }
                                                    else
                                                    {
                                                        <div class="fs-5 fw-bold text-secondary">--</div>
                                                        <span class="badge bg-secondary">لا توجد بيانات</span>
                                                    }
                                                </td>
                                                <td class="text-center">
                                                    @if (hasData)
                                                    {
                                                        <div class="fs-5 fw-bold text-success">@onTimePercentage.ToString("F1")%</div>
                                                        <div class="progress mt-1" style="height: 8px;">
                                                            <div class="progress-bar bg-success" style="width: @onTimePercentage%"></div>
                                                        </div>
                                                    }
                                                    else
                                                    {
                                                        <div class="fs-5 fw-bold text-secondary">--</div>
                                                    }
                                                </td>
                                                <td class="text-center">
                                                    @if (hasData)
                                                    {
                                                        <div class="fs-5 fw-bold text-warning">@supervisor.DelayedOrders</div>
                                                        @if (supervisor.DelayedOrders > 0)
                                                        {
                                                            <small class="text-muted">من @supervisor.TotalCompletedOrders</small>
                                                        }
                                                        else
                                                        {
                                                            <small class="text-success">لا توجد</small>
                                                        }
                                                    }
                                                    else
                                                    {
                                                        <div class="fs-5 fw-bold text-secondary">--</div>
                                                    }
                                                </td>
                                                <td class="text-center">
                                                    @if (hasData)
                                                    {
                                                        <span class="badge @(avgDelay <= 1 ? "bg-success" : avgDelay <= 2 ? "bg-info" : avgDelay <= 5 ? "bg-warning" : "bg-danger") fs-6">
                                                            @supervisor.PerformanceLevel
                                                        </span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-secondary fs-6">غير نشط</span>
                                                    }
                                                </td>
                                                <td class="text-center">
                                                    @if (hasData)
                                                    {
                                                        @if (avgDelay <= 1)
                                                        {
                                                            <div class="alert alert-success p-2 mb-0">
                                                                <i class="fas fa-trophy me-1"></i>
                                                                <strong>أداء ممتاز</strong>
                                                            </div>
                                                        }
                                                        else if (avgDelay <= 2)
                                                        {
                                                            <div class="alert alert-info p-2 mb-0">
                                                                <i class="fas fa-thumbs-up me-1"></i>
                                                                <strong>أداء جيد</strong>
                                                            </div>
                                                        }
                                                        else if (avgDelay <= 5)
                                                        {
                                                            <div class="alert alert-warning p-2 mb-0">
                                                                <i class="fas fa-clock me-1"></i>
                                                                <strong>يحتاج متابعة</strong>
                                                            </div>
                                                        }
                                                        else
                                                        {
                                                            <div class="alert alert-danger p-2 mb-0">
                                                                <i class="fas fa-exclamation-triangle me-1"></i>
                                                                <strong>يحتاج تحسين عاجل</strong>
                                                            </div>
                                                        }
                                                    }
                                                    else
                                                    {
                                                        <div class="alert alert-secondary p-2 mb-0">
                                                            <i class="fas fa-minus-circle me-1"></i>
                                                            <strong>غير نشط</strong>
                                                        </div>
                                                    }
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                }
                else
                {
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle fa-3x mb-3"></i>
                        <h5>لا توجد بيانات تأخير متاحة</h5>
                        <p class="mb-0">لم يتم العثور على طلبات مكتملة لحساب إحصائيات التأخير</p>
                    </div>
                }
            </div>
        </div>
    </div> <!-- ✅ إغلاق tab-content -->
    <!-- مكتبات JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>

    <script>
                                        // تحريك العدادات
                            function animateCounters() {
                                const counters = document.querySelectorAll('[data-count]');

                                counters.forEach(counter => {
                                    const target = parseFloat(counter.dataset.count.replace(',', ''));
                                    const duration = 2000;
                                    const step = target / (duration / 16);
                                    let current = 0;

                                    const timer = setInterval(() => {
                                        current += step;
                                        if (current >= target) {
                                            counter.textContent = counter.dataset.count;
                                            clearInterval(timer);
                                        } else {
                                            if (target > 100) {
                                                counter.textContent = Math.floor(current).toLocaleString();
                                            } else {
                                                counter.textContent = current.toFixed(1);
                                            }
                                        }
                                    }, 16);
                                });
                            }

                            // إنشاء الرسوم البيانية المصغرة
                            function createMiniCharts() {
                                const charts = document.querySelectorAll('.mini-chart');

                                charts.forEach((canvas, index) => {
                                    const ctx = canvas.getContext('2d');

                                    // بيانات عشوائية للعرض التوضيحي
                                    const data = [];
                                    for (let i = 0; i < 7; i++) {
                                        data.push(Math.random() * 100);
                                    }

                                    new Chart(ctx, {
                                        type: 'line',
                                        data: {
                                            labels: ['1', '2', '3', '4', '5', '6', '7'],
                                            datasets: [{
                                                data: data,
                                                borderColor: 'rgba(255,255,255,0.8)',
                                                backgroundColor: 'rgba(255,255,255,0.1)',
                                                borderWidth: 2,
                                                fill: true,
                                                tension: 0.4,
                                                pointRadius: 0
                                            }]
                                        },
                                        options: {
                                            responsive: true,
                                            maintainAspectRatio: false,
                                            plugins: {
                                                legend: { display: false }
                                            },
                                            scales: {
                                                x: { display: false },
                                                y: { display: false }
                                            },
                                            elements: {
                                                point: { radius: 0 }
                                            }
                                        }
                                    });
                                });
                            }

                        // إنشاء الرسم البياني الدائري لمراحل المعالجة
                        function createStageChart() {
                            const ctx = document.getElementById('stageChart');
                            if (!ctx) return;

                            new Chart(ctx, {
                                type: 'doughnut',
                                data: {
                                    labels: [
                                        'مدير القسم',
                                        'مساعد المدير A1',
                                        'مساعد المدير A2',
                                        'مساعد المدير A3',
                                        'مساعد المدير A4',
                                        'منسق الموارد البشرية',
                                        'المشرفون',
                                        'مدير الموارد البشرية'
                                    ],
                                    datasets: [{
                                        data: [
        @Model.StageStatistics.DepartmentManager,
        @Model.StageStatistics.AssistantManagerA1,
        @Model.StageStatistics.AssistantManagerA2,
        @Model.StageStatistics.AssistantManagerA3,
        @Model.StageStatistics.AssistantManagerA4,
        @Model.StageStatistics.HRCoordinator,
        @Model.StageStatistics.Supervisors,
        @Model.StageStatistics.HRManager
                                        ],
                                        backgroundColor: [
                            '#6c757d',  // مدير القسم - secondary (رمادي)
                            '#0d6efd',  // مساعد المدير A1 - primary (أزرق)
                            '#0dcaf0',  // مساعد المدير A2 - info (أزرق فاتح)
                            '#ffc107',  // مساعد المدير A3 - warning (أصفر)
                            '#198754',  // مساعد المدير A4 - success (أخضر)
                            '#dc3545',  // منسق الموارد البشرية - danger (أحمر)
                            '#212529',  // المشرفون - dark (أسود)
                            '#0d6efd'   // مدير الموارد البشرية - primary (أزرق)
                                        ],
                                        borderWidth: 0,
                                        hoverOffset: 10
                                    }]
                                },
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: false,
                                    plugins: {
                                        legend: {
                                            position: 'bottom',
                                            labels: {
                                                padding: 20,
                                                usePointStyle: true,
                                                font: {
                                                    size: 12
                                                }
                                            }
                                        }
                                    },
                                    cutout: '60%',
                                    animation: {
                                        animateScale: true,
                                        animateRotate: true
                                    }
                                }
                            });
                        }

                        // تأثيرات التمرير
                        function initScrollEffects() {
                            const observer = new IntersectionObserver((entries) => {
                                entries.forEach(entry => {
                                    if (entry.isIntersecting) {
                                        entry.target.style.opacity = '1';
                                        entry.target.style.transform = 'translateY(0)';
                                    }
                                });
                            }, {
                                threshold: 0.1
                            });

                            document.querySelectorAll('.fade-in-up').forEach(el => {
                                observer.observe(el);
                            });
                        }

                        // تأثيرات التحويم على البطاقات
                        function initCardHoverEffects() {
                            const cards = document.querySelectorAll('.stat-card-enhanced');

                            cards.forEach(card => {
                                card.addEventListener('mouseenter', function() {
                                    this.style.transform = 'translateY(-10px) scale(1.02)';
                                    this.style.boxShadow = '0 25px 50px rgba(0,0,0,0.2)';
                                });

                                card.addEventListener('mouseleave', function() {
                                    this.style.transform = 'translateY(0) scale(1)';
                                    this.style.boxShadow = '0 10px 30px rgba(0,0,0,0.1)';
                                });
                            });
                        }

                        // تأثيرات النقر على التبويبات
                        function initTabEffects() {
                            const tabButtons = document.querySelectorAll('.nav-tabs-enhanced .nav-link');

                            tabButtons.forEach(button => {
                                button.addEventListener('click', function() {
                                    this.style.transform = 'scale(0.95)';
                                    setTimeout(() => {
                                        this.style.transform = 'scale(1)';
                                    }, 150);
                                });
                            });
                        }

                        // تحديث الإحصائيات تلقائياً (كل 5 دقائق)
                        function autoUpdateStats() {
                            setInterval(() => {
                                const counters = document.querySelectorAll('[data-count]');
                                counters.forEach(counter => {
                                    const currentValue = parseInt(counter.textContent.replace(',', ''));
                                    const change = Math.floor(Math.random() * 10) - 5;
                                    const newValue = Math.max(0, currentValue + change);

                                    counter.style.transform = 'scale(1.1)';
                                    counter.style.color = change > 0 ? '#43cea2' : change < 0 ? '#ff6b6b' : '';

                                    setTimeout(() => {
                                        counter.textContent = newValue.toLocaleString();
                                        counter.style.transform = 'scale(1)';
                                        counter.style.color = '';
                                    }, 300);
                                });

                                console.log('تم تحديث الإحصائيات:', new Date().toLocaleTimeString());
                            }, 300000);
                        }

                        // إظهار إشعار بسيط
                        function showUpdateNotification() {
                            const notification = document.createElement('div');
                            notification.className = 'alert alert-success position-fixed';
                            notification.style.cssText = `
                                top: 20px;
                                right: 20px;
                                z-index: 9999;
                                min-width: 300px;
                            `;
                            notification.innerHTML = `
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-check-circle me-2"></i>
                                    <span>تم تحديث الإحصائيات بنجاح</span>
                                    <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
                                </div>
                            `;

                            document.body.appendChild(notification);

                            setTimeout(() => {
                                if (notification.parentElement) {
                                    notification.remove();
                                }
                            }, 5000);
                        }

                        // CSS بسيط للتأثيرات
                        const simpleStyles = `
                            .stat-card-enhanced {
                                transition: all 0.3s ease;
                                cursor: pointer;
                            }

                            .stage-item-enhanced:hover .stage-icon-enhanced {
                                transform: scale(1.1);
                            }

                            .nav-tabs-enhanced .nav-link {
                                transition: all 0.2s ease;
                                cursor: pointer;
                            }

                            .fade-in-up {
                                opacity: 0;
                                transform: translateY(30px);
                                transition: all 0.6s ease;
                            }
                        `;

                        // إضافة الأنماط
                        const styleSheet = document.createElement('style');
                        styleSheet.textContent = simpleStyles;
                        document.head.appendChild(styleSheet);

                        // تهيئة كل شيء عند تحميل الصفحة
                        document.addEventListener('DOMContentLoaded', function() {
                            console.log('🚀 تهيئة لوحة الإحصائيات...');

                            setTimeout(() => {
                                createStageChart();
                                initScrollEffects();
                                initCardHoverEffects();
                                initTabEffects();
                                autoUpdateStats();

                                console.log('✅ تم تحميل المكونات بنجاح!');

                                setTimeout(() => {
                                    showUpdateNotification();
                                }, 1000);
                            }, 500);
                        });

                        // إضافة اختصارات لوحة المفاتيح البسيطة
                        document.addEventListener('keydown', function(e) {
                            if (e.altKey) {
                                switch(e.key) {
                                    case '1':
                                        document.getElementById('stages-tab')?.click();
                                        break;
                                    case '2':
                                        document.getElementById('departments-tab')?.click();
                                        break;
                                    case '3':
                                        document.getElementById('assistants-tab')?.click();
                                        break;
                                    case '4':
                                        document.getElementById('performance-tab')?.click();
                                        break;
                                }
                            }
                        });
                                // نهاية انشاء الرسم البياني الدائري لمراحل المعالجة
                                                        document.addEventListener('DOMContentLoaded', function() {
                                    // تفعيل Bootstrap tooltips للإحصائيات المهمة فقط
                                    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                                    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                                        return new bootstrap.Tooltip(tooltipTriggerEl, {
                                            html: true,
                                            trigger: 'hover',
                                            delay: { show: 500, hide: 100 }
                                        });
                                    });
                                });

                                //الطلبات المعلقة

        @functions {
            // أرقام الطلبات دالة محدثة لتحديد لون البادج بناءً على أيام الانتظار (المعايير المشددة)
            private string GetOrderBadgeClass(int pendingDays)
            {
                if (pendingDays > 7) return "bg-danger";     // أحمر: عاجل (+7 أيام)
                if (pendingDays > 5) return "bg-warning";    // أصفر: متأخر (+5 أيام)
                if (pendingDays > 3) return "bg-info";       // أزرق: حرج (+3 أيام)
                return "bg-secondary";                       // رمادي: عادي
            }

            // دالة محدثة لتحديد حالة الطلب بناءً على المعايير المشددة
            private string GetUrgencyLevel(double avgDays)
            {
                if (avgDays > 5) return "عاجل جداً";
                if (avgDays > 3) return "حرج";
                if (avgDays > 1) return "متأخر";
                return "عادي";
            }

            // دالة محدثة لتحديد كلاس الصف بناءً على المعايير المشددة
            private string GetRowClass(double avgDays)
            {
                if (avgDays > 5) return "table-danger";
                if (avgDays > 3) return "table-warning";
                if (avgDays > 1) return "table-light";
                return "";
            }

            // الاحتفاظ بالدوال الأصلية للأولوية المركبة (إذا كانت لا تزال مستخدمة)
            private string GetPriorityBadgeClass(double score, double maxScore)
            {
                double percentage = (score / maxScore) * 100;
                if (percentage >= 80) return "bg-danger";
                if (percentage >= 60) return "bg-warning";
                if (percentage >= 40) return "bg-info";
                if (percentage >= 20) return "bg-primary";
                return "bg-success";
            }
                }
                // 🎯 المرحلة الثانية الجزء الثاني : الطلبات المعلقة
                @functions {
            private string GetPendingText(int count)
            {
                if (count == 0) return "لا توجد طلبات";
                if (count == 1) return "طلب معلق";
                if (count == 2) return "طلبين معلقين";
                if (count <= 10) return $"{count} طلبات معلقة";
                return $"{count} طلب معلق";
            }
                }

               // <!-- JavaScript للرسم البياني الشهري -->

                document.addEventListener('DOMContentLoaded', function() {
                    // بناء البيانات مباشرة من Razor مع ترميز صحيح للنص العربي
                    const monthlyData = [
                        @foreach (var month in Model.MonthlyStatistics.MonthlyBreakdown)
        {
            <text>{
                                        month: '@Html.Raw(month.MonthName)',
                                        requests: @month.RequestCount,
                                        completed: @month.CompletedCount,
                                        pending: @month.PendingCount,
                                        cancelled: @month.CancelledCount
                                    },</text>
        }
                    ];

            if (monthlyData && monthlyData.length > 0) {
                const ctx = document.getElementById('monthlyChart').getContext('2d');

                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: monthlyData.map(m => m.month),
                        datasets: [{
                            label: 'إجمالي الطلبات',
                            data: monthlyData.map(m => m.requests),
                            borderColor: '#007bff',
                            backgroundColor: 'rgba(0, 123, 255, 0.1)',
                            tension: 0.4,
                            fill: true
                        }, {
                            label: 'المكتملة',
                            data: monthlyData.map(m => m.completed),
                            borderColor: '#28a745',
                            backgroundColor: 'rgba(40, 167, 69, 0.1)',
                            tension: 0.4
                        }, {
                            label: 'المعلقة',
                            data: monthlyData.map(m => m.pending),
                            borderColor: '#ffc107',
                            backgroundColor: 'rgba(255, 193, 7, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: true,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    usePointStyle: true,
                                    padding: 15
                                }
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 1
                                }
                            }
                        }
                    }
                });
            }
        });
                // <!-- JavaScript للرسم البياني الشهري -->
                       document.addEventListener('DOMContentLoaded', function() {
                    // بيانات بسيطة
                    const simpleOrderTypesData = [
        @foreach (var orderType in Model.OrderTypesStatistics)
        {
            <text>{
                                        type: '@Html.Raw(orderType.TransferType)',
                                        count: @orderType.Count,
                                        percentage: @orderType.Percentage.ToString("F1", System.Globalization.CultureInfo.InvariantCulture)
                                    },</text>
        }
                    ];

                    if (simpleOrderTypesData && simpleOrderTypesData.length > 0) {
                        const ctx = document.getElementById('simpleOrderTypesChart').getContext('2d');

                        new Chart(ctx, {
                            type: 'pie',
                            data: {
                                labels: simpleOrderTypesData.map(t => `${t.type} (${t.percentage}%)`),
                                datasets: [{
                                    data: simpleOrderTypesData.map(t => t.count),
                                    backgroundColor: [
                                        '#007bff', '#28a745', '#ffc107', '#dc3545',
                                        '#6c757d', '#17a2b8', '#fd7e14', '#6f42c1'
                                    ],
                                    borderColor: '#fff',
                                    borderWidth: 2
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: true,
                                plugins: {
                                    legend: {
                                        position: 'bottom',
                                        labels: {
                                            usePointStyle: true,
                                            padding: 10
                                        }
                                    }
                                }
                            }
                        });
                    }
                });

    </script>
    <!-- 📋 JavaScript للفلترة -->
    @section Scripts {
        <script>
            document.addEventListener('DOMContentLoaded', function () {
                const filterButtons = document.querySelectorAll('[data-filter]');
                const tableRows = document.querySelectorAll('#unifiedSupervisorsTable tbody tr');

                filterButtons.forEach(button => {
                    button.addEventListener('click', function () {
                        const filter = this.getAttribute('data-filter');

                        // إزالة النشط من جميع الأزرار
                        filterButtons.forEach(btn => btn.classList.remove('active'));
                        this.classList.add('active');

                        // تطبيق الفلتر
                        tableRows.forEach(row => {
                            if (filter === 'all') {
                                row.style.display = '';
                            } else {
                                const category = row.getAttribute('data-category');
                                row.style.display = category === filter ? '' : 'none';
                            }
                        });
                    });
                });
            });
        </script>

        <!-- دوال C# للألوان والأيقونات -->
        @functions {
        private string GetStatusClass(string statusName)
        {
            return statusName switch
            {
                "مقبول" => "success",
                "مدير الموارد البشرية" => "primary",
                "المشرفون" => "dark",
                "منسق الموارد البشرية" => "danger",
                "مدير القسم" => "secondary",
                "مساعد المدير للخدمات الطبية (A1)" => "primary",
                "مساعد المدير لخدمات التمريض (A2)" => "info",
                "مساعد المدير للخدمات الإدارية والتشغيل (A3)" => "warning",
                "مساعد المدير للموارد البشرية (A4)" => "success",
                "يتطلب إجراء من المشرف" => "info",
                "يتطلب إجراء" => "info",
                "معاد من المنسق" => "secondary",
                "معاد من المشرف" => "secondary",
                "معاد من مساعد المدير" => "secondary",
                "معاد من المدير" => "secondary",
                "ملغي من مدير القسم" => "danger",
                "ملغي من مساعد المدير" => "danger",
                "ملغي من المدير" => "danger",
                _ => "primary"
            };
        }

        private string GetStatusIcon(string statusName)
        {
            return statusName switch
            {
                "مقبول" => "fas fa-check-circle",
                "مدير الموارد البشرية" => "fas fa-user-shield",
                "المشرفون" => "fas fa-user-check",
                "منسق الموارد البشرية" => "fas fa-user-cog",
                "مدير القسم" => "fas fa-user-tie",
                "مساعد المدير للخدمات الطبية (A1)" => "fas fa-user-md",
                "مساعد المدير لخدمات التمريض (A2)" => "fas fa-user-nurse",
                "مساعد المدير للخدمات الإدارية والتشغيل (A3)" => "fas fa-cogs",
                "مساعد المدير للموارد البشرية (A4)" => "fas fa-users",
                "يتطلب إجراء من المشرف" => "fas fa-exclamation-triangle",
                "يتطلب إجراء" => "fas fa-exclamation-triangle",
                "معاد من المنسق" => "fas fa-undo-alt",
                "معاد من المشرف" => "fas fa-undo-alt",
                "معاد من مساعد المدير" => "fas fa-undo-alt",
                "معاد من المدير" => "fas fa-undo-alt",
                "ملغي من مدير القسم" => "fas fa-times-circle",
                "ملغي من مساعد المدير" => "fas fa-times-circle",
                "ملغي من المدير" => "fas fa-times-circle",
                _ => "fas fa-flag"
            };
        }
    }

    <!-- JavaScript للرسم البياني الدائري -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // بيانات حالات الطلبات
            const orderStatusData = [
                @foreach (var status in Model.OrderStatusStatistics)
            {
                <text>{
                                        status: '@Html.Raw(status.TransferType)',
                                        count: @status.Count,
                                        percentage: @status.Percentage.ToString("F1", System.Globalization.CultureInfo.InvariantCulture),
                                        thisMonth: @status.ThisMonthCount,
                                        thisWeek: @status.ThisWeekCount
                                    },</text>
            }
                    ];

                    if (orderStatusData && orderStatusData.length > 0) {
                        const ctx = document.getElementById('orderStatusChart').getContext('2d');

                        // ألوان مخصصة للحالات
                        const statusColors = [
                            '#28a745', // أخضر - مقبول
                            '#ffc107', // أصفر - تحت التنفيذ
                            '#17a2b8', // أزرق فاتح - يتطلب إجراء
                            '#6c757d', // رمادي - معاد
                            '#dc3545', // أحمر - ملغي
                            '#007bff', // أزرق
                            '#fd7e14', // برتقالي
                            '#6f42c1', // بنفسجي
                            '#e83e8c', // وردي
                            '#20c997'  // أخضر فاتح
                        ];

                        new Chart(ctx, {
                            type: 'doughnut',
                            data: {
                                labels: orderStatusData.map(s => `${s.status} (${s.percentage}%)`),
                                datasets: [{
                                    data: orderStatusData.map(s => s.count),
                                    backgroundColor: statusColors.slice(0, orderStatusData.length),
                                    borderColor: '#fff',
                                    borderWidth: 3,
                                    hoverBorderWidth: 4
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: true,
                                cutout: '60%',
                                plugins: {
                                    legend: {
                                        position: 'bottom',
                                        labels: {
                                            usePointStyle: true,
                                            padding: 15,
                                            font: {
                                                family: 'Cairo, sans-serif',
                                                size: 11
                                            },
                                            generateLabels: function(chart) {
                                                const original = Chart.defaults.plugins.legend.labels.generateLabels;
                                                const labels = original.call(this, chart);

                                                labels.forEach((label, index) => {
                                                    const statusData = orderStatusData[index];
                                                    label.text = `${statusData.status} (${statusData.count})`;
                                                });

                                                return labels;
                                            }
                                        }
                                    },
                                    tooltip: {
                                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                        titleColor: '#fff',
                                        bodyColor: '#fff',
                                        borderColor: '#007bff',
                                        borderWidth: 1,
                                        cornerRadius: 8,
                                        callbacks: {
                                            label: function(context) {
                                                const statusData = orderStatusData[context.dataIndex];
                                                return [
                                                    `${context.label}: ${context.parsed}`,
                                                    `النسبة: ${statusData.percentage}%`,
                                                    `هذا الشهر: ${statusData.thisMonth}`,
                                                    `هذا الأسبوع: ${statusData.thisWeek}`
                                                ];
                                            }
                                        }
                                    }
                                },
                                animation: {
                                    animateRotate: true,
                                    animateScale: true,
                                    duration: 1200,
                                    easing: 'easeOutQuart'
                                }
                            }
                        });
                    }
                });
        </script>
    }
</body>
</html>