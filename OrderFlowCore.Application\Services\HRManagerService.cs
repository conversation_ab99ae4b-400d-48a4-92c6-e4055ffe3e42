using OrderFlowCore.Application.Common;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces.Repositories;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Core.Models;
using System.IO.Compression;
using System.IO;
using System.Text;
using OrderFlowCore.Application.Helper;

namespace OrderFlowCore.Application.Services;

public class HRManagerService : IHRManagerService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly ISupervisorService _supervisorService;
    private readonly IFileService _fileService;

    public HRManagerService(IUnitOfWork unitOfWork, ISupervisorService supervisorService, IFileService fileService)
    {
        _unitOfWork = unitOfWork;
        _supervisorService = supervisorService;
        _fileService = fileService;
    }

    public async Task<ServiceResult<List<OrderSummaryDto>>> GetHRManagerOrdersAsync()
    {
        try
        {
            var orders = await _unitOfWork.Orders.GetHRMangerPendingOrdersAsync();

            var orderSummaries = orders.Select(o => {
                var dto = new OrderSummaryDto
                {
                    Id = o.Id,
                    EmployeeName = o.EmployeeName ?? "",
                    Department = o.Department ?? "",
                    CreatedAt = o.CreatedAt,
                    OrderStatus = o.OrderStatus
                };

                // تحديد نوع الطلب بناءً على ConfirmedByCoordinator
                if (o.OrderStatus == OrderStatus.D)
                {
                    if (!string.IsNullOrEmpty(o.ConfirmedByCoordinator))
                    {
                        if (o.ConfirmedByCoordinator.Contains("تم التحويل مباشر للمدير"))
                        {
                            dto.AdditionalInfo = "محول مباشرة";
                        }
                        else if (o.ConfirmedByCoordinator.Contains("اعتماد بواسطة"))
                        {
                            dto.AdditionalInfo = "جديد";
                        }
                    }
                    else
                    {
                        dto.AdditionalInfo = "جديد";
                    }
                }
                else if (o.OrderStatus == OrderStatus.ReturnedByManager)
                {
                    dto.AdditionalInfo = "مُعاد من المدير";
                }

                return dto;
            }).OrderByDescending(o => o.Id).ToList();

            return ServiceResult<List<OrderSummaryDto>>.Success(orderSummaries);
        }
        catch (Exception ex)
        {
            return ServiceResult<List<OrderSummaryDto>>.Failure($"حدث خطأ أثناء تحميل الطلبات: {ex.Message}");
        }
    }

    public async Task<ServiceResult> ApproveOrderAsync(int orderId, string userName)
    {
        try
        {
            var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
            if (order == null)
            {
                return ServiceResult.Failure("لم يتم العثور على الطلب");
            }

            if (order.OrderStatus != OrderStatus.D)
            {
                return ServiceResult.Failure("الطلب ليس في الحالة المناسبة للاعتماد");
            }

            // Order was already assigned, update with confirmation
            var assignmentDate = ExtractAssignmentDate(order.HumanResourcesManager);
            order.HumanResourcesManager = OrderHelper.ConfirmedByWithAssignment(userName, assignmentDate);

            // Update order status and HR manager approval
            order.OrderStatus = OrderStatus.Accepted;

            order.ReasonForCancellation = null;

            await _unitOfWork.Orders.UpdateAsync(order);
            await _unitOfWork.SaveChangesAsync();

            return ServiceResult.Success("تم قبول الطلب بنجاح");
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure($"حدث خطأ أثناء معالجة الطلب: {ex.Message}");
        }
    }

    public async Task<ServiceResult> RejectOrderAsync(int orderId, string rejectReason, string userName)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(rejectReason))
            {
                return ServiceResult.Failure("يرجى إدخال سبب الإلغاء");
            }

            var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
            if (order == null)
            {
                return ServiceResult.Failure("لم يتم العثور على الطلب");
            }

            // Order was already assigned, update with rejection
            var assignmentDate = ExtractAssignmentDate(order.HumanResourcesManager);
            order.HumanResourcesManager = OrderHelper.RejectedByWithAssignment(userName, assignmentDate);

            // Update order status
            order.OrderStatus = OrderStatus.CancelledByManager;
            order.ReasonForCancellation = rejectReason;

            await _unitOfWork.Orders.UpdateAsync(order);
            await _unitOfWork.SaveChangesAsync();

            return ServiceResult.Success("تم إلغاء الطلب بنجاح");
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure($"حدث خطأ أثناء معالجة الطلب: {ex.Message}");
        }
    }

    public async Task<ServiceResult> ReturnOrderAsync(int orderId, string returnReason, string userName)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(returnReason))
            {
                return ServiceResult.Failure("يرجى إدخال سبب الإعادة");
            }

            var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
            if (order == null)
            {
                return ServiceResult.Failure("لم يتم العثور على الطلب");
            }

            // Order was already assigned, update with return
            var assignmentDate = ExtractAssignmentDate(order.HumanResourcesManager);
            order.HumanResourcesManager = OrderHelper.ReturnedByWithAssignment(userName, assignmentDate);

            _supervisorService.ClearAllStatuses(order);
            // Update order status
            order.OrderStatus = OrderStatus.ReturnedByManager;
            order.ReasonForCancellation = returnReason;

            await _unitOfWork.Orders.UpdateAsync(order);
            await _unitOfWork.SaveChangesAsync();

            return ServiceResult.Success("تم إعادة الطلب إلى المنسق بنجاح");
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure($"حدث خطأ أثناء معالجة الطلب: {ex.Message}");
        }
    }

    public async Task<ServiceResult<List<OrderSummaryDto>>> GetOrdersForStatusChangeAsync(string searchTerm = "", string filter = "today")
    {
        try
        {
            var orders = await _unitOfWork.Orders.GetAllAsync();

            // Apply date filter
            var filteredOrders = ApplyDateFilter(orders, filter);

            // Apply search filter
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                filteredOrders = filteredOrders.Where(o =>
                    o.Id.ToString().Contains(searchTerm) ||
                    (o.EmployeeName != null && o.EmployeeName.Contains(searchTerm))
                ).ToList();
            }

            var orderSummaries = filteredOrders.Select(o => new OrderSummaryDto
            {
                Id = o.Id,
                EmployeeName = o.EmployeeName ?? "",
                Department = o.Department ?? "",
                CreatedAt = o.CreatedAt,
                OrderStatus = o.OrderStatus
            }).OrderByDescending(o => o.Id).ToList();

            return ServiceResult<List<OrderSummaryDto>>.Success(orderSummaries);
        }
        catch (Exception ex)
        {
            return ServiceResult<List<OrderSummaryDto>>.Failure($"حدث خطأ أثناء تحميل الطلبات: {ex.Message}");
        }
    }

    public async Task<ServiceResult<List<DropdownItemDto>>> GetAvailableStatusesAsync()
    {

        var statuses = new List<DropdownItemDto>
            {
                new() { Value = "", Text = "-- اختر الحالة الجديدة --" },
                new() { Value = OrderStatus.DM.ToDisplayString(), Text = OrderStatus.DM.ToDisplayString() },
                new() { Value = OrderStatus.A1.ToDisplayString(), Text = OrderStatus.A1.ToDisplayString() },
                new() { Value = OrderStatus.A2.ToDisplayString(), Text = OrderStatus.A2.ToDisplayString() },
                new() { Value = OrderStatus.A3.ToDisplayString(), Text = OrderStatus.A3.ToDisplayString() },
                new() { Value = OrderStatus.A4.ToDisplayString(), Text = OrderStatus.A4.ToDisplayString() },
                new() { Value = OrderStatus.B.ToDisplayString(), Text = OrderStatus.B.ToDisplayString() },
                new() { Value = OrderStatus.D.ToDisplayString(), Text = OrderStatus.D.ToDisplayString() },
                new() { Value = OrderStatus.Accepted.ToDisplayString(), Text = OrderStatus.Accepted.ToDisplayString() },
                new() { Value = OrderStatus.ReturnedByManager.ToDisplayString(), Text = OrderStatus.ReturnedByManager.ToDisplayString() },
                new() { Value = OrderStatus.CancelledByManager.ToDisplayString(), Text = OrderStatus.CancelledByManager.ToDisplayString() }
            };

        return ServiceResult<List<DropdownItemDto>>.Success(statuses);
    }

    public async Task<ServiceResult> ChangeOrderStatusAsync(int orderId, string newStatus, string notes, string userName)
    {
        try
        {
            var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
            if (order == null)
            {
                return ServiceResult.Failure("لم يتم العثور على الطلب المحدد");
            }

            // Parse the new status
            var orderStatus = ParseOrderStatus(newStatus);
            if (orderStatus == null)
            {
                return ServiceResult.Failure("حالة الطلب غير صحيحة");
            }

            // Update order status
            order.OrderStatus = orderStatus.Value;
            order.HumanResourcesManager = $"{DateTime.Now:yyyy-MM-dd} - تم تغيير الحالة إلى {newStatus} بواسطة {userName}";
            order.ReasonForCancellation = notes;

            await _unitOfWork.Orders.UpdateAsync(order);
            await _unitOfWork.SaveChangesAsync();

            return ServiceResult.Success("تم تحديث حالة الطلب بنجاح");
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure($"حدث خطأ أثناء تحديث حالة الطلب: {ex.Message}");
        }
    }

    public async Task<ServiceResult> ChangeOrderStatusAndDepartmentAsync(int orderId, string newStatus, string newDepartment, string notes, string userName)
    {
        try
        {
            var order = await _unitOfWork.Orders.GetByIdAsync(orderId);
            if (order == null)
            {
                return ServiceResult.Failure("لم يتم العثور على الطلب المحدد");
            }

            var changes = new List<string>();
            var oldStatus = order.OrderStatus.ToString();
            var oldDepartment = order.Department;

            // Update status if provided
            if (!string.IsNullOrWhiteSpace(newStatus))
            {
                var orderStatus = ParseOrderStatus(newStatus);
                if (orderStatus == null)
                {
                    return ServiceResult.Failure("حالة الطلب غير صحيحة");
                }
                order.OrderStatus = orderStatus.Value;
                changes.Add($"الحالة من {oldStatus} إلى {newStatus}");
            }

            // Update department if provided
            if (!string.IsNullOrWhiteSpace(newDepartment))
            {
                order.Department = newDepartment;
                changes.Add($"القسم من {oldDepartment} إلى {newDepartment}");
            }

            // Update HR manager notes with changes
            var changeDescription = string.Join(" و ", changes);
            order.HumanResourcesManager = $"{DateTime.Now:yyyy-MM-dd} - تم تغيير {changeDescription} بواسطة {userName}";

            if (!string.IsNullOrWhiteSpace(notes))
            {
                order.ReasonForCancellation = notes;
            }

            await _unitOfWork.Orders.UpdateAsync(order);
            await _unitOfWork.SaveChangesAsync();

            return ServiceResult.Success($"تم تحديث {changeDescription} بنجاح");
        }
        catch (Exception ex)
        {
            return ServiceResult.Failure($"حدث خطأ أثناء تحديث الطلب: {ex.Message}");
        }
    }

    private List<OrdersTable> ApplyDateFilter(IEnumerable<OrdersTable> orders, string filter)
    {
        var now = DateTime.Now;
        var today = now.Date;

        return filter switch
        {
            "today" => orders.Where(o => o.CreatedAt.Date == today).ToList(),
            "week" => orders.Where(o => o.CreatedAt.Date >= today.AddDays(-7)).ToList(),
            "month" => orders.Where(o => o.CreatedAt.Date >= today.AddMonths(-1)).ToList(),
            "two_months" => orders.Where(o => o.CreatedAt.Date >= today.AddMonths(-2)).ToList(),
            "all" => orders.ToList(),
            _ => orders.Where(o => o.CreatedAt.Date == today).ToList()
        };
    }

    private OrderStatus? ParseOrderStatus(string statusString)
    {
        return statusString switch
        {
            "(DM)" => OrderStatus.DM,
            "(A1)" => OrderStatus.A1,
            "(A2)" => OrderStatus.A2,
            "(A3)" => OrderStatus.A3,
            "(A4)" => OrderStatus.A4,
            "(B)" => OrderStatus.B,
            "(C)" => OrderStatus.C,
            "(D)" => OrderStatus.D,
            "مقبول" => OrderStatus.Accepted,
            "أُعيد بواسطة المدير" => OrderStatus.ReturnedByManager,
            "تم الإلغاء من قبل المدير" => OrderStatus.CancelledByManager,
            _ => null
        };
    }

    private string ExtractAssignmentDate(string status)
    {
        if (string.IsNullOrEmpty(status))
            return DateTime.Now.ToString("yyyy-MM-dd");

        var parts = status.Split(" | ");
        if (parts.Length >= 3)
        {
            // Format: "assignmentDate | action | outDate"
            return parts[0];
        }
        else if (parts.Length >= 2)
        {
            // Format: "date | action" (old format)
            return parts[0];
        }

        return DateTime.Now.ToString("yyyy-MM-dd");
    }
}
