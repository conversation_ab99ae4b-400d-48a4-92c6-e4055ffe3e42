
using OrderFlowCore.Application.Common;
using OrderFlowCore.Core.Entities;
using OrderFlowCore.Core.Models;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Interfaces.Services
{
    public interface ICertificateService
    {
        Task<ServiceResult<int>> CreateAsync(Certificate certificate);
        Task<ServiceResult> ConfirmByDirectManagerAsync(int id, string userName);
        Task<ServiceResult> ConfirmByCoordinatorAsync(int id, string userName, string details);
        Task<ServiceResult> ConfirmByHRManagerAsync(int id, string userName);
        Task<ServiceResult> CancelAsync(int id, string userName, string reason);
        Task<ServiceResult<Certificate>> GetByIdAsync(int id);
        Task<ServiceResult<List<Certificate>>> GetPendingAsync(CertificateStatus status);
    }
}



