using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Application.Interfaces;
using OrderFlowCore.Application.Interfaces.Services;
using OrderFlowCore.Web.ViewModels;
using Microsoft.Extensions.Logging;
using OrderFlowCore.Web.Attributes;
using OrderFlowCore.Core.Entities;
using System.Security.Claims;
using OrderFlowCore.Web.Extentions;

namespace OrderFlowCore.Web.Controllers
{
    [AuthorizeRole(UserRole.DirectManager, UserRole.AssistantManager, UserRole.Coordinator, UserRole.Supervisor, UserRole.Manager, UserRole.Admin)]
    public class OrdersManagementController : Controller
    {
        private readonly IOrdersManagementService _ordersManagementService;
        private readonly IAutomaticDeletionSettingsService _automaticDeletionSettingsService;
        private readonly ILogger<OrdersManagementController> _logger;

        public OrdersManagementController(
            IOrdersManagementService ordersManagementService,
            IAutomaticDeletionSettingsService automaticDeletionSettingsService,
            ILogger<OrdersManagementController> logger)
        {
            _ordersManagementService = ordersManagementService;
            _automaticDeletionSettingsService = automaticDeletionSettingsService;
            _logger = logger;
        }

        [HttpGet]
        public async Task<IActionResult> Index(int page = 1, int pageSize = 50, string selectedStatus = "All", string selectedDepartment = "All", DateTime? fromDate = null, DateTime? toDate = null, string civilRecord = "")
        {
            var viewModel = new OrdersManagementViewModel
            {
                CurrentPage = page,
                PageSize = pageSize,
                SelectedStatus = selectedStatus,
                SelectedDepartment = selectedDepartment,
                FromDate = fromDate,
                ToDate = toDate,
                CivilRecord = civilRecord ?? string.Empty,
            };

            await LoadOrdersAsync(viewModel);
            return View(viewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> FilterOrders(OrdersManagementViewModel model)
        {
            // Reset to first page when applying new filters
            model.CurrentPage = 1;

            await LoadOrdersAsync(model);

            if (model.TotalOrdersCount > 0)
            {
                model.SuccessMessage = $"تم العثور على {model.TotalOrdersCount} طلب";
            }

            return View("Index", model);
        }

        private async Task LoadOrdersAsync(OrdersManagementViewModel viewModel)
        {
            try
            {
                viewModel.IsAdmin = IsUserAdmin();
                await LoadDropdownOptionsAsync(viewModel);
                await LoadAutomaticDeletionSettingsAsync(viewModel);

                var filterDto = viewModel.GetFilterDto();
                var ordersResult = await _ordersManagementService.GetFilteredOrdersAsync(filterDto);

                if (ordersResult.IsSuccess)
                {
                    viewModel.Orders = ordersResult.Data;
                    viewModel.TotalOrdersCount = ordersResult.TotalCount;
                }
                else
                {
                    viewModel.ErrorMessage = ordersResult.Message;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحميل الطلبات");
                viewModel.ErrorMessage = "حدث خطأ أثناء تحميل الطلبات";
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ClearFilters()
        {
            return RedirectToAction(nameof(Index));
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ExportOrders(OrdersManagementViewModel model)
        {
            try
            {
                if (!IsUserAdmin())
                {
                    return Forbid();
                }

                var filterDto = model.GetFilterDto();
                var exportResult = await _ordersManagementService.ExportOrdersToExcelAsync(filterDto);

                if (exportResult.IsSuccess)
                {
                    var fileName = $"الطلبات_{DateTime.Now:yyyy-MM-dd_HH-mm}.xlsx";
                    return File(exportResult.Data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                }
                else
                {
                    TempData["ErrorMessage"] = exportResult.Message;
                    return RedirectToAction(nameof(Index));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير الطلبات");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تصدير الطلبات";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ImportOrders(IFormFile excelFile)
        {
            try
            {
                if (!IsUserAdmin())
                {
                    return Json(new { success = false, message = "غير مصرح لك بهذا الإجراء" });
                }

                if (excelFile == null || excelFile.Length == 0)
                {
                    return Json(new { success = false, message = "يرجى اختيار ملف Excel" });
                }

                if (!excelFile.FileName.EndsWith(".xlsx") && !excelFile.FileName.EndsWith(".xls"))
                {
                    return Json(new { success = false, message = "يرجى اختيار ملف Excel صحيح (.xlsx أو .xls)" });
                }

                using var stream = excelFile.OpenReadStream();
                var result = await _ordersManagementService.ImportOrdersFromExcelAsync(stream);

                if (result.IsSuccess)
                {
                    return Json(new
                    {
                        success = true,
                        message = result.Message,
                        importedCount = result.Data,
                        errors = result.Errors
                    });
                }
                else
                {
                    return Json(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في استيراد الطلبات");
                return Json(new { success = false, message = "حدث خطأ أثناء استيراد الطلبات" });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ExportFilteredResults(OrdersManagementViewModel model)
        {
            try
            {
                if (!IsUserAdmin())
                {
                    return Forbid();
                }

                var filterDto = model.GetFilterDto();
                var exportResult = await _ordersManagementService.ExportFilteredResultsToExcelAsync(filterDto);

                if (exportResult.IsSuccess)
                {
                    var fileName = $"النتائج_المفلترة_{DateTime.Now:yyyy-MM-dd_HH-mm}.xlsx";
                    return File(exportResult.Data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                }
                else
                {
                    TempData["ErrorMessage"] = exportResult.Message;
                    return RedirectToAction(nameof(Index));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير النتائج المفلترة");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تصدير النتائج";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ExportDetailedStatistics()
        {
            try
            {
                if (!IsUserAdmin())
                {
                    return Forbid();
                }

                var exportResult = await _ordersManagementService.ExportDetailedStatisticsToExcelAsync();

                if (exportResult.IsSuccess)
                {
                    var fileName = $"التقارير_المفصلة_{DateTime.Now:yyyy-MM-dd_HH-mm}.xlsx";
                    return File(exportResult.Data, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
                }
                else
                {
                    TempData["ErrorMessage"] = exportResult.Message;
                    return RedirectToAction(nameof(Index));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تصدير التقارير المفصلة");
                TempData["ErrorMessage"] = "حدث خطأ أثناء تصدير التقارير";
                return RedirectToAction(nameof(Index));
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteOldOrders(OrdersManagementViewModel model)
        {
            try
            {
                if (!IsUserAdmin())
                {
                    return Json(new { success = false, message = "غير مصرح لك بهذا الإجراء" });
                }

                var userName = User.Identity?.Name ?? "مجهول";
                var deleteDto = model.GetDeleteOldOrdersDto();
                var result = await _ordersManagementService.DeleteOldOrdersAsync(deleteDto, userName);

                return Json(new { success = result.IsSuccess, message = result.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الطلبات القديمة");
                return Json(new { success = false, message = "حدث خطأ أثناء حذف الطلبات القديمة" });
            }
        }
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteOrderAttachments(OrdersManagementViewModel model)
        {
            try
            {
                if (!IsUserAdmin())
                {
                    return Json(new { success = false, message = "غير مصرح لك بهذا الإجراء" });
                }

                var userName = User.Identity?.Name ?? "مجهول";
                var deleteDto = model.GetDeleteOldOrdersDto();
                var result = await _ordersManagementService.DeleteOrderAttachmentsAsync(deleteDto, userName);

                return Json(new { success = result.IsSuccess, message = result.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف مرفقات الطلبات");
                return Json(new { success = false, message = "حدث خطأ أثناء حذف مرفقات الطلبات" });
            }
        }
        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> DeleteSpecificOrder(int orderId)
        {
            try
            {
                if (!IsUserAdmin())
                {
                    return Json(new { success = false, message = "غير مصرح لك بهذا الإجراء" });
                }

                var userName = User.Identity?.Name ?? "مجهول";
                var result = await _ordersManagementService.DeleteSpecificOrderAsync(orderId, userName);

                return Json(new { success = result.IsSuccess, message = result.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في حذف الطلب {OrderId}", orderId);
                return Json(new { success = false, message = "حدث خطأ أثناء حذف الطلب" });
            }
        }

        private async Task LoadDropdownOptionsAsync(OrdersManagementViewModel viewModel)
        {
            // Load status options
            var statusResult = await _ordersManagementService.GetOrderStatusesAsync();
            if (statusResult.IsSuccess)
            {
                viewModel.StatusOptions = statusResult.Data.Select(s => new SelectListItem
                {
                    Value = s.Value,
                    Text = s.Text
                }).ToList();
            }

            // Load department options
            var departmentResult = await _ordersManagementService.GetDepartmentsAsync();
            if (departmentResult.IsSuccess)
            {
                viewModel.DepartmentOptions = departmentResult.Data.Select(d => new SelectListItem
                {
                    Value = d.Value,
                    Text = d.Text
                }).ToList();
            }

            // Load order numbers for deletion
            var orderNumbersResult = await _ordersManagementService.GetOrderNumbersAsync();
            if (orderNumbersResult.IsSuccess)
            {
                viewModel.OrderNumbers = orderNumbersResult.Data.Select(o => new SelectListItem
                {
                    Value = o.Value,
                    Text = o.Text
                }).ToList();
            }
        }

        private async Task LoadAutomaticDeletionSettingsAsync(OrdersManagementViewModel viewModel)
        {
            if (IsUserAdmin())
            {
                var settingsResult = await _automaticDeletionSettingsService.GetAllSettingsAsync();
                viewModel.AutomaticDeletionSettings = settingsResult.Data;
            }

        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> UpdateAutomaticDeletionSetting(int id, bool isEnabled)
        {
            try
            {
                if (!IsUserAdmin())
                {
                    return Json(new { success = false, message = "غير مصرح لك بهذا الإجراء" });
                }

                var userName = User.Identity?.Name ?? "مجهول";
                var updateDto = new AutomaticDeletionSettingsUpdateDto
                {
                    Id = id,
                    IsEnabled = isEnabled,
                    LastModifiedBy = userName
                };

                var result = await _automaticDeletionSettingsService.UpdateSettingAsync(updateDto);
                return Json(new { success = result.IsSuccess, message = result.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث إعداد الحذف التلقائي {Id}", id);
                return Json(new { success = false, message = "حدث خطأ أثناء تحديث الإعداد" });
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public async Task<IActionResult> ExecuteAutomaticDeletion()
        {
            try
            {
                if (!IsUserAdmin())
                {
                    return Json(new { success = false, message = "غير مصرح لك بهذا الإجراء" });
                }

                var result = await _automaticDeletionSettingsService.ExecuteAutomaticDeletionAsync();
                return Json(new { success = result.IsSuccess, message = result.Message });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تنفيذ الحذف التلقائي");
                return Json(new { success = false, message = "حدث خطأ أثناء تنفيذ الحذف التلقائي" });
            }
        }

        private bool IsUserAdmin()
        {
            // Check if user has admin role - adjust based on your role system
            var userRole = User.GetUserRole();
            return userRole is UserRole.Admin;
        }
    }
}
