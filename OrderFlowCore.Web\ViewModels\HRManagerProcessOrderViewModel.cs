using Microsoft.AspNetCore.Mvc.Rendering;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace OrderFlowCore.Web.ViewModels
{
    public class HRManagerProcessOrderViewModel
    {
        public HRManagerProcessOrderViewModel()
        {
            OrderNumbers = new List<SelectListItem>();
        }

        // Main order processing section
        public List<SelectListItem> OrderNumbers { get; set; }
        public int SelectedOrderId { get; set; }

        [Display(Name = "سبب الإلغاء/الإعادة")]
        public string RejectReason { get; set; } = string.Empty;

    }
} 