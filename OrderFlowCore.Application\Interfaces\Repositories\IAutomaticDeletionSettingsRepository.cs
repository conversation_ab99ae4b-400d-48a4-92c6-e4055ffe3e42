using OrderFlowCore.Core.Entities;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace OrderFlowCore.Application.Interfaces.Repositories
{
    public interface IAutomaticDeletionSettingsRepository
    {
        Task<int> AddAsync(AutomaticDeletionSettings setting);
        Task<int> UpdateAsync(AutomaticDeletionSettings setting);
        Task<AutomaticDeletionSettings> GetByIdAsync(int id);
        Task<List<AutomaticDeletionSettings>> GetAllAsync();
        Task DeleteAsync(AutomaticDeletionSettings setting);
        Task<IEnumerable<AutomaticDeletionSettings>> GetActiveSettingsAsync();
    }
}
