using Microsoft.AspNetCore.Mvc.Rendering;
using OrderFlowCore.Application.DTOs;
using OrderFlowCore.Core.Entities;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;

namespace OrderFlowCore.Web.ViewModels
{
    public class HRCoordinatorViewModel
    {
        public HRCoordinatorViewModel()
        {
            OrderNumbers = new List<SelectListItem>();
        }

        // Order Selection
        public List<SelectListItem> OrderNumbers { get; set; }
        public int SelectedOrderId { get; set; }
        public Dictionary<string, List<string>> PathsConfiguration { get; set; } = new();

    }
}
