@model OrderFlowCore.Web.ViewModels.CertificatePrintViewModel
@{
    ViewData["Title"] = "طباعة الشهادات";
    Layout = "_DashboardLayout";
}

<section class="content p-3">
    <div class="container-fluid">
        <div class="card">
            <div class="card-header d-flex align-items-center justify-content-between">
                <h5 class="mb-0">طباعة الشهادات</h5>
                <form method="get" class="d-flex" asp-action="Index">
                    <input name="searchTerm" value="@Model.SearchTerm" class="form-control form-control-sm me-2" placeholder="بحث" />
                    <select name="filter" class="form-select form-select-sm me-2">
                        <option value="today" selected>اليوم</option>
                        <option value="all">الكل</option>
                    </select>
                    <button class="btn btn-sm btn-primary" type="submit">تصفية</button>
                </form>
            </div>
            <div class="card-body">
                @if (TempData["SuccessMessage"] != null)
                {
                    <div class="alert alert-info">@TempData["SuccessMessage"]</div>
                }

                <form asp-action="Print" method="post" id="printForm">
                    <div class="row g-3 align-items-end">
                        <div class="col-md-6">
                            <label class="form-label">اختر الشهادة</label>
                            <select class="form-select" name="certificateId" id="certificateSelect">
                                @foreach (var item in Model.Certificates)
                                {
                                    <option value="@item.Value">@item.Text</option>
                                }
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-success">طباعة</button>
                        </div>
                    </div>
                </form>

                <hr />
                <div id="details" class="mt-3"></div>
            </div>
        </div>
    </div>
</section>

@section Scripts {
<script>
    const select = document.getElementById('certificateSelect');
    const details = document.getElementById('details');
    function loadDetails() {
        const id = select.value;
        if (!id) return;
        fetch('@Url.Action("GetCertificateDetails")', {
            method: 'POST',
            headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
            body: 'certificateId=' + encodeURIComponent(id)
        }).then(r => r.json()).then(r => {
            if (!r.success || !r.data) { details.innerHTML = '<div class="alert alert-warning">لا توجد تفاصيل</div>'; return; }
            const c = r.data;
            details.innerHTML = `
                <div class="row g-3">
                    <div class="col-md-4"><strong>الاسم:</strong> ${c.fullName}</div>
                    <div class="col-md-4"><strong>القسم:</strong> ${c.department}</div>
                    <div class="col-md-4"><strong>الوظيفة:</strong> ${c.jobTitle}</div>
                    <div class="col-md-4"><strong>الجهة:</strong> ${c.certificateRecipient}</div>
                    <div class="col-md-4"><strong>تاريخ الإصدار:</strong> ${c.issueDate?.substring(0,10) ?? ''}</div>
                </div>`;
        });
    }
    select.addEventListener('change', loadDetails);
    loadDetails();
</script>
}




