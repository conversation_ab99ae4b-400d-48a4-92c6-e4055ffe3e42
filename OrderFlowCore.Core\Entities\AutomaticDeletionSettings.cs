using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace OrderFlowCore.Core.Entities
{
    [Table("AutomaticDeletionSettings")]
    public class AutomaticDeletionSettings
    {
        [Key]
        public int Id { get; set; }

        [Required]
        [StringLength(100)]
        public string SettingName { get; set; }

        [Required]
        public bool IsEnabled { get; set; }

        [Required]
        public int PeriodInMonths { get; set; }

        [StringLength(200)]
        public string Description { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime? LastModifiedAt { get; set; }

        [StringLength(255)]
        public string? LastModifiedBy { get; set; }

        [StringLength(255)]
        public string CreatedBy { get; set; }

        // Navigation properties
        public bool IsActive { get; set; } = true;
    }
}
